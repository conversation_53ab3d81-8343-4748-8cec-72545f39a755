{"name": "bank-reconciliation-ai", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "eslint"}, "dependencies": {"@google/generative-ai": "^0.24.1", "@hookform/resolvers": "^5.2.1", "@playwright/test": "^1.55.0", "@radix-ui/react-alert-dialog": "^1.1.15", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.8", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.13", "@radix-ui/react-tooltip": "^1.2.8", "@supabase/supabase-js": "^2.57.2", "@tanstack/react-query": "^5.87.1", "@tanstack/react-query-devtools": "^5.87.1", "@types/crypto-js": "^4.2.2", "@types/pdf-parse": "^1.1.5", "ai": "^5.0.34", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "crypto-js": "^4.2.0", "e2b": "^2.1.5", "form-data": "^4.0.4", "lucide-react": "^0.542.0", "next": "15.5.2", "next-themes": "^0.4.6", "node-fetch": "^3.3.2", "papaparse": "^5.5.3", "pdf-parse": "^1.1.1", "react": "19.1.0", "react-dom": "19.1.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.62.0", "sonner": "^2.0.7", "tailwind-merge": "^3.3.1", "unpdf": "^1.2.2", "xlsx": "^0.18.5", "zod": "^4.1.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/papaparse": "^5.3.16", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.5.2", "tailwindcss": "^4", "tw-animate-css": "^1.3.8", "typescript": "^5"}}