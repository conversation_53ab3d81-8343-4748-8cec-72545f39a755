/**
 * A simple test to verify the AI explanation update fix works
 */

// Run this in browser console after processing files to confirm the fix

function checkAIExplanationsInLocalStorage() {
  try {
    // Get the stored results
    const storedData = localStorage.getItem('bank-reconciliation-results');
    if (!storedData) {
      console.error('No reconciliation results found in localStorage');
      return;
    }

    const results = JSON.parse(storedData);
    if (!results.length) {
      console.error('No results found in localStorage data');
      return;
    }

    // Get the latest result
    const latestResult = results[0].result;

    // Check if we have discrepancies
    if (!latestResult.discrepancies || !latestResult.discrepancies.length) {
      console.error('No discrepancies found in the latest result');
      return;
    }

    // Count how many have real AI explanations vs. placeholder text
    const total = latestResult.discrepancies.length;
    const withRealAI = latestResult.discrepancies.filter(
      d => d.aiExplanation && d.aiExplanation !== 'AI analysis in progress...'
    ).length;

    console.log(`AI Explanation Status: ${withRealAI}/${total} discrepancies have real AI explanations`);

    // Sample of the explanations
    const sample = latestResult.discrepancies
      .filter(d => d.aiExplanation && d.aiExplanation !== 'AI analysis in progress...')
      .slice(0, 3)
      .map(d => ({ id: d.id, preview: d.aiExplanation.substring(0, 100) + '...' }));

    console.log('Sample AI explanations:', sample);

    return {
      total,
      withRealAI,
      percentage: (withRealAI / total * 100).toFixed(1) + '%',
      sample
    };
  } catch (error) {
    console.error('Error checking AI explanations:', error);
  }
}

// Run the check
checkAIExplanationsInLocalStorage();
