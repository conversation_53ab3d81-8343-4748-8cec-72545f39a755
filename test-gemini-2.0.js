import { GoogleGenerativeAI } from '@google/generative-ai';
import fs from 'fs';

// Simple env loader
function loadEnv() {
  try {
    const envContent = fs.readFileSync('.env.local', 'utf8');
    const lines = envContent.split('\n');
    for (const line of lines) {
      const [key, value] = line.split('=');
      if (key && value) {
        process.env[key.trim()] = value.trim();
      }
    }
  } catch {
    console.log('No .env.local file found, using system environment variables');
  }
}

loadEnv();

async function testGemini2() {
  try {
    const genAI = new GoogleGenerativeAI(process.env.GOOGLE_GENERATIVE_AI_API_KEY);
    
    const model = genAI.getGenerativeModel({ 
      model: 'gemini-2.0-flash-exp',
      generationConfig: {
        temperature: 0.3,
        maxOutputTokens: 150,
        topK: 40,
        topP: 0.95,
      },
    });

    console.log('Testing Gemini 2.0 Flash...');
    
    const prompt = `Analyze this bank reconciliation discrepancy:
    
    1. Bank transaction: $1,500.00 debit on 2025-01-15 - "Office Supplies Purchase"
    
    Provide a brief explanation for why this might be unmatched in the ledger.`;

    const result = await model.generateContent(prompt);
    const response = await result.response;
    const text = response.text();
    
    console.log('✅ Gemini 2.0 Flash is working!');
    console.log('Response:', text);
    console.log('\nModel configuration updated successfully.');
    
  } catch (error) {
    console.error('❌ Error testing Gemini 2.0 Flash:', error);
    
    if (error.message?.includes('429') || error.message?.includes('Too Many Requests')) {
      console.log('Rate limit hit - this is expected if you\'ve been testing frequently.');
      console.log('The model configuration is correct, just wait a few minutes before testing again.');
    }
  }
}

testGemini2();
