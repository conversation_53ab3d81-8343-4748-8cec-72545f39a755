// Test script for E2B endpoint
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import FormData from 'form-data';
import fetch from 'node-fetch';

// Get current directory with ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function testE2BEndpoint() {
  try {
    console.log('Starting E2B endpoint test...');
    
    // Check if example files exist
    const exampleDocsDir = path.join(__dirname, 'example-docs');
    const bankStatementPath = path.join(exampleDocsDir, 'RFSA bank statement July 2025.pdf');
    const ledgerPath = path.join(exampleDocsDir, 'RFSA_July_2025_CBE_bank_statement.xlsx');
    
    if (!fs.existsSync(bankStatementPath)) {
      console.error(`Bank statement file not found: ${bankStatementPath}`);
      return;
    }
    
    if (!fs.existsSync(ledgerPath)) {
      console.error(`Ledger file not found: ${ledgerPath}`);
      return;
    }
    
    console.log('Found example files:');
    console.log(`- Bank statement: ${bankStatementPath}`);
    console.log(`- Ledger: ${ledgerPath}`);
    
    // Create form data
    const formData = new FormData();
    formData.append('bank_statement', fs.createReadStream(bankStatementPath));
    formData.append('ledger', fs.createReadStream(ledgerPath));
    
    console.log('Sending request to E2B endpoint...');
    
    // Send request to E2B endpoint
    const response = await fetch('http://localhost:3000/api/process-files-e2b', {
      method: 'POST',
      body: formData,
      headers: formData.getHeaders(),
    });
    
    console.log(`Response status: ${response.status}`);
    
    if (response.status !== 200) {
      const text = await response.text();
      console.error(`Error response: ${text}`);
      return;
    }
    
    // Handle SSE response
    const reader = response.body;
    
    reader.on('readable', () => {
      let chunk;
      while (null !== (chunk = reader.read())) {
        const lines = chunk.toString().split('\n\n');
        
        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.substring(6);
            try {
              const parsed = JSON.parse(data);
              console.log(`[${parsed.type}] ${parsed.step || ''} ${JSON.stringify(parsed.data || {})}`);
              
              if (parsed.type === 'error') {
                console.error(`Error: ${parsed.error}`);
              }
            } catch (error) {
              console.log(`Raw data: ${data}`);
              console.error('JSON parse error:', error.message);
            }
          }
        }
      }
    });
    
    reader.on('end', () => {
      console.log('Response ended');
    });
    
    reader.on('error', (err) => {
      console.error('Stream error:', err);
    });
    
  } catch (error) {
    console.error('Test failed:', error);
  }
}

testE2BEndpoint();
