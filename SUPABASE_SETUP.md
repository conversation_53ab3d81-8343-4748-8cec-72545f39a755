# Supabase Integration Setup

This document provides instructions for setting up the Supabase database integration for the Bank Reconciliation AI application.

## Prerequisites

1. Supabase account and project created
2. Environment variables configured in `.env.local`
3. Supabase CLI installed (optional, for local development)

## Environment Variables

Ensure these variables are set in your `.env.local` file:

```env
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
```

## Database Schema

The application uses the following tables:

### 1. reconciliation_reports
Main table storing reconciliation report metadata.

### 2. transactions
Stores individual bank and ledger transactions.

### 3. matches
Stores matched transaction pairs with confidence scores.

### 4. discrepancies
Stores unmatched transactions with AI explanations.

## Setup Instructions

### Option 1: Using Supabase Dashboard (Recommended)

1. Go to your Supabase project dashboard
2. Navigate to the SQL Editor
3. Copy and paste the contents of `supabase/migrations/001_create_reconciliation_tables.sql`
4. Execute the SQL to create all tables, indexes, and RLS policies

### Option 2: Using Supabase CLI

If you have the Supabase CLI installed:

```bash
# Initialize Supabase in your project (if not already done)
supabase init

# Link to your remote project
supabase link --project-ref your-project-ref

# Apply the migration
supabase db push
```

## Features Implemented

### 1. Efficient Caching with TanStack Query
- **Optimized Query Client**: Configured with intelligent caching strategies
- **Stale Time**: 5 minutes for reports, 2 minutes for lists
- **Cache Time**: 10 minutes to keep data available after component unmounts
- **Retry Logic**: Exponential backoff for failed requests
- **Prefetching**: Automatic prefetching of related data

### 2. Supabase Integration
- **Type-Safe Client**: Full TypeScript support for database operations
- **Row Level Security**: Proper RLS policies for data protection
- **Optimistic Updates**: Immediate UI updates with rollback on failure
- **Batch Operations**: Efficient bulk inserts for large datasets

### 3. Real-time Data Management
- **Automatic Saving**: Reports are automatically saved to Supabase after processing
- **Progress Tracking**: Real-time updates during save operations
- **Error Handling**: Graceful fallbacks if database operations fail

## Usage

### Testing the Integration

Use the `SupabaseTest` component to verify your setup:

```tsx
import { SupabaseTest } from '@/components/supabase-test';

// Add to any page to test the integration
<SupabaseTest />
```

### Using the Hooks

```tsx
import { 
  useReconciliationReports, 
  useSaveReconciliationReport,
  useSupabaseConnection 
} from '@/hooks/use-reconciliation-reports';

// Check connection status
const { data: isConnected } = useSupabaseConnection();

// Fetch reports with caching
const { data: reports, isLoading } = useReconciliationReports(10);

// Save a new report
const saveReport = useSaveReconciliationReport();
```

## Performance Optimizations

### 1. Query Key Factory
Centralized query key management for consistent caching:

```typescript
queryKeys.reconciliationReports.list({ limit: 10 })
queryKeys.reconciliationReports.detail(reportId)
```

### 2. Optimistic Updates
Immediate UI feedback with automatic rollback on errors:

```typescript
// Optimistically add report to cache
optimisticUpdates.addReportOptimistically(newReport);
```

### 3. Prefetching Strategies
Proactive data loading for better UX:

```typescript
// Prefetch report details on hover
const prefetchDetails = usePrefetchReportDetails();
```

## Database Indexes

The following indexes are created for optimal performance:

- `idx_reconciliation_reports_report_id`: Fast lookups by report ID
- `idx_reconciliation_reports_created_at`: Efficient sorting by creation date
- `idx_transactions_report_id`: Quick transaction filtering by report
- `idx_matches_report_id`: Fast match queries
- `idx_discrepancies_report_id`: Efficient discrepancy lookups

## Security

### Row Level Security (RLS)
- **Authenticated users**: Read access to all data
- **Service role**: Full CRUD access for API operations
- **Anonymous users**: No access

### Data Protection
- All sensitive operations use the service role key
- Client-side operations use the anonymous key with RLS
- Proper error handling prevents data leakage

## Troubleshooting

### Connection Issues
1. Verify environment variables are correct
2. Check Supabase project status
3. Ensure RLS policies are properly configured

### Performance Issues
1. Monitor query performance in React Query DevTools
2. Check database query performance in Supabase dashboard
3. Verify indexes are being used effectively

### Data Consistency
1. Use optimistic updates for immediate feedback
2. Implement proper error boundaries
3. Monitor cache invalidation patterns

## Next Steps

1. **Run the migration**: Execute the SQL migration to create tables
2. **Test the integration**: Use the SupabaseTest component
3. **Monitor performance**: Use React Query DevTools in development
4. **Scale considerations**: Add database connection pooling for production

## Support

For issues with the Supabase integration:
1. Check the browser console for detailed error messages
2. Verify database permissions in Supabase dashboard
3. Test queries directly in the SQL editor
4. Monitor network requests in browser DevTools
