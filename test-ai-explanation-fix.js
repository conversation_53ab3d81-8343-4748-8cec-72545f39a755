#!/usr/bin/env node

/**
 * Test script to verify AI explanation handling fix
 * This script simulates the frontend behavior to test if AI explanations are properly preserved
 */

console.log('🧪 Testing AI Explanation Fix');
console.log('==============================\n');

// Simulate the issue scenario
let currentResult = null;

// Step 1: Initial result with placeholder AI explanations (from server)
console.log('Step 1: Initial result received from server');
const initialResult = {
  id: 'test-reconciliation',
  uploadedAt: new Date().toISOString(),
  discrepancies: [
    { id: 'bank_bank_1', amount: ********, aiExplanation: "AI analysis in progress..." },
    { id: 'bank_bank_2', amount: 20000, aiExplanation: "AI analysis in progress..." },
    { id: 'bank_bank_3', amount: 35000, aiExplanation: "AI analysis in progress..." }
  ]
};

currentResult = initialResult;
console.log('Initial discrepancies:', currentResult.discrepancies.map(d => ({ 
  id: d.id, 
  hasAI: d.aiExplanation !== "AI analysis in progress..." 
})));

// Step 2: Simulate AI explanation updates during streaming
console.log('\nStep 2: Receiving AI explanation updates during streaming');

const handleAIExplanationUpdate = (discrepancyId, explanation) => {
  console.log(`[AI_UPDATE] Updating discrepancy ${discrepancyId}`);
  
  if (!currentResult) {
    console.log(`[AI_UPDATE] No previous result to update for ${discrepancyId}`);
    return;
  }

  const updatedDiscrepancies = currentResult.discrepancies.map(discrepancy => {
    if (discrepancy.id === discrepancyId) {
      console.log(`[AI_UPDATE] Found matching discrepancy ${discrepancyId}, updating explanation`);
      return { ...discrepancy, aiExplanation: explanation };
    }
    return discrepancy;
  });

  currentResult = { ...currentResult, discrepancies: updatedDiscrepancies };
  console.log(`[AI_UPDATE] Updated result saved for ${discrepancyId}`);
};

// Simulate receiving AI updates
handleAIExplanationUpdate('bank_bank_1', '**DISCREPANCY 1:** This is a large deposit that needs investigation...');
handleAIExplanationUpdate('bank_bank_2', '**DISCREPANCY 2:** This appears to be an unrecorded payment...');

console.log('After AI updates:', currentResult.discrepancies.map(d => ({ 
  id: d.id, 
  hasAI: d.aiExplanation !== "AI analysis in progress...",
  preview: d.aiExplanation.substring(0, 30) + '...'
})));

// Step 3: Simulate onSuccess callback with original data
console.log('\nStep 3: onSuccess callback called with original data');
const onSuccessData = {
  id: 'test-reconciliation-final',
  uploadedAt: new Date().toISOString(),
  status: 'completed',
  discrepancies: [
    { id: 'bank_bank_1', amount: ********, aiExplanation: "AI analysis in progress..." },
    { id: 'bank_bank_2', amount: 20000, aiExplanation: "AI analysis in progress..." },
    { id: 'bank_bank_3', amount: 35000, aiExplanation: "AI analysis in progress..." }
  ]
};

// Simulate the fixed onSuccess logic
const handleOnSuccess = (data) => {
  console.log('Processing completed successfully, received data:', data.id);

  const prevResult = currentResult;
  
  if (prevResult) {
    console.log('We have existing results with AI updates, preserving them');
    
    // Count how many AI explanations we already have
    const existingAICount = prevResult.discrepancies.filter(d => 
      d.aiExplanation && d.aiExplanation !== "AI analysis in progress..."
    ).length;
    console.log(`Found ${existingAICount} existing AI explanations out of ${prevResult.discrepancies.length} discrepancies`);

    // If we have existing AI explanations, preserve the current result and just update metadata
    if (existingAICount > 0) {
      console.log('Preserving existing result with AI explanations, only updating metadata');
      currentResult = { 
        ...prevResult,
        id: data.id,
        uploadedAt: data.uploadedAt,
        status: data.status
      };
      return currentResult;
    }

    // If no AI explanations yet, merge normally
    const mergedResult = { ...data };
    mergedResult.discrepancies = data.discrepancies.map(newDisc => {
      const existingDisc = prevResult.discrepancies.find(d => d.id === newDisc.id);
      if (existingDisc && existingDisc.aiExplanation && existingDisc.aiExplanation !== "AI analysis in progress...") {
        console.log(`Preserving AI explanation for ${newDisc.id}`);
        return { ...newDisc, aiExplanation: existingDisc.aiExplanation };
      }
      return newDisc;
    });

    currentResult = mergedResult;
    return mergedResult;
  } else {
    console.log('No existing results, using data from onSuccess');
    currentResult = data;
    return data;
  }
};

const finalResult = handleOnSuccess(onSuccessData);

console.log('\nFinal Result:');
console.log('ID:', finalResult.id);
console.log('Status:', finalResult.status);
console.log('Discrepancies with AI:', finalResult.discrepancies.map(d => ({ 
  id: d.id, 
  hasAI: d.aiExplanation !== "AI analysis in progress...",
  preview: d.aiExplanation.substring(0, 50) + '...'
})));

// Verify the fix worked
const aiExplanationsPreserved = finalResult.discrepancies.filter(d => 
  d.aiExplanation !== "AI analysis in progress..."
).length;

console.log('\n🎯 Test Results:');
console.log(`✅ AI explanations preserved: ${aiExplanationsPreserved}/3`);
console.log(`✅ Metadata updated: ${finalResult.id === onSuccessData.id ? 'Yes' : 'No'}`);
console.log(`✅ Status updated: ${finalResult.status === onSuccessData.status ? 'Yes' : 'No'}`);

if (aiExplanationsPreserved === 2) {
  console.log('🎉 SUCCESS: AI explanations were properly preserved!');
} else {
  console.log('❌ FAILED: AI explanations were not preserved correctly');
}
