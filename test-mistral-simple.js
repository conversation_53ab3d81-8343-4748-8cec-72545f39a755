const fs = require('fs');
const path = require('path');

// Read environment variables from .env.local
function loadEnvFile() {
  const envPath = path.join(__dirname, '.env.local');
  if (fs.existsSync(envPath)) {
    const envContent = fs.readFileSync(envPath, 'utf8');
    envContent.split('\n').forEach(line => {
      const [key, ...valueParts] = line.split('=');
      if (key && valueParts.length > 0) {
        const value = valueParts.join('=').trim();
        if (value.startsWith('"') && value.endsWith('"')) {
          process.env[key.trim()] = value.slice(1, -1);
        } else {
          process.env[key.trim()] = value;
        }
      }
    });
  }
}

async function testMistralOCR() {
  loadEnvFile();
  
  const apiKey = process.env.MISTRAL_API_KEY;
  if (!apiKey) {
    console.error('❌ MISTRAL_API_KEY not found in environment variables');
    return;
  }

  console.log('🔍 Testing Mistral OCR API...');
  console.log('✅ API Key found:', apiKey.substring(0, 10) + '...');

  // Test PDF file path
  const pdfPath = path.join(__dirname, 'example-docs', 'RFSA bank statement July 2025_compressed.pdf');
  
  if (!fs.existsSync(pdfPath)) {
    console.error('❌ PDF file not found:', pdfPath);
    return;
  }

  console.log('✅ PDF file found:', pdfPath);
  console.log('📄 File size:', fs.statSync(pdfPath).size, 'bytes');

  try {
    // Read PDF file as buffer and convert to base64
    const pdfBuffer = fs.readFileSync(pdfPath);
    const base64Content = pdfBuffer.toString('base64');
    
    const requestBody = {
      model: 'mistral-ocr-latest',
      document: {
        type: 'document_url',
        document_url: `data:application/pdf;base64,${base64Content}`
      },
      include_image_base64: true
    };

    console.log('🚀 Making request to Mistral OCR API...');
    
    // Use dynamic import for fetch in Node.js
    const { default: fetch } = await import('node-fetch');
    
    const response = await fetch('https://api.mistral.ai/v1/ocr', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
      },
      body: JSON.stringify(requestBody),
    });

    console.log('📡 Response status:', response.status);
    console.log('📡 Response headers:', Object.fromEntries(response.headers.entries()));

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ API Error:', response.status, errorText);
      return;
    }

    const result = await response.json();
    console.log('✅ Mistral OCR Response received');
    console.log('📊 Response structure:', Object.keys(result));
    console.log('📊 Full response:', JSON.stringify(result, null, 2));
    
    if (result.pages && result.pages.length > 0) {
      console.log('📄 Number of pages processed:', result.pages.length);
      const firstPage = result.pages[0];
      console.log('📄 First page structure:', Object.keys(firstPage));
      
      if (firstPage.text) {
        console.log('📝 First page text length:', firstPage.text.length);
        console.log('📝 First 500 characters:');
        console.log(firstPage.text.substring(0, 500));
        console.log('...');
      }
      
      if (firstPage.markdown) {
        console.log('📋 First page markdown length:', firstPage.markdown.length);
        console.log('📋 First 500 characters of markdown:');
        console.log(firstPage.markdown.substring(0, 500));
        console.log('...');
      }
    }
    
    if (result.content) {
      console.log('📝 Extracted content length:', result.content.length);
      console.log('📝 First 500 characters:');
      console.log(result.content.substring(0, 500));
      console.log('...');
    }

    if (result.text) {
      console.log('📝 Extracted text length:', result.text.length);
      console.log('📝 First 500 characters:');
      console.log(result.text.substring(0, 500));
      console.log('...');
    }

  } catch (error) {
    console.error('❌ Error testing Mistral OCR:', error.message);
    if (error.cause) {
      console.error('❌ Cause:', error.cause);
    }
  }
}

// Run the test
testMistralOCR().catch(console.error);
