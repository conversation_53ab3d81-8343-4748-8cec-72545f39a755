#!/usr/bin/env node

/**
 * Debug script to test the frontend AI explanation issue
 * This will make a direct request to the streaming endpoint and check the responses
 */

import fs from 'fs';
import path from 'path';
import FormData from 'form-data';
import fetch from 'node-fetch';

console.log('🔍 Debugging Frontend AI Explanation Issue');
console.log('==========================================\n');

async function testStreamingEndpoint() {
  try {
    console.log('1. Testing streaming endpoint directly...');

    // Check if we have test files
    const testFiles = [
      'RFSA bank statement July 2025_compressed.pdf',
      'RFSA July 2025 CBE bank statement.xlsx'
    ];

    let bankFile = null;
    let ledgerFile = null;

    // Look for test files in common locations
    const possiblePaths = ['.', 'test-files', 'uploads', 'samples', 'example-docs'];

    for (const dir of possiblePaths) {
      try {
        const files = fs.readdirSync(dir);
        for (const file of files) {
          if (file.includes('bank') && file.endsWith('.pdf')) {
            bankFile = path.join(dir, file);
            console.log(`Found bank file: ${bankFile}`);
          }
          if (file.includes('ledger') || (file.includes('.xlsx') && file.includes('bank'))) {
            ledgerFile = path.join(dir, file);
            console.log(`Found ledger file: ${ledgerFile}`);
          }
        }
      } catch (e) {
        // Directory doesn't exist, continue
      }
    }

    if (!bankFile || !ledgerFile) {
      console.log('❌ Test files not found. Looking for any PDF and Excel files...');

      // Look for any PDF and Excel files
      for (const dir of possiblePaths) {
        try {
          const files = fs.readdirSync(dir);
          for (const file of files) {
            if (!bankFile && file.endsWith('.pdf')) {
              bankFile = path.join(dir, file);
              console.log(`Using PDF file: ${bankFile}`);
            }
            if (!ledgerFile && (file.endsWith('.xlsx') || file.endsWith('.xls'))) {
              ledgerFile = path.join(dir, file);
              console.log(`Using Excel file: ${ledgerFile}`);
            }
          }
        } catch (e) {
          // Directory doesn't exist, continue
        }
      }
    }

    if (!bankFile || !ledgerFile) {
      console.log('❌ No suitable test files found. Please ensure you have:');
      console.log('   - A PDF bank statement file');
      console.log('   - An Excel ledger file');
      console.log('   In the current directory or test-files directory');
      return;
    }

    console.log('\n2. Making request to streaming endpoint...');

    const form = new FormData();
    form.append('bankStatement', fs.createReadStream(bankFile));
    form.append('ledger', fs.createReadStream(ledgerFile));

    const response = await fetch('http://localhost:3001/api/process-files-stream', {
      method: 'POST',
      body: form
    });

    if (!response.ok) {
      console.log(`❌ HTTP Error: ${response.status} ${response.statusText}`);
      const errorText = await response.text();
      console.log('Error response:', errorText);
      return;
    }

    console.log('✅ Streaming response received, processing...');

    let aiUpdatesReceived = 0;
    let resultReceived = false;
    const aiExplanations = new Map();

    const reader = response.body;
    let buffer = '';

    reader.on('data', (chunk) => {
      buffer += chunk.toString();
      const lines = buffer.split('\n');
      buffer = lines.pop() || '';

      for (const line of lines) {
        if (line.startsWith('data: ') && line.trim().length > 6) {
          try {
            const jsonData = line.slice(6).trim();
            if (jsonData) {
              const message = JSON.parse(jsonData);

              if (message.type === 'progress') {
                console.log(`[PROGRESS] ${message.step}`);
              } else if (message.type === 'result') {
                resultReceived = true;
                console.log('[RESULT] Initial result received');
                console.log(`  - Discrepancies: ${message.data.discrepancies?.length || 0}`);
              } else if (message.type === 'ai_explanation_update') {
                aiUpdatesReceived++;
                const { discrepancyId, aiExplanation } = message.data;
                aiExplanations.set(discrepancyId, aiExplanation);
                console.log(`[AI_UPDATE ${aiUpdatesReceived}] ${discrepancyId}: ${aiExplanation.substring(0, 80)}...`);
              } else if (message.type === 'error') {
                console.log(`[ERROR] ${message.error}`);
              }
            }
          } catch (parseError) {
            console.log('Failed to parse message:', line.substring(0, 100));
          }
        }
      }
    });

    reader.on('end', () => {
      console.log('\n3. Streaming completed');
      console.log(`✅ Result received: ${resultReceived}`);
      console.log(`✅ AI updates received: ${aiUpdatesReceived}`);
      console.log(`✅ Unique AI explanations: ${aiExplanations.size}`);

      if (aiUpdatesReceived > 0) {
        console.log('\n📝 Sample AI explanations:');
        let count = 0;
        for (const [id, explanation] of aiExplanations) {
          if (count < 3) {
            console.log(`  ${id}: ${explanation.substring(0, 100)}...`);
            count++;
          }
        }

        console.log('\n🎯 DIAGNOSIS:');
        console.log('✅ Backend is working correctly - AI explanations are being sent');
        console.log('❌ Frontend issue - AI explanations are not being displayed');
        console.log('\n💡 Next steps:');
        console.log('1. Check browser console for frontend logs');
        console.log('2. Verify handleAIExplanationUpdate is being called');
        console.log('3. Check if React state updates are being applied');
      } else {
        console.log('\n❌ No AI explanations received from backend');
        console.log('💡 Check backend AI processing logic');
      }
    });

    reader.on('error', (error) => {
      console.log('❌ Stream error:', error);
    });

  } catch (error) {
    console.log('❌ Error testing streaming endpoint:', error.message);
  }
}

// Run the test
testStreamingEndpoint();
