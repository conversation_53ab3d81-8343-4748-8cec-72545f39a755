import fs from 'fs';

async function testStreamingAPI() {
  try {
    console.log('🧪 Testing Streaming API with Fixed AI Queue');
    console.log('=' .repeat(50));

    // Use files from example-docs folder
    const pdfPath = './example-docs/RFSA bank statement July 2025_compressed.pdf';
    const excelPath = './example-docs/RFSA_July_2025_CBE_bank_statement.xlsx';

    if (!fs.existsSync(pdfPath) || !fs.existsSync(excelPath)) {
      console.error('❌ Test files not found');
      return;
    }

    const formData = new FormData();
    formData.append('bankStatement', new Blob([fs.readFileSync(pdfPath)]), 'statement.pdf');
    formData.append('ledger', new Blob([fs.readFileSync(excelPath)]), 'ledger.xlsx');

    console.log('\n🚀 Processing files via streaming API...');
    const startTime = Date.now();

    // Use the streaming endpoint
    const response = await fetch('http://localhost:3001/api/process-files-stream', {
      method: 'POST',
      body: formData
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ API Error:', response.status, errorText);
      return;
    }

    console.log('✅ Started streaming response...\n');

    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let buffer = '';
    let aiUpdatesReceived = 0;
    let resultReceived = false;

    while (true) {
      const { done, value } = await reader.read();

      if (done) break;

      const chunk = decoder.decode(value, { stream: true });
      buffer += chunk;
      const lines = buffer.split('\n');

      buffer = lines.pop() || '';

      for (const line of lines) {
        if (line.startsWith('data: ')) {
          try {
            const jsonData = line.slice(6).trim();
            if (jsonData) {
              const message = JSON.parse(jsonData);

              if (message.type === 'progress') {
                console.log(`📋 ${message.step}`);
                if (message.data) {
                  console.log('   ', JSON.stringify(message.data));
                }
              } else if (message.type === 'result') {
                console.log('\n✅ Final result received');
                resultReceived = true;
                console.log(`   Matches: ${message.data.matches?.length || 0}`);
                console.log(`   Discrepancies: ${message.data.discrepancies?.length || 0}`);
              } else if (message.type === 'ai_explanation_update') {
                aiUpdatesReceived++;
                console.log(`🤖 AI Update #${aiUpdatesReceived}: ${message.data.discrepancyId}`);
              } else if (message.type === 'error') {
                console.error('❌ Error:', message.error);
              }
            }
          } catch {
            // Ignore parse errors for incomplete messages
          }
        }
      }
    }

    const processingTime = ((Date.now() - startTime) / 1000).toFixed(1);

    console.log('\n' + '='.repeat(50));
    console.log(`🎉 Processing completed in ${processingTime}s`);
    console.log(`📊 Result received: ${resultReceived ? 'Yes' : 'No'}`);
    console.log(`🤖 AI updates received: ${aiUpdatesReceived}`);

    if (resultReceived && aiUpdatesReceived > 0) {
      console.log('✅ INFINITE LOOP FIXED! Processing completed successfully.');
    } else {
      console.log('⚠️ Test may have issues - check the results above');
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testStreamingAPI();
