const fs = require('fs');
const path = require('path');

// Read environment variables from .env.local
function loadEnvFile() {
  const envPath = path.join(__dirname, '.env.local');
  if (fs.existsSync(envPath)) {
    const envContent = fs.readFileSync(envPath, 'utf8');
    envContent.split('\n').forEach(line => {
      const [key, ...valueParts] = line.split('=');
      if (key && valueParts.length > 0) {
        const value = valueParts.join('=').trim();
        if (value.startsWith('"') && value.endsWith('"')) {
          process.env[key.trim()] = value.slice(1, -1);
        } else {
          process.env[key.trim()] = value;
        }
      }
    });
  }
}

// Mock File class for Node.js testing
class MockFile {
  constructor(buffer, name, type) {
    this.buffer = buffer;
    this.name = name;
    this.type = type;
    this.size = buffer.length;
  }
}

async function testMistralOCRIntegration() {
  loadEnvFile();
  
  const apiKey = process.env.MISTRAL_API_KEY;
  if (!apiKey) {
    console.error('❌ MISTRAL_API_KEY not found in environment variables');
    return;
  }

  console.log('🔍 Testing Mistral OCR Integration with Transaction Parsing...');
  console.log('✅ API Key found:', apiKey.substring(0, 10) + '...');

  // Test PDF file path
  const pdfPath = path.join(__dirname, 'example-docs', 'RFSA bank statement July 2025_compressed.pdf');
  
  if (!fs.existsSync(pdfPath)) {
    console.error('❌ PDF file not found:', pdfPath);
    return;
  }

  console.log('✅ PDF file found:', pdfPath);
  console.log('📄 File size:', fs.statSync(pdfPath).size, 'bytes');

  try {
    // Read PDF file as buffer
    const pdfBuffer = fs.readFileSync(pdfPath);
    const mockFile = new MockFile(pdfBuffer, 'RFSA bank statement July 2025_compressed.pdf', 'application/pdf');

    // Import and test the Mistral OCR service
    const { createMistralOCRService } = await import('./src/lib/mistral-ocr.ts');
    const mistralService = createMistralOCRService(apiKey);

    console.log('🚀 Testing Mistral OCR service...');
    
    // Test parseDocument method
    console.log('📄 Testing parseDocument...');
    const ocrResult = await mistralService.parseDocument(mockFile);
    
    console.log('✅ OCR Result received');
    console.log('📊 Pages processed:', ocrResult.pages.length);
    console.log('📊 Model used:', ocrResult.model);
    console.log('📊 Usage info:', ocrResult.usage_info);

    // Test extractBankTransactions method
    console.log('💰 Testing extractBankTransactions...');
    const transactions = await mistralService.extractBankTransactions(mockFile);
    
    console.log('✅ Transaction extraction completed');
    console.log('📊 Total transactions extracted:', transactions.length);
    
    if (transactions.length > 0) {
      console.log('📋 First 5 transactions:');
      transactions.slice(0, 5).forEach((transaction, index) => {
        console.log(`  ${index + 1}. ${transaction.date} | ${transaction.description} | ${transaction.reference} | ${transaction.amount} | ${transaction.type}`);
      });
      
      // Analyze transaction patterns
      const debits = transactions.filter(t => t.type === 'debit');
      const credits = transactions.filter(t => t.type === 'credit');
      const totalDebitAmount = debits.reduce((sum, t) => sum + Math.abs(t.amount), 0);
      const totalCreditAmount = credits.reduce((sum, t) => sum + t.amount, 0);
      
      console.log('📊 Transaction Analysis:');
      console.log(`  - Debit transactions: ${debits.length} (Total: ${totalDebitAmount.toLocaleString()})`);
      console.log(`  - Credit transactions: ${credits.length} (Total: ${totalCreditAmount.toLocaleString()})`);
      
      // Check for specific RFSA patterns
      const rfsaTransactions = transactions.filter(t => 
        t.reference.includes('FT') || 
        t.description.includes('ECC-SFIN') || 
        t.description.includes('FIN')
      );
      console.log(`  - RFSA-specific transactions: ${rfsaTransactions.length}`);
      
      if (rfsaTransactions.length > 0) {
        console.log('✅ Successfully identified RFSA transaction patterns');
      }
    } else {
      console.log('⚠️ No transactions extracted - checking markdown content...');
      
      // Check if we have markdown content
      const firstPageMarkdown = ocrResult.pages[0]?.markdown;
      if (firstPageMarkdown) {
        console.log('📋 First page markdown preview:');
        console.log(firstPageMarkdown.substring(0, 500) + '...');
      }
    }

  } catch (error) {
    console.error('❌ Error testing Mistral OCR integration:', error.message);
    if (error.stack) {
      console.error('Stack trace:', error.stack);
    }
  }
}

// Run the test
testMistralOCRIntegration().catch(console.error);
