#!/usr/bin/env python3
"""
Improved Transaction Matching Algorithm for Bank Reconciliation
Based on analysis of actual RFSA bank statement and ledger data.
"""

import pandas as pd
import re
import json
from datetime import datetime, timedelta
from typing import List, Dict, Tuple, Optional
import difflib
from dataclasses import dataclass
from enum import Enum

class MatchType(Enum):
    EXACT = "exact"
    FUZZY = "fuzzy"
    PARTIAL = "partial"
    NO_MATCH = "no_match"

@dataclass
class Transaction:
    id: str
    date: datetime
    amount: float
    description: str
    reference: str
    type: str  # 'debit' or 'credit'
    source: str  # 'bank' or 'ledger'
    raw_data: Dict

@dataclass
class Match:
    bank_transaction: Transaction
    ledger_transaction: Transaction
    match_type: MatchType
    confidence: float
    matching_criteria: List[str]
    notes: str

class ImprovedTransactionMatcher:
    """
    Comprehensive transaction matching algorithm that handles real-world complexities
    discovered in the RFSA bank statement and ledger analysis.
    """
    
    def __init__(self):
        self.reference_patterns = {
            # Map bank reference patterns to ledger patterns
            'check_number': {
                'bank_pattern': r'CHQ NO (\d+)',
                'ledger_pattern': r'CD(\d+)',
                'description': 'Check number matching'
            },
            'voucher_number': {
                'bank_pattern': r'(PV-?\d+)',
                'ledger_pattern': r'(PV-?\d+)',
                'description': 'Voucher number matching'
            },
            'fin_reference': {
                'bank_pattern': r'FIN/(\d+/\d+)',
                'ledger_pattern': r'FIN/(\d+/\d+)',
                'description': 'FIN reference matching'
            }
        }
        
        self.amount_tolerance = 0.01  # Allow 1 cent difference for rounding
        self.date_tolerance_days = 7  # Allow up to 7 days difference
        
    def normalize_amount(self, amount: float, transaction_type: str, source: str) -> float:
        """
        Normalize amounts to handle different accounting conventions.
        Bank: Debits are negative, Credits are positive
        Ledger: May show both as positive with separate debit/credit columns
        """
        # Convert to absolute value for comparison, handle sign differences later
        return abs(float(amount))
    
    def normalize_date(self, date_str: str) -> Optional[datetime]:
        """Handle various date formats found in bank statements and ledgers."""
        if pd.isna(date_str) or not date_str:
            return None
            
        date_str = str(date_str).strip()
        
        # Common date patterns
        patterns = [
            '%Y-%m-%d %H:%M:%S',  # 2025-07-01 00:00:00
            '%Y-%m-%d',           # 2025-07-01
            '%d %m %Y',           # 01 07 2025
            '%m/%d/%y',           # 7/1/25
            '%m/%d/%Y',           # 7/1/2025
            '%d/%m/%Y',           # 1/7/2025
            '%d-%m-%Y',           # 01-07-2025
        ]
        
        for pattern in patterns:
            try:
                return datetime.strptime(date_str, pattern)
            except ValueError:
                continue
                
        return None
    
    def extract_reference_numbers(self, text: str) -> Dict[str, str]:
        """Extract various reference numbers from transaction descriptions."""
        references = {}
        
        for ref_type, patterns in self.reference_patterns.items():
            # Check bank pattern
            bank_match = re.search(patterns['bank_pattern'], text, re.IGNORECASE)
            if bank_match:
                references[f'bank_{ref_type}'] = bank_match.group(1)
            
            # Check ledger pattern
            ledger_match = re.search(patterns['ledger_pattern'], text, re.IGNORECASE)
            if ledger_match:
                references[f'ledger_{ref_type}'] = ledger_match.group(1)
        
        return references
    
    def calculate_similarity_score(self, text1: str, text2: str) -> float:
        """Calculate text similarity using difflib."""
        if not text1 or not text2:
            return 0.0
        return difflib.SequenceMatcher(None, text1.lower(), text2.lower()).ratio()
    
    def is_amount_match(self, amount1: float, amount2: float, tolerance: float = None) -> bool:
        """Check if two amounts match within tolerance."""
        if tolerance is None:
            tolerance = self.amount_tolerance
        return abs(amount1 - amount2) <= tolerance
    
    def is_date_match(self, date1: datetime, date2: datetime, tolerance_days: int = None) -> bool:
        """Check if two dates match within tolerance."""
        if not date1 or not date2:
            return False
        if tolerance_days is None:
            tolerance_days = self.date_tolerance_days
        return abs((date1 - date2).days) <= tolerance_days
    
    def find_reference_matches(self, bank_transaction: Transaction, ledger_transaction: Transaction) -> List[str]:
        """Find matching reference numbers between bank and ledger transactions."""
        matches = []
        
        # Extract references from both transactions
        bank_refs = self.extract_reference_numbers(f"{bank_transaction.description} {bank_transaction.reference}")
        ledger_refs = self.extract_reference_numbers(f"{ledger_transaction.description} {ledger_transaction.reference}")
        
        # Check for check number matches (CD******** = CHQ NO ********)
        if 'bank_check_number' in bank_refs and 'ledger_check_number' in ledger_refs:
            if bank_refs['bank_check_number'] == ledger_refs['ledger_check_number']:
                matches.append(f"Check number match: {bank_refs['bank_check_number']}")
        
        # Check for voucher matches
        if 'bank_voucher_number' in bank_refs and 'ledger_voucher_number' in ledger_refs:
            if bank_refs['bank_voucher_number'] == ledger_refs['ledger_voucher_number']:
                matches.append(f"Voucher match: {bank_refs['bank_voucher_number']}")
        
        # Check for FIN reference matches
        if 'bank_fin_reference' in bank_refs and 'ledger_fin_reference' in ledger_refs:
            if bank_refs['bank_fin_reference'] == ledger_refs['ledger_fin_reference']:
                matches.append(f"FIN reference match: {bank_refs['bank_fin_reference']}")
        
        return matches
    
    def calculate_match_confidence(self, bank_transaction: Transaction, ledger_transaction: Transaction) -> Tuple[float, List[str], MatchType]:
        """Calculate confidence score and determine match type."""
        criteria = []
        confidence = 0.0
        
        # Amount matching (40% weight)
        bank_amount = self.normalize_amount(bank_transaction.amount, bank_transaction.type, 'bank')
        ledger_amount = self.normalize_amount(ledger_transaction.amount, ledger_transaction.type, 'ledger')
        
        if self.is_amount_match(bank_amount, ledger_amount):
            confidence += 0.4
            criteria.append(f"Exact amount match: {bank_amount}")
        elif self.is_amount_match(bank_amount, ledger_amount, tolerance=bank_amount * 0.01):  # 1% tolerance
            confidence += 0.35
            criteria.append(f"Fuzzy amount match: {bank_amount} ≈ {ledger_amount}")
        
        # Date matching (25% weight)
        if self.is_date_match(bank_transaction.date, ledger_transaction.date, tolerance_days=1):
            confidence += 0.25
            criteria.append("Same day match")
        elif self.is_date_match(bank_transaction.date, ledger_transaction.date, tolerance_days=3):
            confidence += 0.2
            criteria.append("Within 3 days")
        elif self.is_date_match(bank_transaction.date, ledger_transaction.date, tolerance_days=7):
            confidence += 0.15
            criteria.append("Within 7 days")
        
        # Reference matching (30% weight)
        reference_matches = self.find_reference_matches(bank_transaction, ledger_transaction)
        if reference_matches:
            confidence += 0.3
            criteria.extend(reference_matches)
        else:
            # Try description similarity as fallback
            desc_similarity = self.calculate_similarity_score(bank_transaction.description, ledger_transaction.description)
            if desc_similarity > 0.6:
                confidence += 0.15
                criteria.append(f"Description similarity: {desc_similarity:.2f}")
        
        # Transaction type consistency (5% weight)
        # Bank debits should match ledger credits (money leaving bank account)
        if (bank_transaction.type == 'debit' and ledger_transaction.type == 'credit') or \
           (bank_transaction.type == 'credit' and ledger_transaction.type == 'debit'):
            confidence += 0.05
            criteria.append("Transaction type consistency")
        
        # Determine match type
        if confidence >= 0.9:
            match_type = MatchType.EXACT
        elif confidence >= 0.7:
            match_type = MatchType.FUZZY
        elif confidence >= 0.5:
            match_type = MatchType.PARTIAL
        else:
            match_type = MatchType.NO_MATCH
        
        return confidence, criteria, match_type
    
    def match_transactions(self, bank_transactions: List[Transaction], ledger_transactions: List[Transaction]) -> List[Match]:
        """
        Main matching algorithm that finds the best matches between bank and ledger transactions.
        """
        matches = []
        used_ledger_ids = set()
        
        # Sort bank transactions by date for processing
        bank_transactions.sort(key=lambda t: t.date if t.date else datetime.min)
        
        for bank_tx in bank_transactions:
            best_match = None
            best_confidence = 0.0
            
            for ledger_tx in ledger_transactions:
                if ledger_tx.id in used_ledger_ids:
                    continue
                
                confidence, criteria, match_type = self.calculate_match_confidence(bank_tx, ledger_tx)
                
                if confidence > best_confidence and match_type != MatchType.NO_MATCH:
                    best_confidence = confidence
                    best_match = Match(
                        bank_transaction=bank_tx,
                        ledger_transaction=ledger_tx,
                        match_type=match_type,
                        confidence=confidence,
                        matching_criteria=criteria,
                        notes=f"Best match with {confidence:.2f} confidence"
                    )
            
            if best_match and best_confidence >= 0.5:  # Minimum confidence threshold
                matches.append(best_match)
                used_ledger_ids.add(best_match.ledger_transaction.id)
        
        return matches
    
    def generate_reconciliation_report(self, matches: List[Match], bank_transactions: List[Transaction], ledger_transactions: List[Transaction]) -> Dict:
        """Generate a comprehensive reconciliation report."""
        matched_bank_ids = {match.bank_transaction.id for match in matches}
        matched_ledger_ids = {match.ledger_transaction.id for match in matches}
        
        unmatched_bank = [tx for tx in bank_transactions if tx.id not in matched_bank_ids]
        unmatched_ledger = [tx for tx in ledger_transactions if tx.id not in matched_ledger_ids]
        
        # Calculate balances
        bank_balance = sum(tx.amount for tx in bank_transactions)
        ledger_balance = sum(tx.amount for tx in ledger_transactions)
        
        report = {
            "summary": {
                "total_bank_transactions": len(bank_transactions),
                "total_ledger_transactions": len(ledger_transactions),
                "matched_transactions": len(matches),
                "unmatched_bank_transactions": len(unmatched_bank),
                "unmatched_ledger_transactions": len(unmatched_ledger),
                "match_rate": len(matches) / max(len(bank_transactions), 1) * 100,
                "bank_balance": bank_balance,
                "ledger_balance": ledger_balance,
                "balance_difference": abs(bank_balance - ledger_balance)
            },
            "matches": [
                {
                    "bank_transaction": {
                        "id": match.bank_transaction.id,
                        "date": match.bank_transaction.date.isoformat() if match.bank_transaction.date else None,
                        "amount": match.bank_transaction.amount,
                        "description": match.bank_transaction.description,
                        "reference": match.bank_transaction.reference
                    },
                    "ledger_transaction": {
                        "id": match.ledger_transaction.id,
                        "date": match.ledger_transaction.date.isoformat() if match.ledger_transaction.date else None,
                        "amount": match.ledger_transaction.amount,
                        "description": match.ledger_transaction.description,
                        "reference": match.ledger_transaction.reference
                    },
                    "match_type": match.match_type.value,
                    "confidence": match.confidence,
                    "matching_criteria": match.matching_criteria,
                    "notes": match.notes
                }
                for match in matches
            ],
            "unmatched_bank": [
                {
                    "id": tx.id,
                    "date": tx.date.isoformat() if tx.date else None,
                    "amount": tx.amount,
                    "description": tx.description,
                    "reference": tx.reference,
                    "suggested_action": "Review for potential ledger entry"
                }
                for tx in unmatched_bank
            ],
            "unmatched_ledger": [
                {
                    "id": tx.id,
                    "date": tx.date.isoformat() if tx.date else None,
                    "amount": tx.amount,
                    "description": tx.description,
                    "reference": tx.reference,
                    "suggested_action": "Review for potential bank transaction"
                }
                for tx in unmatched_ledger
            ]
        }
        
        return report

def test_with_rfsa_data():
    """Test the algorithm with the actual RFSA data we discovered."""
    
    # Create test transactions based on our analysis
    bank_tx = Transaction(
        id="bank_1",
        date=datetime(2025, 7, 1),
        amount=-24000.00,  # Bank shows as debit (negative)
        description="CHQ NO ********",
        reference="TT25182CV3YG",
        type="debit",
        source="bank",
        raw_data={}
    )
    
    # Ledger transactions we found
    ledger_transactions = [
        Transaction(
            id="ledger_33",
            date=datetime(2025, 7, 1),
            amount=24000.00,  # Ledger shows as credit (positive)
            description="Bank CBE-RFSA *************",
            reference="FIN/2009/25",
            type="credit",
            source="ledger",
            raw_data={"voucher": "PV-353071"}
        ),
        Transaction(
            id="ledger_36",
            date=datetime(2025, 7, 1),
            amount=24000.00,
            description="Bank CBE-RFSA *************",
            reference="FIN/2011/25",
            type="credit",
            source="ledger",
            raw_data={"voucher": "Pv-353074"}
        ),
        Transaction(
            id="ledger_246",
            date=datetime(2025, 7, 17),
            amount=24000.00,
            description="Bank CBE-RFSA *************",
            reference="CD********",  # This should match!
            type="credit",
            source="ledger",
            raw_data={"voucher": "PV-353206"}
        )
    ]
    
    matcher = ImprovedTransactionMatcher()
    matches = matcher.match_transactions([bank_tx], ledger_transactions)
    
    print("RFSA DATA MATCHING TEST RESULTS:")
    print("="*50)
    
    for match in matches:
        print(f"Match found with {match.confidence:.2f} confidence ({match.match_type.value})")
        print(f"Bank: {match.bank_transaction.description} | {match.bank_transaction.amount}")
        print(f"Ledger: {match.ledger_transaction.description} | {match.ledger_transaction.reference}")
        print(f"Criteria: {', '.join(match.matching_criteria)}")
        print("-" * 30)
    
    # Generate full report
    report = matcher.generate_reconciliation_report(matches, [bank_tx], ledger_transactions)
    return report

if __name__ == "__main__":
    report = test_with_rfsa_data()
    print("\nFULL RECONCILIATION REPORT:")
    print(json.dumps(report, indent=2))
