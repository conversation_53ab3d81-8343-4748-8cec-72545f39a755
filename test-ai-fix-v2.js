/**
 * Test script to verify the fix for AI explanations
 * This simulates the workflow and checks if the fix prevents overwriting AI explanations
 */

// Create a mock localStorage
class MockLocalStorage {
  constructor() {
    this.store = {};
  }

  getItem(key) {
    return this.store[key] || null;
  }

  setItem(key, value) {
    this.store[key] = value;
  }

  clear() {
    this.store = {};
  }
}

// Create a mock ReconciliationStorage class
class MockReconciliationStorage {
  static localStorage = new MockLocalStorage();

  static saveResult(result) {
    const stored = JSON.parse(this.localStorage.getItem('reconciliation-results') || '[]');
    stored.unshift({
      id: result.id || 'test-id',
      timestamp: result.uploadedAt || new Date().toISOString(),
      bankFileName: 'test-bank.pdf',
      ledgerFileName: 'test-ledger.xlsx',
      result
    });
    this.localStorage.setItem('reconciliation-results', JSON.stringify(stored));
  }

  static getLatestResult() {
    const stored = JSON.parse(this.localStorage.getItem('reconciliation-results') || '[]');
    return stored[0]?.result || null;
  }
}

// Simulate the updated onSuccess handler
function simulateFixedOnSuccess(prevResult, newData) {
  console.log('Processing completed successfully');

  // Check if we already have results (potentially with AI updates)
  let finalResult;

  if (prevResult) {
    console.log('We have existing results with AI updates, preserving them');

    // Keep the structure from data but preserve any AI explanations we already have
    const mergedResult = { ...newData };
    mergedResult.discrepancies = newData.discrepancies.map(newDisc => {
      const existingDisc = prevResult.discrepancies.find(d => d.id === newDisc.id);
      if (existingDisc && existingDisc.aiExplanation && existingDisc.aiExplanation !== "AI analysis in progress...") {
        return { ...newDisc, aiExplanation: existingDisc.aiExplanation };
      }
      return newDisc;
    });

    // Save the merged result
    MockReconciliationStorage.saveResult(mergedResult);
    finalResult = mergedResult;
  } else {
    // No existing results, use the data from onSuccess
    console.log('No existing results, using data from onSuccess');
    MockReconciliationStorage.saveResult(newData);
    finalResult = newData;
  }

  return finalResult;
}

// Test function
function testFix() {
  console.log('=== TESTING AI EXPLANATION FIX ===');

  // Initial state
  let currentResult = null;

  // Step 1: Create initial result with placeholder AI explanations
  console.log('\nStep 1: Initial result with placeholder AI explanations');
  const initialResult = {
    id: 'test-reconciliation',
    uploadedAt: new Date().toISOString(),
    discrepancies: [
      { id: 'bank_bank_1', amount: ********, aiExplanation: "AI analysis in progress..." },
      { id: 'bank_bank_2', amount: 20000, aiExplanation: "AI analysis in progress..." }
    ]
  };

  currentResult = initialResult;
  console.log('Initial result set with placeholder AI explanations');

  // Step 2: Receive AI updates during streaming
  console.log('\nStep 2: Receive AI updates during streaming');
  const aiUpdate1 = "**DISCREPANCY 1:** This is a significant transaction that needs attention...";
  const aiUpdate2 = "**DISCREPANCY 2:** This appears to be an interest payment that wasn't recorded...";

  // Update the result with AI explanations (simulating streaming updates)
  currentResult = {
    ...currentResult,
    discrepancies: currentResult.discrepancies.map(disc => {
      if (disc.id === 'bank_bank_1') {
        return { ...disc, aiExplanation: aiUpdate1 };
      } else if (disc.id === 'bank_bank_2') {
        return { ...disc, aiExplanation: aiUpdate2 };
      }
      return disc;
    })
  };

  console.log('AI updates received during streaming:');
  currentResult.discrepancies.forEach(disc => {
    console.log(`- ${disc.id}: ${disc.aiExplanation.substring(0, 30)}...`);
  });

  // Step 3: onSuccess called with original data (potential overwrite)
  console.log('\nStep 3: onSuccess called with data that has placeholder AI explanations');
  const finalResultData = {
    id: 'test-reconciliation',
    uploadedAt: new Date().toISOString(),
    discrepancies: [
      { id: 'bank_bank_1', amount: ********, aiExplanation: "AI analysis in progress..." },
      { id: 'bank_bank_2', amount: 20000, aiExplanation: "AI analysis in progress..." }
    ]
  };

  // Use our fixed onSuccess handler
  const finalResult = simulateFixedOnSuccess(currentResult, finalResultData);

  // Step 4: Verify the results
  console.log('\nStep 4: Check final results to verify AI explanations were preserved');

  let success = true;
  finalResult.discrepancies.forEach(disc => {
    const hasRealAI = disc.aiExplanation !== "AI analysis in progress...";
    console.log(`- ${disc.id}: ${hasRealAI ? 'SUCCESS ✓' : 'FAILED ✗'} - ${disc.aiExplanation.substring(0, 30)}...`);
    if (!hasRealAI) success = false;
  });

  console.log(`\n=== TEST ${success ? 'PASSED ✓' : 'FAILED ✗'} ===`);
  return success;
}

// Run the test
testFix();
