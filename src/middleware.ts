import { NextRequest, NextResponse } from 'next/server'
import { createSecurityMiddleware, SecurityConfig } from '@/lib/security'

const securityMiddleware = createSecurityMiddleware()

export function middleware(request: NextRequest) {
  try {
    // Apply security validation
    securityMiddleware.validateRequest(request)

    // Set security headers
    const response = NextResponse.next()
    
    // Content Security Policy
    const csp = Object.entries(SecurityConfig.csp)
      .map(([directive, sources]) => `${directive.replace(/([A-Z])/g, '-$1').toLowerCase()} ${sources.join(' ')}`)
      .join('; ')
    
    response.headers.set('Content-Security-Policy', csp)
    response.headers.set('X-Frame-Options', 'DENY')
    response.headers.set('X-Content-Type-Options', 'nosniff')
    response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin')
    response.headers.set('Permissions-Policy', 'camera=(), microphone=(), geolocation=()')
    
    return response
  } catch (error) {
    console.error('Security middleware error:', error)
    return new NextResponse('Security validation failed', { status: 429 })
  }
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
}
