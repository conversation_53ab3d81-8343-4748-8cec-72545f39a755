'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { useSaveReconciliationReport } from '@/hooks/use-supabase-api';
import { ReconciliationResult } from '@/lib/schemas';
import { Database } from 'lucide-react';
import { Check, Loader2 } from 'lucide-react';
import { toast } from 'sonner';

interface SaveToDbButtonProps {
  result: ReconciliationResult;
  className?: string;
  onSuccess?: (reportId: string) => void;
  onError?: () => void;
  onSaving?: () => void;
}

export function SaveToDbButton({ result, className, onSuccess, onError, onSaving }: SaveToDbButtonProps) {
  const [saved, setSaved] = useState(false);
  const saveReportMutation = useSaveReconciliationReport();

  const handleSaveToDb = async () => {
    try {
      if (onSaving) onSaving();
      // Convert result to match the expected ReconciliationReport type
      const convertedResult = {
        ...result,
        matches: result.matches.map(match => ({
          ...match,
          bankTransaction: {
            ...match.bankTransaction,
            reference: match.bankTransaction.reference || '',
          },
          ledgerTransaction: {
            ...match.ledgerTransaction,
            reference: match.ledgerTransaction.reference || '',
          }
        })),
        discrepancies: result.discrepancies.map(d => {
          // Convert to the expected format
          if (d.type === 'missing_from_ledger') {
            return {
              id: d.id,
              type: d.type,
              bankTransaction: {
                ...d.transaction,
                reference: d.transaction.reference || '',
              },
              ledgerTransaction: null,
              aiExplanation: d.aiExplanation
            };
          } else if (d.type === 'missing_from_bank') {
            return {
              id: d.id,
              type: d.type,
              bankTransaction: null,
              ledgerTransaction: {
                ...d.transaction,
                reference: d.transaction.reference || '',
              },
              aiExplanation: d.aiExplanation
            };
          } else {
            // For other types, convert to missing_from_ledger as fallback
            return {
              id: d.id,
              type: 'missing_from_ledger' as const,
              bankTransaction: {
                ...d.transaction,
                reference: d.transaction.reference || '',
              },
              ledgerTransaction: null,
              aiExplanation: d.aiExplanation
            };
          }
        })
      };
      const reportId = await saveReportMutation.mutateAsync(convertedResult);
      setSaved(true);
      toast.success('Reconciliation saved to database successfully!');
      if (onSuccess && reportId) {
        onSuccess(reportId);
      }
    } catch (error) {
      console.error('Error saving to database:', error);
      toast.error('Failed to save to database. Please try again.');
      if (onError) onError();
    }
  };

  if (saved) {
    return (
      <Button variant="outline" className={className} disabled>
        <Check className="h-4 w-4 mr-2 text-green-500" />
        Saved to Database
      </Button>
    );
  }

  return (
    <Button
      variant="outline"
      className={className}
      onClick={handleSaveToDb}
      disabled={saveReportMutation.isPending}
    >
      {saveReportMutation.isPending ? (
        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
      ) : (
        <Database className="h-4 w-4 mr-2" />
      )}
      Save to Database
    </Button>
  );
}
