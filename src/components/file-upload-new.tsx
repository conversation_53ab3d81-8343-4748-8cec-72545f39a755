'use client'

import { useState, useCallback } from 'react'
import { useDropzone } from 'react-dropzone'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Progress } from '@/components/ui/progress'
import { Upload, FileText, X, AlertCircle, CheckCircle } from 'lucide-react'
import { toast } from 'sonner'
import { z } from 'zod'

// Define file validation schemas
const bankStatementSchema = z
  .instanceof(File)
  .refine((file) => {
    const validPdfTypes = ['application/pdf', 'application/x-pdf'];
    return validPdfTypes.includes(file.type) || file.name.toLowerCase().endsWith('.pdf');
  }, {
    message: 'Bank statement must be a PDF file',
  })
  .refine((file) => file.size <= 10 * 1024 * 1024, {
    message: 'Bank statement must be less than 10MB',
  });

const ledgerSchema = z
  .instanceof(File)
  .refine(
    (file) => {
      const validExcelTypes = [
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-excel',
        'text/csv',
        'application/csv',
        'text/x-csv',
        'application/x-csv'
      ];
      const fileName = file.name.toLowerCase();
      return validExcelTypes.includes(file.type) ||
             fileName.endsWith('.xlsx') ||
             fileName.endsWith('.xls') ||
             fileName.endsWith('.csv');
    },
    {
      message: 'Ledger must be an Excel (.xlsx, .xls) or CSV file',
    }
  )
  .refine((file) => file.size <= 5 * 1024 * 1024, {
    message: 'Ledger file must be less than 5MB',
  });

interface ProcessingData {
  bankTransactions?: number
  ledgerTransactions?: number
  matches?: number
  matchRate?: string
}

interface FileUploadProps {
  onFilesSelected: (files: { bankStatement: File; ledger: File }) => void
  isProcessing?: boolean
  processingStep?: string
  processingData?: ProcessingData | null
}

export function FileUploadNew({
  onFilesSelected,
  isProcessing = false,
  processingStep,
  processingData
}: FileUploadProps) {
  const [bankStatement, setBankStatement] = useState<File | null>(null)
  const [ledger, setLedger] = useState<File | null>(null)
  const [errors, setErrors] = useState<{ bankStatement?: string; ledger?: string }>({})

  // Bank statement dropzone
  const onBankStatementDrop = useCallback((acceptedFiles: File[]) => {
    console.log('Bank statement files dropped:', acceptedFiles);
    if (acceptedFiles.length > 0) {
      const file = acceptedFiles[0];
      console.log('Processing bank statement file:', file.name);

      try {
        bankStatementSchema.parse(file);
        setBankStatement(file);
        setErrors(prev => ({ ...prev, bankStatement: undefined }));
        toast.success(`✅ Bank statement "${file.name}" uploaded successfully!`, {
          duration: 3000,
          description: `File size: ${formatFileSize(file.size)}`
        });
      } catch (error) {
        if (error instanceof z.ZodError) {
          const errorMessage = error.issues[0]?.message || 'Invalid file';
          setErrors(prev => ({ ...prev, bankStatement: errorMessage }));
          toast.error(errorMessage);
        }
      }
    }
  }, []);

  const { getRootProps: getBankRootProps, getInputProps: getBankInputProps } = useDropzone({
    onDrop: onBankStatementDrop,
    accept: {
      'application/pdf': ['.pdf'],
      'application/x-pdf': ['.pdf'],
    },
    maxFiles: 1,
    disabled: isProcessing
  });

  // Ledger dropzone
  const onLedgerDrop = useCallback((acceptedFiles: File[]) => {
    console.log('Ledger files dropped:', acceptedFiles);
    if (acceptedFiles.length > 0) {
      const file = acceptedFiles[0];
      console.log('Processing ledger file:', file.name);

      try {
        ledgerSchema.parse(file);
        setLedger(file);
        setErrors(prev => ({ ...prev, ledger: undefined }));
        toast.success(`✅ Ledger "${file.name}" uploaded successfully!`, {
          duration: 3000,
          description: `File size: ${formatFileSize(file.size)}`
        });
      } catch (error) {
        if (error instanceof z.ZodError) {
          const errorMessage = error.issues[0]?.message || 'Invalid file';
          setErrors(prev => ({ ...prev, ledger: errorMessage }));
          toast.error(errorMessage);
        }
      }
    }
  }, []);

  const { getRootProps: getLedgerRootProps, getInputProps: getLedgerInputProps } = useDropzone({
    onDrop: onLedgerDrop,
    accept: {
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
      'application/vnd.ms-excel': ['.xls'],
      'text/csv': ['.csv'],
      'application/csv': ['.csv'],
      'text/x-csv': ['.csv'],
      'application/x-csv': ['.csv'],
    },
    maxFiles: 1,
    disabled: isProcessing
  });

  const removeFile = (type: 'bankStatement' | 'ledger') => {
    if (type === 'bankStatement') {
      setBankStatement(null);
      setErrors(prev => ({ ...prev, bankStatement: undefined }));
    } else {
      setLedger(null);
      setErrors(prev => ({ ...prev, ledger: undefined }));
    }
  };

  const handleSubmit = () => {
    console.log('Submit button clicked');
    console.log('Bank statement:', bankStatement);
    console.log('Ledger:', ledger);

    if (bankStatement && ledger) {
      try {
        // Final validation before submission
        bankStatementSchema.parse(bankStatement);
        ledgerSchema.parse(ledger);

        console.log('Files validated successfully, calling onFilesSelected');
        onFilesSelected({ bankStatement, ledger });
        toast.success('Files submitted for processing!');
      } catch (error) {
        console.error('Validation error during submission:', error);
        if (error instanceof z.ZodError) {
          error.issues.forEach((err: z.ZodIssue) => {
            toast.error(err.message);
          });
        }
      }
    } else {
      if (!bankStatement) toast.error('Please upload a bank statement');
      if (!ledger) toast.error('Please upload a ledger file');
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Add visual feedback when both files are uploaded
  const bothFilesUploaded = bankStatement && ledger;
  if (bothFilesUploaded && !isProcessing) {
    console.log('Both files uploaded successfully!');
  }

  return (
    <div className="space-y-4 sm:space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
        {/* Bank Statement Upload */}
        <Card className={`relative transition-all duration-200 ${
          errors.bankStatement ? 'border-destructive' :
          bankStatement ? 'border-green-500 bg-green-50' : 'border-dashed border-muted-foreground/25'
        }`}>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Bank Statement
            </CardTitle>
            <CardDescription>Upload your PDF bank statement</CardDescription>
          </CardHeader>
          <CardContent>
            {bankStatement ? (
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span className="text-sm font-medium truncate max-w-[200px]">
                      {bankStatement.name}
                    </span>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => removeFile('bankStatement')}
                    disabled={isProcessing}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="secondary">{formatFileSize(bankStatement.size)}</Badge>
                  <Badge variant="outline">{bankStatement.type.split('/')[1]?.toUpperCase() || 'PDF'}</Badge>
                </div>
              </div>
            ) : (
              <div
                {...getBankRootProps()}
                className="border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors hover:border-muted-foreground/50"
                style={{ position: 'relative' }}
              >
                <input
                  {...getBankInputProps()}
                  style={{ position: 'absolute', top: 0, right: 0, bottom: 0, left: 0, opacity: 0, width: '100%', height: '100%', cursor: 'pointer', zIndex: 10 }}
                />
                <Upload className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
                <p className="text-sm text-muted-foreground">
                  Drag & drop a PDF file here, or click to select
                </p>
              </div>
            )}
            {errors.bankStatement && (
              <Alert variant="destructive" className="mt-2">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{errors.bankStatement}</AlertDescription>
              </Alert>
            )}
          </CardContent>
        </Card>

        {/* Ledger Upload */}
        <Card className={`relative transition-all duration-200 ${
          errors.ledger ? 'border-destructive' :
          ledger ? 'border-green-500 bg-green-50' : 'border-dashed border-muted-foreground/25'
        }`}>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Accounting Ledger
            </CardTitle>
            <CardDescription>Upload your Excel or CSV ledger file</CardDescription>
          </CardHeader>
          <CardContent>
            {ledger ? (
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span className="text-sm font-medium truncate max-w-[200px]">
                      {ledger.name}
                    </span>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => removeFile('ledger')}
                    disabled={isProcessing}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="secondary">{formatFileSize(ledger.size)}</Badge>
                  <Badge variant="outline">{ledger.type.split('/')[1]?.toUpperCase() || 'SPREADSHEET'}</Badge>
                </div>
              </div>
            ) : (
              <div
                {...getLedgerRootProps()}
                className="border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors hover:border-muted-foreground/50"
                style={{ position: 'relative' }}
              >
                <input
                  {...getLedgerInputProps()}
                  style={{ position: 'absolute', top: 0, right: 0, bottom: 0, left: 0, opacity: 0, width: '100%', height: '100%', cursor: 'pointer', zIndex: 10 }}
                />
                <Upload className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
                <p className="text-sm text-muted-foreground">
                  Drag & drop an Excel or CSV file here, or click to select
                </p>
              </div>
            )}
            {errors.ledger && (
              <Alert variant="destructive" className="mt-2">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{errors.ledger}</AlertDescription>
              </Alert>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Success message when both files are uploaded */}
      {bothFilesUploaded && !isProcessing && (
        <Card className="border-green-500 bg-green-50">
          <CardContent className="pt-4 sm:pt-6">
            <div className="flex items-center gap-2 text-green-700">
              <CheckCircle className="h-5 w-5" />
              <span className="font-medium">Both files uploaded successfully!</span>
            </div>
            <p className="text-sm text-green-600 mt-1">
              Ready to start reconciliation process.
            </p>
          </CardContent>
        </Card>
      )}

      {isProcessing && (
        <Card>
          <CardContent className="pt-4 sm:pt-6">
            <div className="space-y-3">
              <div className="flex items-center justify-between text-sm">
                <span>{processingStep || 'Processing files...'}</span>
                <span className="hidden sm:inline">Please wait</span>
              </div>
              <Progress value={undefined} className="w-full" />
              {processingData && (
                <div className="text-xs text-muted-foreground space-y-1">
                  {processingData.bankTransactions && (
                    <div>Bank transactions: {processingData.bankTransactions}</div>
                  )}
                  {processingData.ledgerTransactions && (
                    <div>Ledger transactions: {processingData.ledgerTransactions}</div>
                  )}
                  {processingData.matches && (
                    <div>Matches found: {processingData.matches}</div>
                  )}
                  {processingData.matchRate && (
                    <div>Match rate: {processingData.matchRate}</div>
                  )}
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      <div className="flex justify-center">
        <Button
          onClick={handleSubmit}
          disabled={!bankStatement || !ledger || isProcessing}
          size="lg"
          className={`w-full sm:w-auto sm:min-w-[200px] max-w-sm transition-all duration-200 ${
            bothFilesUploaded && !isProcessing ? 'bg-green-600 hover:bg-green-700 animate-pulse' : ''
          }`}
        >
          {isProcessing ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Processing...
            </>
          ) : bothFilesUploaded ? (
            <>
              <CheckCircle className="h-4 w-4 mr-2" />
              Start Reconciliation
            </>
          ) : (
            'Start Reconciliation'
          )}
        </Button>
      </div>
    </div>
  )
}
