'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table'
import {
  CheckCircle,
  AlertTriangle,
  FileText,
  TrendingUp,
  TrendingDown,
  Brain,
  ChevronLeft,
  ChevronRight,
  Check,
  Loader2,
  Trash2
} from 'lucide-react'
import { ExportDialog } from './export-dialog'
import { SaveToDbButton } from './save-to-db-button'
import { UnsaveFromDbButton } from './unsave-from-db-button'
import { ReconciliationResult } from '@/lib/schemas'

interface ResultsDashboardProps {
  result: ReconciliationResult
  onNewReconciliation?: () => void
  savedReportId?: string
}

export function ResultsDashboard({
  result,
  onNewReconciliation,
  savedReportId
}: ResultsDashboardProps) {
  const { summary, matches = [], discrepancies = [] } = result
  const [currentPage, setCurrentPage] = useState(1)
  const [isSaved, setIsSaved] = useState(!!savedReportId);
  const [reportId, setReportId] = useState<string | undefined>(savedReportId);
  const [saveStatus, setSaveStatus] = useState<'idle' | 'saving' | 'saved' | 'error' | 'deleted'>(
    savedReportId ? 'saved' : 'idle'
  );
  const itemsPerPage = 10

  // Debug: Log discrepancies to see what we're getting
  console.log('🔍 ResultsDashboard received discrepancies:', discrepancies.length);
  console.log('🔍 AI explanations status:', discrepancies.map(d => ({
    id: d.id,
    hasAI: !!d.aiExplanation,
    isProgress: d.aiExplanation === "AI analysis in progress...",
    preview: d.aiExplanation?.substring(0, 50) + '...'
  })));

  // Calculate pagination for matches
  const totalPages = Math.ceil(matches.length / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const endIndex = startIndex + itemsPerPage
  const currentMatches = matches.slice(startIndex, endIndex)

  const handlePreviousPage = () => {
    setCurrentPage(prev => Math.max(prev - 1, 1))
  }

  const handleNextPage = () => {
    setCurrentPage(prev => Math.min(prev + 1, totalPages))
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-ET', {
      style: 'currency',
      currency: 'ETB',
    }).format(amount)
  }

  const getDiscrepancyIcon = (type: string) => {
    switch (type) {
      case 'missing_from_ledger':
        return <TrendingUp className="h-4 w-4 text-blue-500" />
      case 'missing_from_bank':
        return <TrendingDown className="h-4 w-4 text-orange-500" />
      case 'amount_mismatch':
        return <AlertTriangle className="h-4 w-4 text-red-500" />
      default:
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />
    }
  }

  const getDiscrepancyLabel = (type: string) => {
    switch (type) {
      case 'missing_from_ledger':
        return 'Missing from Ledger'
      case 'missing_from_bank':
        return 'Missing from Bank'
      case 'amount_mismatch':
        return 'Amount Mismatch'
      default:
        return 'Unknown'
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Reconciliation Results</h2>
          <div className="flex items-center gap-2">
            <p className="text-muted-foreground">
              Processed on {new Date(result.uploadedAt).toLocaleDateString()}
            </p>
            {saveStatus === 'saved' && (
              <span className="inline-flex items-center px-2 py-1 text-xs font-medium rounded-md bg-green-100 text-green-800">
                <Check className="h-3 w-3 mr-1" /> Saved to Database
              </span>
            )}
            {saveStatus === 'saving' && (
              <span className="inline-flex items-center px-2 py-1 text-xs font-medium rounded-md bg-blue-100 text-blue-800">
                <Loader2 className="h-3 w-3 mr-1 animate-spin" /> Saving...
              </span>
            )}
            {saveStatus === 'error' && (
              <span className="inline-flex items-center px-2 py-1 text-xs font-medium rounded-md bg-red-100 text-red-800">
                <AlertTriangle className="h-3 w-3 mr-1" /> Error Saving
              </span>
            )}
            {saveStatus === 'deleted' && (
              <span className="inline-flex items-center px-2 py-1 text-xs font-medium rounded-md bg-orange-100 text-orange-800">
                <Trash2 className="h-3 w-3 mr-1" /> Removed from Database
              </span>
            )}
          </div>
        </div>
        <div className="flex gap-2">
          {!isSaved ? (
            <SaveToDbButton 
              result={result} 
              onSuccess={(id) => {
                setIsSaved(true);
                setReportId(id);
                setSaveStatus('saved');
              }}
              onError={() => setSaveStatus('error')}
              onSaving={() => setSaveStatus('saving')}
            />
          ) : (
            <UnsaveFromDbButton 
              reportId={reportId!} 
              onSuccess={() => {
                setIsSaved(false);
                setReportId(undefined);
                setSaveStatus('deleted');
              }}
              onError={() => setSaveStatus('error')}
            />
          )}
          <ExportDialog result={result} />
          <Button onClick={onNewReconciliation}>
            New Reconciliation
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Matches</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{summary.matchedTransactions}</div>
            <p className="text-xs text-muted-foreground">
              {((summary.matchedTransactions / summary.totalBankTransactions) * 100).toFixed(1)}% match rate
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Discrepancies</CardTitle>
            <AlertTriangle className="h-4 w-4 text-yellow-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{summary.discrepancies}</div>
            <p className="text-xs text-muted-foreground">
              Require attention
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Bank Balance</CardTitle>
            <FileText className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(summary.bankBalance.closing)}</div>
            <p className="text-xs text-muted-foreground">
              Closing balance
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Balance Difference</CardTitle>
            <TrendingUp className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(Math.abs(summary.balanceDifference))}</div>
            <p className="text-xs text-muted-foreground">
              {summary.balanceDifference > 0 ? 'Bank higher' : 'Ledger higher'}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Balance Comparison */}
      <Card>
        <CardHeader>
          <CardTitle>Balance Comparison</CardTitle>
          <CardDescription>
            Detailed breakdown of bank vs ledger balances
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-semibold mb-3">Bank Statement</h4>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span>Opening Balance:</span>
                  <span>{formatCurrency(summary.bankBalance.opening)}</span>
                </div>
                <div className="flex justify-between">
                  <span>Total Credits:</span>
                  <span className="text-green-600">{formatCurrency(summary.bankBalance.totalCredits)}</span>
                </div>
                <div className="flex justify-between">
                  <span>Total Debits:</span>
                  <span className="text-red-600">{formatCurrency(Math.abs(summary.bankBalance.totalDebits))}</span>
                </div>
                <Separator />
                <div className="flex justify-between font-semibold">
                  <span>Closing Balance:</span>
                  <span>{formatCurrency(summary.bankBalance.closing)}</span>
                </div>
              </div>
            </div>
            <div>
              <h4 className="font-semibold mb-3">Ledger</h4>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span>Opening Balance:</span>
                  <span>{formatCurrency(summary.ledgerBalance.opening)}</span>
                </div>
                <div className="flex justify-between">
                  <span>Total Credits:</span>
                  <span className="text-green-600">{formatCurrency(summary.ledgerBalance.totalCredits)}</span>
                </div>
                <div className="flex justify-between">
                  <span>Total Debits:</span>
                  <span className="text-red-600">{formatCurrency(Math.abs(summary.ledgerBalance.totalDebits))}</span>
                </div>
                <Separator />
                <div className="flex justify-between font-semibold">
                  <span>Closing Balance:</span>
                  <span>{formatCurrency(summary.ledgerBalance.closing)}</span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Discrepancies */}
      {discrepancies.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Brain className="h-5 w-5" />
              AI-Identified Discrepancies
            </CardTitle>
            <CardDescription>
              Transactions that require your attention with AI-powered explanations
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {discrepancies.map((discrepancy) => (
                <div key={discrepancy.id} className="mb-4 border rounded-lg overflow-hidden bg-white shadow-sm">
                  <div className="p-4 md:p-6 w-full">
                    {/* Top Row - Badge, Amount, Description */}
                    <div className="flex flex-col md:flex-row md:items-center gap-4 mb-4 pb-4 border-b">
                      {/* Left: Badge */}
                      <div className="flex items-center gap-2">
                        {getDiscrepancyIcon(discrepancy.type)}
                        <Badge variant="outline" className="whitespace-nowrap">
                          {getDiscrepancyLabel(discrepancy.type)}
                        </Badge>
                      </div>

                      {/* Middle: Amount */}
                      <div className="md:ml-auto">
                        <span className="text-2xl font-bold text-primary">
                          {formatCurrency(Math.abs(discrepancy.transaction.amount))}
                        </span>
                      </div>
                    </div>

                    {/* Transaction Details */}
                    <div className="mb-4">
                      <p className="font-semibold text-lg">{discrepancy.transaction.description}</p>
                      <div className="flex flex-wrap gap-x-6 gap-y-1 mt-1 text-sm text-muted-foreground">
                        <span><strong>Reference:</strong> {discrepancy.transaction.reference || 'No reference'}</span>
                        <span><strong>Date:</strong> {new Date(discrepancy.transaction.date).toLocaleDateString()}</span>
                      </div>
                    </div>

                    {/* AI Analysis and Suggested Action - Full Width */}
                    <div className="space-y-4 w-full">
                      {/* AI Analysis Section */}
                      {discrepancy.aiExplanation && (
                        <div className="w-full bg-muted/50 p-4 rounded-md border-l-4 border-blue-500">
                          <div className="flex items-start gap-3">
                            <Brain className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
                            <div className="flex-1 w-full">
                              <p className="font-medium text-blue-900 mb-2">AI Analysis:</p>
                              <p className="text-sm text-gray-700 leading-relaxed">{discrepancy.aiExplanation}</p>
                              {/* Debug info */}
                              <p className="text-xs text-gray-500 mt-2">
                                Debug: ID={discrepancy.id}, Length={discrepancy.aiExplanation?.length},
                                IsProgress={discrepancy.aiExplanation === "AI analysis in progress..." ? 'YES' : 'NO'}
                              </p>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Suggested Action Section */}
                      {discrepancy.suggestedAction && (
                        <div className="w-full bg-blue-50 border border-blue-200 p-4 rounded-md">
                          <div className="flex items-start gap-3">
                            <CheckCircle className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
                            <div className="flex-1 w-full">
                              <p className="font-medium text-blue-900 mb-2">Suggested Action:</p>
                              <p className="text-sm text-blue-800 leading-relaxed">{discrepancy.suggestedAction}</p>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Matched Transactions */}
      {matches.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Matched Transactions</CardTitle>
            <CardDescription>
              Successfully matched transactions between bank and ledger
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Date</TableHead>
                  <TableHead>Bank Description</TableHead>
                  <TableHead>Bank Ref</TableHead>
                  <TableHead>Ledger Description</TableHead>
                  <TableHead>Ledger Ref</TableHead>
                  <TableHead className="text-right">Amount</TableHead>
                  <TableHead>Confidence</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {currentMatches.map((match) => (
                  <TableRow key={`${match.bankTransaction.id}-${match.ledgerTransaction.id}`}>
                    <TableCell>
                      <div className="space-y-1">
                        <div className="text-sm font-medium">
                          {new Date(match.bankTransaction.date).toLocaleDateString()}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {new Date(match.ledgerTransaction.date).toLocaleDateString()}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="max-w-[200px]">
                        <Badge variant="outline" className="mb-1 text-xs bg-blue-50 text-blue-700 border-blue-200">
                          BANK
                        </Badge>
                        <div className="text-sm truncate" title={match.bankTransaction.description}>
                          {match.bankTransaction.description}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm font-mono">
                        {match.bankTransaction.reference || 'N/A'}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="max-w-[200px]">
                        <Badge variant="outline" className="mb-1 text-xs bg-green-50 text-green-700 border-green-200">
                          LEDGER
                        </Badge>
                        <div className="text-sm truncate" title={match.ledgerTransaction.description}>
                          {match.ledgerTransaction.description}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm font-mono">
                        {match.ledgerTransaction.reference || 'N/A'}
                      </div>
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="font-semibold">
                        {formatCurrency(Math.abs(match.bankTransaction.amount))}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant={match.confidence >= 0.9 ? 'default' : 'secondary'}>
                        {(match.confidence * 100).toFixed(0)}%
                      </Badge>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>

            {/* Pagination Controls */}
            {matches.length > itemsPerPage && (
              <div className="flex items-center justify-between mt-4">
                <div className="text-sm text-muted-foreground">
                  Showing {startIndex + 1} to {Math.min(endIndex, matches.length)} of {matches.length} matches
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handlePreviousPage}
                    disabled={currentPage === 1}
                  >
                    <ChevronLeft className="h-4 w-4" />
                    Previous
                  </Button>

                  {/* Smart pagination - show first page, current page context, and last page */}
                  <div className="flex items-center gap-1">
                    {/* First page */}
                    {currentPage > 3 && (
                      <>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setCurrentPage(1)}
                          className="w-8 h-8 p-0"
                        >
                          1
                        </Button>
                        {currentPage > 4 && <span className="text-muted-foreground">...</span>}
                      </>
                    )}

                    {/* Pages around current page */}
                    {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                      let page;
                      if (totalPages <= 5) {
                        page = i + 1;
                      } else if (currentPage <= 3) {
                        page = i + 1;
                      } else if (currentPage >= totalPages - 2) {
                        page = totalPages - 4 + i;
                      } else {
                        page = currentPage - 2 + i;
                      }

                      if (page < 1 || page > totalPages) return null;
                      if (page === 1 && currentPage > 3) return null;
                      if (page === totalPages && currentPage < totalPages - 2) return null;

                      return (
                        <Button
                          key={page}
                          variant={currentPage === page ? 'default' : 'outline'}
                          size="sm"
                          onClick={() => setCurrentPage(page)}
                          className="w-8 h-8 p-0"
                        >
                          {page}
                        </Button>
                      );
                    })}

                    {/* Last page */}
                    {currentPage < totalPages - 2 && (
                      <>
                        {currentPage < totalPages - 3 && <span className="text-muted-foreground">...</span>}
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setCurrentPage(totalPages)}
                          className="w-8 h-8 p-0"
                        >
                          {totalPages}
                        </Button>
                      </>
                    )}
                  </div>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleNextPage}
                    disabled={currentPage === totalPages}
                  >
                    Next
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  )
}
