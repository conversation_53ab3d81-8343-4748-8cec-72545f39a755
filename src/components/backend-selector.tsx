'use client'

import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  Server, 
  Zap, 
  Shield, 
  Clock, 
  Info,
  CheckCircle,
  AlertTriangle
} from 'lucide-react'

export type BackendOption = 'nextjs' | 'fastapi' | 'e2b'

interface BackendSelectorProps {
  selectedBackend: BackendOption
  onBackendChange: (backend: BackendOption) => void
  fileSize?: number
  disabled?: boolean
}

export function BackendSelector({
  selectedBackend,
  onBackendChange,
  fileSize = 0,
  disabled = false
}: BackendSelectorProps) {
  const fileSizeMB = fileSize / (1024 * 1024)
  const VERCEL_LIMIT_MB = 7.5
  const isLargeFile = fileSizeMB > VERCEL_LIMIT_MB

  const backendOptions = [
    {
      id: 'nextjs' as BackendOption,
      name: 'Next.js API',
      description: 'Fast processing for smaller files',
      icon: Zap,
      features: ['Optimized for files < 7.5MB', 'Fastest processing', 'Real-time progress'],
      limitations: isLargeFile ? ['File too large for this option'] : [],
      recommended: !isLargeFile && fileSizeMB > 0,
      available: !isLargeFile,
      badge: 'Fast',
      badgeVariant: 'default' as const
    },
    {
      id: 'fastapi' as BackendOption,
      name: 'FastAPI Backend',
      description: 'Enhanced processing with Mistral OCR',
      icon: Server,
      features: ['Advanced PDF parsing', 'Mistral OCR integration', 'Handles large files'],
      limitations: [],
      recommended: isLargeFile,
      available: true,
      badge: 'Enhanced',
      badgeVariant: 'secondary' as const
    },
    {
      id: 'e2b' as BackendOption,
      name: 'E2B Secure Sandbox',
      description: 'Secure sandboxed processing (Beta)',
      icon: Shield,
      features: ['Maximum security', 'Isolated execution', 'Advanced analytics'],
      limitations: ['Beta feature', 'Longer processing time'],
      recommended: false,
      available: process.env.NEXT_PUBLIC_USE_E2B === 'true',
      badge: 'Beta',
      badgeVariant: 'outline' as const
    }
  ]

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Server className="h-5 w-5" />
          Processing Backend
        </CardTitle>
        <CardDescription>
          Choose how you want to process your files
          {fileSizeMB > 0 && (
            <span className="ml-2 text-sm">
              (Total file size: {fileSizeMB.toFixed(1)}MB)
            </span>
          )}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <RadioGroup
          value={selectedBackend}
          onValueChange={onBackendChange}
          disabled={disabled}
          className="space-y-4"
        >
          {backendOptions.map((option) => (
            <div key={option.id} className="relative">
              <div
                className={`flex items-start space-x-3 p-4 rounded-lg border transition-all ${
                  !option.available
                    ? 'opacity-50 cursor-not-allowed bg-muted/30'
                    : selectedBackend === option.id
                    ? 'border-primary bg-primary/5'
                    : 'border-border hover:border-primary/50'
                } ${option.recommended ? 'ring-2 ring-green-200' : ''}`}
              >
                <RadioGroupItem
                  value={option.id}
                  id={option.id}
                  disabled={!option.available || disabled}
                  className="mt-1"
                />
                <div className="flex-1 space-y-2">
                  <div className="flex items-center gap-2">
                    <Label
                      htmlFor={option.id}
                      className={`flex items-center gap-2 text-base font-medium cursor-pointer ${
                        !option.available ? 'cursor-not-allowed' : ''
                      }`}
                    >
                      <option.icon className="h-4 w-4" />
                      {option.name}
                    </Label>
                    <Badge variant={option.badgeVariant}>{option.badge}</Badge>
                    {option.recommended && (
                      <Badge variant="default" className="bg-green-600">
                        <CheckCircle className="h-3 w-3 mr-1" />
                        Recommended
                      </Badge>
                    )}
                  </div>
                  
                  <p className="text-sm text-muted-foreground">
                    {option.description}
                  </p>
                  
                  <div className="space-y-2">
                    <div className="flex flex-wrap gap-1">
                      {option.features.map((feature, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {feature}
                        </Badge>
                      ))}
                    </div>
                    
                    {option.limitations.length > 0 && (
                      <div className="flex flex-wrap gap-1">
                        {option.limitations.map((limitation, index) => (
                          <Badge key={index} variant="destructive" className="text-xs">
                            <AlertTriangle className="h-3 w-3 mr-1" />
                            {limitation}
                          </Badge>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </RadioGroup>

        {/* File size warning */}
        {isLargeFile && (
          <Alert className="mt-4">
            <Info className="h-4 w-4" />
            <AlertDescription>
              Your files are larger than {VERCEL_LIMIT_MB}MB. The Next.js API option is not available for large files.
              We recommend using the FastAPI backend for optimal performance.
            </AlertDescription>
          </Alert>
        )}

        {/* E2B availability notice */}
        {process.env.NEXT_PUBLIC_USE_E2B !== 'true' && (
          <Alert className="mt-4">
            <Info className="h-4 w-4" />
            <AlertDescription>
              E2B Secure Sandbox is currently disabled. To enable it, set NEXT_PUBLIC_USE_E2B=true in your environment variables.
            </AlertDescription>
          </Alert>
        )}

        {/* Processing time estimates */}
        <div className="mt-4 p-3 bg-muted/30 rounded-lg">
          <div className="flex items-center gap-2 text-sm font-medium mb-2">
            <Clock className="h-4 w-4" />
            Estimated Processing Time
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-2 text-xs text-muted-foreground">
            <div className="flex justify-between">
              <span>Next.js API:</span>
              <span>30-60s</span>
            </div>
            <div className="flex justify-between">
              <span>FastAPI:</span>
              <span>60-120s</span>
            </div>
            <div className="flex justify-between">
              <span>E2B Sandbox:</span>
              <span>90-180s</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
