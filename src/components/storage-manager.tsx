'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table'
import { 
  Trash2, 
  Download, 
  Clock, 
  FileText, 
  HardDrive,
  AlertCircle
} from 'lucide-react'
import { ReconciliationStorage, StoredReconciliation } from '@/lib/local-storage'
import { ReconciliationResult } from '@/lib/schemas'

interface StorageManagerProps {
  onLoadResult?: (result: ReconciliationResult) => void
}

export function StorageManager({ onLoadResult }: StorageManagerProps) {
  const [storedResults, setStoredResults] = useState<StoredReconciliation[]>([])
  const [storageInfo, setStorageInfo] = useState({ count: 0, totalSize: '0 KB' })

  useEffect(() => {
    loadStoredResults()
  }, [])

  const loadStoredResults = () => {
    const results = ReconciliationStorage.getAllResults()
    const info = ReconciliationStorage.getStorageInfo()
    setStoredResults(results)
    setStorageInfo(info)
  }

  const handleLoadResult = (result: ReconciliationResult) => {
    onLoadResult?.(result)
  }

  const handleDeleteResult = (id: string) => {
    ReconciliationStorage.deleteResult(id)
    loadStoredResults()
  }

  const handleClearAll = () => {
    if (confirm('Are you sure you want to delete all stored reconciliation results? This action cannot be undone.')) {
      ReconciliationStorage.clearAll()
      loadStoredResults()
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString()
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <HardDrive className="h-5 w-5" />
              Stored Reconciliations
            </CardTitle>
            <CardDescription>
              Previously processed reconciliation results stored locally
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="text-xs">
              {storageInfo.count} results • {storageInfo.totalSize}
            </Badge>
            {storedResults.length > 0 && (
              <Button 
                variant="destructive" 
                size="sm" 
                onClick={handleClearAll}
              >
                <Trash2 className="h-4 w-4 mr-1" />
                Clear All
              </Button>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {storedResults.length === 0 ? (
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              No stored reconciliation results found. Process some files to see them here.
            </AlertDescription>
          </Alert>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Date</TableHead>
                <TableHead>Bank Statement</TableHead>
                <TableHead>Ledger</TableHead>
                <TableHead>Matches</TableHead>
                <TableHead>Discrepancies</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {storedResults.map((stored) => (
                <TableRow key={stored.id}>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4 text-muted-foreground" />
                      <div className="text-sm">
                        {formatDate(stored.timestamp)}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <FileText className="h-4 w-4 text-blue-500" />
                      <div className="max-w-[150px] truncate text-sm" title={stored.bankFileName}>
                        {stored.bankFileName}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <FileText className="h-4 w-4 text-green-500" />
                      <div className="max-w-[150px] truncate text-sm" title={stored.ledgerFileName}>
                        {stored.ledgerFileName}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="default" className="text-xs">
                      {stored.result.summary.matchedTransactions}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Badge variant="secondary" className="text-xs">
                      {stored.result.summary.discrepancies}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex gap-1">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleLoadResult(stored.result)}
                        className="text-xs px-2 py-1"
                      >
                        <Download className="h-3 w-3 mr-1" />
                        Load
                      </Button>
                      <Button
                        variant="destructive"
                        size="sm"
                        onClick={() => handleDeleteResult(stored.id)}
                        className="text-xs px-2 py-1"
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </CardContent>
    </Card>
  )
}
