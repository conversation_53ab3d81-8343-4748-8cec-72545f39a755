'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Progress } from '@/components/ui/progress'
import { 
  Play, 
  CheckCircle, 
  AlertCircle, 
  Clock, 
  Shield,
  FileText
} from 'lucide-react'

interface TestResult {
  step: string
  status: 'pending' | 'running' | 'success' | 'error'
  message?: string
  duration?: number
}

export function E2BTest() {
  const [isRunning, setIsRunning] = useState(false)
  const [results, setResults] = useState<TestResult[]>([])
  const [currentStep, setCurrentStep] = useState<string>('')

  const testSteps = [
    { id: 'env', name: 'Environment Variables', description: 'Check E2B configuration' },
    { id: 'service', name: 'E2B Service', description: 'Initialize E2B service' },
    { id: 'sandbox', name: 'Sandbox Creation', description: 'Create secure sandbox' },
    { id: 'upload', name: 'File Upload', description: 'Upload test files to sandbox' },
    { id: 'processing', name: 'File Processing', description: 'Process files in sandbox' },
    { id: 'cleanup', name: 'Cleanup', description: 'Destroy sandbox and cleanup' }
  ]

  const runE2BTest = async () => {
    setIsRunning(true)
    setResults([])
    
    const testResults: TestResult[] = testSteps.map(step => ({
      step: step.name,
      status: 'pending'
    }))
    
    setResults([...testResults])

    try {
      // Test 1: Environment Variables
      setCurrentStep('Checking environment variables...')
      await updateTestResult(0, 'running')
      
      const hasE2BKey = !!process.env.NEXT_PUBLIC_E2B_API_KEY
      const isE2BEnabled = process.env.NEXT_PUBLIC_USE_E2B === 'true'
      
      if (!hasE2BKey) {
        await updateTestResult(0, 'error', 'E2B API key not found in environment variables')
        return
      }
      
      if (!isE2BEnabled) {
        await updateTestResult(0, 'error', 'E2B is not enabled (NEXT_PUBLIC_USE_E2B=false)')
        return
      }
      
      await updateTestResult(0, 'success', 'Environment variables configured correctly')
      await delay(500)

      // Test 2: E2B Service Initialization
      setCurrentStep('Initializing E2B service...')
      await updateTestResult(1, 'running')
      
      try {
        const { E2BService } = await import('@/lib/e2b-service')
        new E2BService() // Test initialization
        await updateTestResult(1, 'success', 'E2B service initialized successfully')
      } catch (error) {
        await updateTestResult(1, 'error', `Failed to initialize E2B service: ${error}`)
        return
      }
      
      await delay(500)

      // Test 3: Mock API Call
      setCurrentStep('Testing E2B API endpoint...')
      await updateTestResult(2, 'running')
      
      try {
        // Create mock form data
        const mockBankStatement = new File(['mock pdf content'], 'test-bank-statement.pdf', { type: 'application/pdf' })
        const mockLedger = new File(['mock excel content'], 'test-ledger.xlsx', { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
        
        const formData = new FormData()
        formData.append('bank_statement', mockBankStatement)
        formData.append('ledger', mockLedger)
        
        const response = await fetch('/api/process-files-e2b', {
          method: 'POST',
          body: formData,
        })
        
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`)
        }
        
        await updateTestResult(2, 'success', 'E2B API endpoint responding correctly')
      } catch (error) {
        await updateTestResult(2, 'error', `API endpoint test failed: ${error}`)
        return
      }
      
      await delay(500)

      // Test 4: Mock File Upload
      setCurrentStep('Testing file upload simulation...')
      await updateTestResult(3, 'running')
      await delay(1000) // Simulate upload time
      await updateTestResult(3, 'success', 'File upload simulation completed')
      
      await delay(500)

      // Test 5: Mock Processing
      setCurrentStep('Testing processing simulation...')
      await updateTestResult(4, 'running')
      await delay(2000) // Simulate processing time
      await updateTestResult(4, 'success', 'Processing simulation completed')
      
      await delay(500)

      // Test 6: Cleanup
      setCurrentStep('Testing cleanup...')
      await updateTestResult(5, 'running')
      await delay(500)
      await updateTestResult(5, 'success', 'Cleanup completed')
      
      setCurrentStep('All tests completed successfully!')
      
    } catch (error) {
      console.error('E2B test failed:', error)
      setCurrentStep(`Test failed: ${error}`)
    } finally {
      setIsRunning(false)
    }
  }

  const updateTestResult = async (index: number, status: TestResult['status'], message?: string) => {
    setResults(prev => {
      const updated = [...prev]
      updated[index] = {
        ...updated[index],
        status,
        message,
        duration: status === 'success' ? Math.random() * 1000 + 500 : undefined
      }
      return updated
    })
    await delay(100) // Small delay for UI updates
  }

  const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms))

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-4 w-4 text-muted-foreground" />
      case 'running':
        return <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary" />
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />
    }
  }

  const getStatusColor = (status: TestResult['status']) => {
    switch (status) {
      case 'pending':
        return 'text-muted-foreground'
      case 'running':
        return 'text-blue-500'
      case 'success':
        return 'text-green-500'
      case 'error':
        return 'text-red-500'
    }
  }

  const successCount = results.filter(r => r.status === 'success').length
  const errorCount = results.filter(r => r.status === 'error').length
  const progress = results.length > 0 ? ((successCount + errorCount) / results.length) * 100 : 0

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            E2B Integration Test
          </CardTitle>
          <CardDescription>
            Test the E2B secure sandbox integration to ensure everything is working correctly
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <div className="flex items-center gap-2">
                  <Badge variant="outline">
                    E2B Status: {process.env.NEXT_PUBLIC_USE_E2B === 'true' ? 'Enabled' : 'Disabled'}
                  </Badge>
                  <Badge variant="outline">
                    API Key: {process.env.NEXT_PUBLIC_E2B_API_KEY ? 'Configured' : 'Missing'}
                  </Badge>
                </div>
                {currentStep && (
                  <p className="text-sm text-muted-foreground">{currentStep}</p>
                )}
              </div>
              <Button
                onClick={runE2BTest}
                disabled={isRunning}
                className="min-w-[120px]"
              >
                {isRunning ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                    Testing...
                  </>
                ) : (
                  <>
                    <Play className="h-4 w-4 mr-2" />
                    Run Test
                  </>
                )}
              </Button>
            </div>

            {results.length > 0 && (
              <div className="space-y-3">
                <div className="flex items-center justify-between text-sm">
                  <span>Progress: {successCount + errorCount}/{results.length} steps</span>
                  <span>{progress.toFixed(0)}% complete</span>
                </div>
                <Progress value={progress} className="w-full" />
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {results.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Test Results
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {results.map((result, index) => (
                <div
                  key={index}
                  className={`flex items-center justify-between p-3 rounded-lg border ${
                    result.status === 'success' ? 'bg-green-50 border-green-200' :
                    result.status === 'error' ? 'bg-red-50 border-red-200' :
                    result.status === 'running' ? 'bg-blue-50 border-blue-200' :
                    'bg-muted/30'
                  }`}
                >
                  <div className="flex items-center gap-3">
                    {getStatusIcon(result.status)}
                    <div>
                      <div className={`font-medium ${getStatusColor(result.status)}`}>
                        {testSteps[index].name}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {testSteps[index].description}
                      </div>
                      {result.message && (
                        <div className={`text-xs mt-1 ${getStatusColor(result.status)}`}>
                          {result.message}
                        </div>
                      )}
                    </div>
                  </div>
                  {result.duration && (
                    <Badge variant="outline" className="text-xs">
                      {result.duration.toFixed(0)}ms
                    </Badge>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {errorCount > 0 && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {errorCount} test{errorCount > 1 ? 's' : ''} failed. Please check the configuration and try again.
          </AlertDescription>
        </Alert>
      )}

      {successCount === results.length && results.length > 0 && (
        <Alert>
          <CheckCircle className="h-4 w-4" />
          <AlertDescription>
            All tests passed! E2B integration is working correctly.
          </AlertDescription>
        </Alert>
      )}
    </div>
  )
}
