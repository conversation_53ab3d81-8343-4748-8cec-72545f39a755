'use client';

import { useState, useEffect } from 'react';
import { useSupabaseConnection, useReconciliationReports } from '@/hooks/use-supabase-api';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, XCircle, Database, FileText, Loader2, Server } from 'lucide-react';

export function SupabaseTest() {
  const [serverConnectionStatus, setServerConnectionStatus] = useState<{
    success: boolean;
    message?: string;
    error?: string;
    recordCount?: number;
    isLoading: boolean;
  }>({ success: false, isLoading: true });
  
  // Client-side connection test
  const { data: isConnected, isLoading: connectionLoading, error: connectionError } = useSupabaseConnection();
  
  // Server-side connection test
  useEffect(() => {
    const testServerConnection = async () => {
      try {
        const response = await fetch('/api/supabase', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            operation: 'test_connection'
          })
        });
        const data = await response.json();
        setServerConnectionStatus({
          success: data.success,
          message: data.message,
          error: data.error,
          recordCount: data.recordCount,
          isLoading: false
        });
      } catch (error) {
        setServerConnectionStatus({
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
          isLoading: false
        });
      }
    };
    
    testServerConnection();
  }, []);
  const { data: reports, isLoading: reportsLoading, error: reportsError, refetch } = useReconciliationReports(5);

  return (
    <div className="space-y-6 p-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">Supabase Integration Test</h2>
        <Button onClick={() => refetch()} variant="outline" size="sm">
          <Database className="w-4 h-4 mr-2" />
          Refresh
        </Button>
      </div>

      {/* Client-Side Connection Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="w-5 h-5" />
            Client-Side Database Connection
          </CardTitle>
          <CardDescription>
            Status of the browser connection to Supabase database
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-2">
            {connectionLoading ? (
              <>
                <Loader2 className="w-4 h-4 animate-spin" />
                <span>Testing connection...</span>
              </>
            ) : isConnected ? (
              <>
                <CheckCircle className="w-4 h-4 text-green-500" />
                <span className="text-green-700">Connected successfully</span>
                <Badge variant="secondary" className="ml-2">Online</Badge>
              </>
            ) : (
              <>
                <XCircle className="w-4 h-4 text-red-500" />
                <span className="text-red-700">Connection failed</span>
                <Badge variant="destructive" className="ml-2">Offline</Badge>
              </>
            )}
          </div>
          {connectionError && (
            <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-700">
              Error: {connectionError.message}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Server-Side Connection Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Server className="w-5 h-5" />
            Server-Side Database Connection
          </CardTitle>
          <CardDescription>
            Status of the API route connection to Supabase database
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-2">
            {serverConnectionStatus.isLoading ? (
              <>
                <Loader2 className="w-4 h-4 animate-spin" />
                <span>Testing server connection...</span>
              </>
            ) : serverConnectionStatus.success ? (
              <>
                <CheckCircle className="w-4 h-4 text-green-500" />
                <span className="text-green-700">{serverConnectionStatus.message}</span>
                <Badge variant="secondary" className="ml-2">Online</Badge>
              </>
            ) : (
              <>
                <XCircle className="w-4 h-4 text-red-500" />
                <span className="text-red-700">Server connection failed</span>
                <Badge variant="destructive" className="ml-2">Offline</Badge>
              </>
            )}
          </div>
          {serverConnectionStatus.error && (
            <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-700">
              Error: {serverConnectionStatus.error}
            </div>
          )}
          {serverConnectionStatus.success && serverConnectionStatus.recordCount !== undefined && (
            <div className="mt-2 p-2 bg-green-50 border border-green-200 rounded text-sm text-green-700">
              Found {serverConnectionStatus.recordCount} records in database
            </div>
          )}
        </CardContent>
      </Card>

      {/* Reports List */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="w-5 h-5" />
            Recent Reconciliation Reports
          </CardTitle>
          <CardDescription>
            Latest reports stored in the database
          </CardDescription>
        </CardHeader>
        <CardContent>
          {reportsLoading ? (
            <div className="flex items-center gap-2">
              <Loader2 className="w-4 h-4 animate-spin" />
              <span>Loading reports...</span>
            </div>
          ) : reportsError ? (
            <div className="p-2 bg-red-50 border border-red-200 rounded text-sm text-red-700">
              Error loading reports: {reportsError.message}
            </div>
          ) : reports && reports.length > 0 ? (
            <div className="space-y-3">
              {reports.map((report) => (
                <div key={report.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="space-y-1">
                    <div className="font-medium">{report.bankStatementFilename}</div>
                    <div className="text-sm text-gray-500">
                      Ledger: {report.ledgerFilename}
                    </div>
                    <div className="text-xs text-gray-400">
                      {new Date(report.uploadedAt).toLocaleString()}
                    </div>
                  </div>
                  <div className="text-right space-y-1">
                    <Badge variant="outline">{report.matchingRate}</Badge>
                    <div className="text-sm text-gray-500">
                      {report.matchedTransactions} matches, {report.discrepancies} discrepancies
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              <FileText className="w-12 h-12 mx-auto mb-4 opacity-50" />
              <p>No reconciliation reports found</p>
              <p className="text-sm">Process some files to see reports here</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* TanStack Query Status */}
      <Card>
        <CardHeader>
          <CardTitle>TanStack Query Status</CardTitle>
          <CardDescription>
            Caching and query management status
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="font-medium">Connection Query:</span>
              <Badge variant={connectionLoading ? "secondary" : isConnected ? "default" : "destructive"} className="ml-2">
                {connectionLoading ? "Loading" : isConnected ? "Cached" : "Error"}
              </Badge>
            </div>
            <div>
              <span className="font-medium">Reports Query:</span>
              <Badge variant={reportsLoading ? "secondary" : reports ? "default" : "destructive"} className="ml-2">
                {reportsLoading ? "Loading" : reports ? "Cached" : "Error"}
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
