'use client';

import { Badge } from '@/components/ui/badge';
import { HardDrive, Database, Cloud } from 'lucide-react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

interface StorageTypeBadgeProps {
  type: 'local' | 'database' | 'both';
  showLabel?: boolean;
  size?: 'sm' | 'md';
}

export function StorageTypeBadge({ 
  type, 
  showLabel = true,
  size = 'md' 
}: StorageTypeBadgeProps) {
  const iconSize = size === 'sm' ? 'h-3 w-3' : 'h-4 w-4';
  const textSize = size === 'sm' ? 'text-xs' : 'text-sm';
  
  const getContent = () => {
    switch (type) {
      case 'local':
        return {
          icon: <HardDrive className={`${iconSize} ${showLabel ? 'mr-1' : ''}`} />,
          label: 'Local Storage',
          variant: 'outline' as const,
          tooltip: 'Saved in browser local storage'
        };
      case 'database':
        return {
          icon: <Database className={`${iconSize} ${showLabel ? 'mr-1' : ''}`} />,
          label: 'Database',
          variant: 'default' as const,
          className: 'bg-blue-500',
          tooltip: 'Saved in database'
        };
      case 'both':
        return {
          icon: <Cloud className={`${iconSize} ${showLabel ? 'mr-1' : ''}`} />,
          label: 'Both Storages',
          variant: 'default' as const,
          className: 'bg-green-600',
          tooltip: 'Saved in both local storage and database'
        };
    }
  };

  const content = getContent();

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Badge 
            variant={content.variant} 
            className={`whitespace-nowrap ${textSize} ${content.className || ''}`}
          >
            {content.icon}
            {showLabel && content.label}
          </Badge>
        </TooltipTrigger>
        <TooltipContent>
          <p>{content.tooltip}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
