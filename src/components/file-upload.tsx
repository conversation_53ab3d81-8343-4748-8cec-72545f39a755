'use client'

import { useC<PERSON>back, useState, useEffect } from 'react'
import { useDropzone, DropzoneRootProps, DropzoneInputProps } from 'react-dropzone'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Progress } from '@/components/ui/progress'
import { Upload, FileText, X, AlertCircle, CheckCircle } from 'lucide-react'
import { cn } from '@/lib/utils'
import { useErrorHandler, FileValidation } from '@/lib/error-handler'
import { toast } from 'sonner'
import { FileUploadSchema } from '@/lib/schemas'
import { z } from 'zod'

interface FileUploadProps {
  onFilesSelected: (files: { bankStatement: File; ledger: File }) => void
  isProcessing?: boolean
}

export function FileUpload({ onFilesSelected, isProcessing = false }: FileUploadProps) {
  const [bankStatement, setBankStatement] = useState<File | null>(null)
  const [ledger, setLedger] = useState<File | null>(null)
  const [errors, setErrors] = useState<{ bankStatement?: string; ledger?: string }>({})
  const [status, setStatus] = useState<{ bankStatement: string; ledger: string }>({
    bankStatement: 'idle',
    ledger: 'idle'
  })
  const [isDragActive, setIsDragActive] = useState(false)
  const { handleError } = useErrorHandler()

  const updateStatus = (type: 'bankStatement' | 'ledger', value: string) => {
    setStatus(prev => ({ ...prev, [type]: value }))
  }

  const validateAndSetFile = useCallback((file: File, type: 'bankStatement' | 'ledger') => {
    try {
      console.log(`Validating ${type} file:`, file.name, file.type);
      updateStatus(type, 'validating')

      if (type === 'bankStatement') {
        // Check if it's a PDF file by extension if MIME type is not detected
        if (!file.type && file.name.toLowerCase().endsWith('.pdf')) {
          console.log('PDF detected by extension');
          setBankStatement(file);
          setErrors(prev => ({ ...prev, bankStatement: undefined }));
          updateStatus('bankStatement', 'selected')
          return;
        }

        FileUploadSchema.shape.bankStatement.parse(file);
        setBankStatement(file);
        setErrors(prev => ({ ...prev, bankStatement: undefined }));
        updateStatus('bankStatement', 'selected')
      } else {
        // Check if it's a valid ledger file by extension if MIME type is not detected
        const fileName = file.name.toLowerCase();
        if (!file.type && (fileName.endsWith('.xlsx') || fileName.endsWith('.xls') || fileName.endsWith('.csv'))) {
          console.log('Excel/CSV detected by extension');
          setLedger(file);
          setErrors(prev => ({ ...prev, ledger: undefined }));
          updateStatus('ledger', 'selected')
          return;
        }

        FileUploadSchema.shape.ledger.parse(file);
        setLedger(file);
        setErrors(prev => ({ ...prev, ledger: undefined }));
        updateStatus('ledger', 'selected')
      }
    } catch (error) {
      console.error(`Validation error for ${type}:`, error);
      if (error instanceof z.ZodError) {
        const errorMessage = error.issues[0]?.message || 'Invalid file';
        setErrors(prev => ({ ...prev, [type]: errorMessage }));
        updateStatus(type, 'error')
        toast.error(errorMessage);
      }
    }
  }, [])

  const onBankStatementDrop = useCallback((acceptedFiles: File[]) => {
    console.log('Bank statement files dropped:', acceptedFiles);
    if (acceptedFiles.length > 0) {
      console.log('Processing bank statement file:', acceptedFiles[0].name);
      validateAndSetFile(acceptedFiles[0], 'bankStatement')
    }
  }, [validateAndSetFile])

  const onLedgerDrop = useCallback((acceptedFiles: File[]) => {
    console.log('Ledger files dropped:', acceptedFiles);
    if (acceptedFiles.length > 0) {
      console.log('Processing ledger file:', acceptedFiles[0].name);
      validateAndSetFile(acceptedFiles[0], 'ledger')
    }
  }, [validateAndSetFile])

  const {
    getRootProps: getBankRootProps,
    getInputProps: getBankInputProps,
    isDragActive: isBankDragActive,
  } = useDropzone({
    onDrop: onBankStatementDrop,
    accept: {
      'application/pdf': ['.pdf'],
      'application/x-pdf': ['.pdf'],
    },
    maxFiles: 1,
    disabled: isProcessing
  })

  const {
    getRootProps: getLedgerRootProps,
    getInputProps: getLedgerInputProps,
    isDragActive: isLedgerDragActive,
  } = useDropzone({
    onDrop: onLedgerDrop,
    accept: {
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
      'application/vnd.ms-excel': ['.xls'],
      'text/csv': ['.csv'],
      'application/csv': ['.csv'],
      'text/x-csv': ['.csv'],
      'application/x-csv': ['.csv'],
    },
    maxFiles: 1,
    disabled: isProcessing
  })

  const removeFile = (type: 'bankStatement' | 'ledger') => {
    if (type === 'bankStatement') {
      setBankStatement(null)
      setErrors(prev => ({ ...prev, bankStatement: undefined }))
    } else {
      setLedger(null)
      setErrors(prev => ({ ...prev, ledger: undefined }))
    }
  }

  useEffect(() => {
    console.log('Bank statement updated:', bankStatement);
    console.log('Ledger updated:', ledger);
  }, [bankStatement, ledger]);

  const handleSubmit = () => {
    console.log('Submit button clicked');
    console.log('Bank statement:', bankStatement);
    console.log('Ledger:', ledger);

    if (bankStatement && ledger) {
      try {
        console.log('Validating files before submission');
        FileUploadSchema.parse({ bankStatement, ledger });
        console.log('Files validated successfully, calling onFilesSelected');
        updateStatus('bankStatement', 'uploading')
        updateStatus('ledger', 'uploading')
        onFilesSelected({ bankStatement, ledger });
        updateStatus('bankStatement', 'processing')
        updateStatus('ledger', 'processing')
        toast.success('Files submitted for processing!');
      } catch (error) {
        console.error('Validation error during submission:', error);
        if (error instanceof z.ZodError) {
          error.issues.forEach((err: z.ZodIssue) => {
            toast.error(err.message);
          });
          updateStatus('bankStatement', bankStatement ? 'selected' : 'idle')
          updateStatus('ledger', ledger ? 'selected' : 'idle')
        }
      }
    } else {
      if (!bankStatement) toast.error('Please upload a bank statement');
      if (!ledger) toast.error('Please upload a ledger file');
    }
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const FileCard = ({
    file,
    type,
    error,
    getRootProps,
    getInputProps,
    isDragActive,
    title,
    description
  }: {
    file: File | null
    type: 'bankStatement' | 'ledger'
    error?: string
    getRootProps: () => DropzoneRootProps
    getInputProps: () => DropzoneInputProps
    isDragActive: boolean
    title: string
    description: string
  }) => {
    console.log(`Rendering ${title} FileCard:`, { file, isDragActive, error });
    return (
    <Card className={`relative transition-all duration-200 ${
      isDragActive ? 'border-primary bg-primary/5' :
      error ? 'border-destructive' :
      file ? 'border-green-500 bg-green-50' : 'border-dashed border-muted-foreground/25'
    }`}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FileText className="h-5 w-5" />
          {title}
        </CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent>
        {file ? (
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-500" />
                <span className="text-sm font-medium truncate max-w-[200px]">
                  {file.name}
                </span>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => removeFile(type)}
                disabled={isProcessing}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant="secondary">{formatFileSize(file.size)}</Badge>
              <Badge variant="outline">{file.type.split('/')[1]?.toUpperCase() || 'FILE'}</Badge>
              <Badge
                variant={status[type] === 'error' ? 'destructive' : status[type] === 'processing' ? 'outline' : 'secondary'}
                className={
                  status[type] === 'uploading' ? 'animate-pulse' : ''
                }
              >
                {status[type] === 'idle' && 'Idle'}
                {status[type] === 'validating' && 'Validating'}
                {status[type] === 'selected' && 'Ready'}
                {status[type] === 'uploading' && 'Uploading'}
                {status[type] === 'processing' && 'Processing'}
                {status[type] === 'error' && 'Error'}
                {status[type] === 'done' && 'Done'}
              </Badge>
            </div>
          </div>
        ) : (
          <div
            {...getRootProps()}
            className={`border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors ${
              isDragActive ? 'border-primary bg-primary/5' : 'border-muted-foreground/25 hover:border-muted-foreground/50'
            }`}
            style={{ position: 'relative' }}
            onClick={() => console.log(`${title} upload area clicked`)}
          >
            <input
              {...getInputProps()}
              style={{ position: 'absolute', top: 0, right: 0, bottom: 0, left: 0, opacity: 0, width: '100%', height: '100%', cursor: 'pointer', zIndex: 10 }}
            />
            <Upload className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
            <p className="text-sm text-muted-foreground">
              {isDragActive ? 'Drop the file here' : 'Drag & drop a file here, or click to select'}
            </p>
          </div>
        )}
        {error && (
          <Alert variant="destructive" className="mt-2">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}
      </CardContent>
    </Card>
  );
  }

  // Debug render
  useEffect(() => {
    console.log('Rendering FileUpload component');
    console.log('Bank statement state:', bankStatement ? bankStatement.name : 'null');
    console.log('Ledger state:', ledger ? ledger.name : 'null');
  }, [bankStatement, ledger]);

  return (
    <div className="space-y-4 sm:space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
        <FileCard
          file={bankStatement}
          type="bankStatement"
          error={errors.bankStatement}
          getRootProps={getBankRootProps}
          getInputProps={getBankInputProps}
          isDragActive={isBankDragActive}
          title="Bank Statement"
          description="Upload your PDF bank statement"
        />
        <FileCard
          file={ledger}
          type="ledger"
          error={errors.ledger}
          getRootProps={getLedgerRootProps}
          getInputProps={getLedgerInputProps}
          isDragActive={isLedgerDragActive}
          title="Accounting Ledger"
          description="Upload your Excel or CSV ledger file"
        />
      </div>

  {isProcessing && (
        <Card>
          <CardContent className="pt-4 sm:pt-6">
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>Processing files...</span>
                <span className="hidden sm:inline">Please wait</span>
              </div>
              <Progress value={undefined} className="w-full" />
            </div>
          </CardContent>
        </Card>
      )}

      <div className="flex justify-center">
  <Button
          onClick={handleSubmit}
          disabled={!bankStatement || !ledger || isProcessing}
          size="lg"
          className="w-full sm:w-auto sm:min-w-[200px] max-w-sm"
        >
          {isProcessing ? 'Processing...' : 'Start Reconciliation'}
        </Button>
      </div>
    </div>
  )
}
