'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { Label } from '@/components/ui/label'
import { Checkbox } from '@/components/ui/checkbox'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Download, FileSpreadsheet, FileText, FileJson, FileImage } from 'lucide-react'
import { ReconciliationResult } from '@/lib/schemas'
import { exportReconciliationResults, generateExportFilename, downloadFile, ExportOptions } from '@/lib/export-utils'
import { useToast } from '../hooks/use-toast'

interface ExportDialogProps {
  result: ReconciliationResult
}

export function ExportDialog({ result }: ExportDialogProps) {
  const [open, setOpen] = useState(false)
  const [format, setFormat] = useState<'excel' | 'csv' | 'pdf' | 'json'>('excel')
  const [includeMatches, setIncludeMatches] = useState(true)
  const [includeDiscrepancies, setIncludeDiscrepancies] = useState(true)
  const [includeSummary, setIncludeSummary] = useState(true)
  const [isExporting, setIsExporting] = useState(false)
  const { toast } = useToast()

  const formatOptions = [
    { value: 'excel', label: 'Excel (.xlsx)', icon: FileSpreadsheet },
    { value: 'csv', label: 'CSV (.csv)', icon: FileText },
    { value: 'json', label: 'JSON (.json)', icon: FileJson },
    { value: 'pdf', label: 'PDF Report (.html)', icon: FileImage },
  ] as const

  const handleExport = async () => {
    try {
      setIsExporting(true)

      const options: ExportOptions = {
        format,
        includeMatches,
        includeDiscrepancies,
        includeSummary,
      }

      // Validate that at least one section is selected
      if (!includeMatches && !includeDiscrepancies && !includeSummary) {
        toast({
          title: 'Export Error',
          description: 'Please select at least one section to export.',
          variant: 'destructive',
        })
        return
      }

      // Generate the export
      const blob = exportReconciliationResults(result, options)
      const filename = generateExportFilename(format)

      // Download the file
      downloadFile(blob, filename)

      toast({
        title: 'Export Successful',
        description: `Reconciliation report exported as ${filename}`,
      })

      setOpen(false)
    } catch (error) {
      console.error('Export error:', error)
      toast({
        title: 'Export Failed',
        description: 'Failed to export reconciliation report. Please try again.',
        variant: 'destructive',
      })
    } finally {
      setIsExporting(false)
    }
  }

  const selectedFormatOption = formatOptions.find(option => option.value === format)
  const FormatIcon = selectedFormatOption?.icon || FileText

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" className="gap-2">
          <Download className="h-4 w-4" />
          Export Results
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Download className="h-5 w-5" />
            Export Reconciliation Results
          </DialogTitle>
          <DialogDescription>
            Choose the format and sections to include in your export.
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-6">
          {/* Format Selection */}
          <div className="space-y-2">
            <Label htmlFor="format">Export Format</Label>
            <Select value={format} onValueChange={(value: 'excel' | 'csv' | 'pdf' | 'json') => setFormat(value)}>
              <SelectTrigger>
                <SelectValue>
                  <div className="flex items-center gap-2">
                    <FormatIcon className="h-4 w-4" />
                    {selectedFormatOption?.label}
                  </div>
                </SelectValue>
              </SelectTrigger>
              <SelectContent>
                {formatOptions.map((option) => {
                  const Icon = option.icon
                  return (
                    <SelectItem key={option.value} value={option.value}>
                      <div className="flex items-center gap-2">
                        <Icon className="h-4 w-4" />
                        {option.label}
                      </div>
                    </SelectItem>
                  )
                })}
              </SelectContent>
            </Select>
          </div>

          {/* Content Selection */}
          <div className="space-y-3">
            <Label>Include Sections</Label>
            
            <div className="flex items-center space-x-2">
              <Checkbox
                id="summary"
                checked={includeSummary}
                onCheckedChange={(checked) => setIncludeSummary(checked === true)}
              />
              <Label htmlFor="summary" className="text-sm font-normal">
                Summary & Statistics
              </Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="matches"
                checked={includeMatches}
                onCheckedChange={(checked) => setIncludeMatches(checked === true)}
              />
              <Label htmlFor="matches" className="text-sm font-normal">
                Matched Transactions ({result.matches.length})
              </Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="discrepancies"
                checked={includeDiscrepancies}
                onCheckedChange={(checked) => setIncludeDiscrepancies(checked === true)}
              />
              <Label htmlFor="discrepancies" className="text-sm font-normal">
                Discrepancies ({result.discrepancies.length})
              </Label>
            </div>
          </div>

          {/* Format-specific notes */}
          <div className="text-xs text-muted-foreground bg-muted/50 p-3 rounded-md">
            {format === 'excel' && (
              <p>Excel format will create separate sheets for summary, matches, and discrepancies.</p>
            )}
            {format === 'csv' && (
              <p>CSV format will include all selected sections in a single file with clear headers.</p>
            )}
            {format === 'json' && (
              <p>JSON format provides structured data suitable for programmatic processing.</p>
            )}
            {format === 'pdf' && (
              <p>PDF format generates an HTML report that can be printed or converted to PDF.</p>
            )}
          </div>
        </div>

        <DialogFooter className="flex-col sm:flex-row gap-2">
          <Button variant="outline" onClick={() => setOpen(false)}>
            Cancel
          </Button>
          <Button 
            onClick={handleExport} 
            disabled={isExporting || (!includeMatches && !includeDiscrepancies && !includeSummary)}
            className="gap-2"
          >
            {isExporting ? (
              <>
                <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                Exporting...
              </>
            ) : (
              <>
                <Download className="h-4 w-4" />
                Export {selectedFormatOption?.label.split(' ')[0]}
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
