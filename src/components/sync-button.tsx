'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { useSaveReconciliationReport } from '@/hooks/use-supabase-api';
import { ReconciliationStorage, StoredReconciliation } from '@/lib/local-storage';
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Cloud, Loader2, Check, AlertCircle } from 'lucide-react';
import { toast } from 'sonner';

export function SyncButton() {
  const [open, setOpen] = useState(false);
  const [syncing, setSyncing] = useState(false);
  const [syncStatus, setSyncStatus] = useState<Record<string, 'pending' | 'success' | 'error'>>({});
  const [localReports, setLocalReports] = useState<StoredReconciliation[]>([]);
  
  const saveReportMutation = useSaveReconciliationReport();

  const handleOpenSync = () => {
    const reports = ReconciliationStorage.getAllResults();
    setLocalReports(reports);
    setSyncStatus(reports.reduce((acc, report) => {
      acc[report.id] = 'pending';
      return acc;
    }, {} as Record<string, 'pending' | 'success' | 'error'>));
    setOpen(true);
  };

  const handleSyncAll = async () => {
    if (localReports.length === 0) return;
    
    setSyncing(true);
    let successCount = 0;
    let errorCount = 0;
    
    for (const report of localReports) {
      try {
        // Convert result to match the expected ReconciliationReport type
        const convertedResult = {
          ...report.result,
          matches: report.result.matches.map(match => ({
            ...match,
            bankTransaction: {
              ...match.bankTransaction,
              reference: match.bankTransaction.reference || '',
            },
            ledgerTransaction: {
              ...match.ledgerTransaction,
              reference: match.ledgerTransaction.reference || '',
            }
          })),
          discrepancies: report.result.discrepancies.map(d => {
            // Convert to the expected format
            if (d.type === 'missing_from_ledger') {
              return {
                id: d.id,
                type: d.type,
                bankTransaction: {
                  ...d.transaction,
                  reference: d.transaction.reference || '',
                },
                ledgerTransaction: null,
                aiExplanation: d.aiExplanation
              };
            } else if (d.type === 'missing_from_bank') {
              return {
                id: d.id,
                type: d.type,
                bankTransaction: null,
                ledgerTransaction: {
                  ...d.transaction,
                  reference: d.transaction.reference || '',
                },
                aiExplanation: d.aiExplanation
              };
            } else {
              // For other types, convert to missing_from_ledger as fallback
              return {
                id: d.id,
                type: 'missing_from_ledger' as const,
                bankTransaction: {
                  ...d.transaction,
                  reference: d.transaction.reference || '',
                },
                ledgerTransaction: null,
                aiExplanation: d.aiExplanation
              };
            }
          })
        };
        
        await saveReportMutation.mutateAsync(convertedResult);
        setSyncStatus(prev => ({ ...prev, [report.id]: 'success' }));
        successCount++;
      } catch (error) {
        console.error(`Error syncing report ${report.id}:`, error);
        setSyncStatus(prev => ({ ...prev, [report.id]: 'error' }));
        errorCount++;
      }
      
      // Small delay to avoid overwhelming the API
      await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    setSyncing(false);
    
    if (successCount > 0) {
      toast.success(`Successfully synced ${successCount} report${successCount !== 1 ? 's' : ''} to database`);
    }
    
    if (errorCount > 0) {
      toast.error(`Failed to sync ${errorCount} report${errorCount !== 1 ? 's' : ''}`);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const getStatusIcon = (status: 'pending' | 'success' | 'error') => {
    switch (status) {
      case 'success':
        return <Check className="h-4 w-4 text-green-500" />;
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return null;
    }
  };

  return (
    <>
      <Button 
        variant="outline" 
        className="flex items-center gap-2"
        onClick={handleOpenSync}
      >
        <Cloud className="h-4 w-4" />
        Sync to Database
      </Button>
      
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Sync Local Reports to Database</DialogTitle>
            <DialogDescription>
              Select reports to sync from local storage to the database for permanent storage
            </DialogDescription>
          </DialogHeader>
          
          {localReports.length === 0 ? (
            <div className="py-8 text-center">
              <p className="text-muted-foreground">No local reports found to sync</p>
            </div>
          ) : (
            <div className="max-h-[400px] overflow-y-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Date</TableHead>
                    <TableHead>Bank Statement</TableHead>
                    <TableHead>Ledger</TableHead>
                    <TableHead className="text-center">Status</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {localReports.map((report) => (
                    <TableRow key={report.id}>
                      <TableCell>{formatDate(report.timestamp)}</TableCell>
                      <TableCell className="max-w-[200px] truncate" title={report.bankFileName}>
                        {report.bankFileName}
                      </TableCell>
                      <TableCell className="max-w-[200px] truncate" title={report.ledgerFileName}>
                        {report.ledgerFileName}
                      </TableCell>
                      <TableCell className="text-center">
                        {syncStatus[report.id] === 'pending' ? (
                          syncing ? <Loader2 className="h-4 w-4 animate-spin mx-auto" /> : 
                          <Badge variant="outline">Pending</Badge>
                        ) : (
                          <div className="flex items-center justify-center">
                            {getStatusIcon(syncStatus[report.id])}
                            <Badge 
                              variant={syncStatus[report.id] === 'success' ? 'default' : 'destructive'}
                              className="ml-2"
                            >
                              {syncStatus[report.id] === 'success' ? 'Synced' : 'Failed'}
                            </Badge>
                          </div>
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setOpen(false)}>
              Cancel
            </Button>
            <Button 
              onClick={handleSyncAll} 
              disabled={syncing || localReports.length === 0}
            >
              {syncing ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Syncing...
                </>
              ) : (
                <>
                  <Cloud className="h-4 w-4 mr-2" />
                  Sync All to Database
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
