'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Trash2, Loader2, HardDrive, Database, Cloud } from 'lucide-react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { toast } from 'sonner';
import { useDeleteReconciliationReport } from '@/hooks/use-delete-report';
import { ReconciliationStorage } from '@/lib/local-storage';

interface DeleteReportButtonProps {
  reportId: string;
  storageType: 'local' | 'database' | 'both';
  onSuccess?: () => void;
  onError?: () => void;
}

export function DeleteReportButton({
  reportId,
  storageType,
  onSuccess,
  onError
}: DeleteReportButtonProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const deleteDbReport = useDeleteReconciliationReport();

  const handleDelete = async () => {
    setIsDeleting(true);
    
    try {
      // Delete from local storage if needed
      if (storageType === 'local' || storageType === 'both') {
        ReconciliationStorage.deleteResult(reportId);
      }
      
      // Delete from database if needed
      if (storageType === 'database' || storageType === 'both') {
        await deleteDbReport.mutateAsync(reportId);
      }
      
      // Show appropriate success message based on storage type
      if (storageType === 'both') {
        toast.success('Report deleted from both local storage and database');
      } else if (storageType === 'local') {
        toast.success('Report deleted from local storage');
      } else {
        toast.success('Report deleted from database');
      }
      
      setIsOpen(false);
      onSuccess?.();
    } catch (error) {
      console.error('Failed to delete report:', error);
      toast.error('Failed to delete report');
      onError?.();
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <AlertDialog open={isOpen} onOpenChange={setIsOpen}>
      <AlertDialogTrigger asChild>
        <Button 
          variant="outline" 
          size="icon"
          className="h-7 w-7 sm:h-8 sm:w-8 text-red-500 hover:text-red-700 hover:bg-red-50 p-1 sm:p-2"
        >
          <Trash2 className="h-3 w-3 sm:h-4 sm:w-4" />
        </Button>
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Delete Reconciliation Report</AlertDialogTitle>
          <AlertDialogDescription>
            Are you sure you want to delete this report? This action cannot be undone.
            
            <div className="flex items-center gap-2 mt-3 p-2 rounded-md border">
              {storageType === 'local' && (
                <>
                  <HardDrive className="h-4 w-4 text-orange-500" />
                  <span className="font-medium">This will remove the report from local storage.</span>
                </>
              )}
              {storageType === 'database' && (
                <>
                  <Database className="h-4 w-4 text-blue-500" />
                  <span className="font-medium">This will remove the report from the database.</span>
                </>
              )}
              {storageType === 'both' && (
                <>
                  <Cloud className="h-4 w-4 text-amber-500" />
                  <span className="font-medium text-amber-600">
                    This will remove the report from both local storage and database.
                  </span>
                </>
              )}
            </div>
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel disabled={isDeleting}>Cancel</AlertDialogCancel>
          <AlertDialogAction
            onClick={(e) => {
              e.preventDefault();
              handleDelete();
            }}
            disabled={isDeleting}
            className="bg-red-500 hover:bg-red-600"
          >
            {isDeleting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Deleting...
              </>
            ) : (
              'Delete'
            )}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
