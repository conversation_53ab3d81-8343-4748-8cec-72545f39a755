'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Trash2, Loader2 } from 'lucide-react';
import { toast } from 'sonner';
import { useDeleteReconciliationReport } from '@/hooks/use-delete-report';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';

interface UnsaveFromDbButtonProps {
  reportId: string;
  onSuccess?: () => void;
  onError?: () => void;
  className?: string;
}

export function UnsaveFromDbButton({ reportId, onSuccess, onError, className }: UnsaveFromDbButtonProps) {
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const deleteReportMutation = useDeleteReconciliationReport();

  const handleDelete = async () => {
    try {
      await deleteReportMutation.mutateAsync(reportId);
      if (onSuccess) {
        onSuccess();
      }
      setShowConfirmDialog(false);
    } catch (error) {
      console.error('Error removing report from database:', error);
      toast.error('Failed to remove report from database. Please try again.');
      setShowConfirmDialog(false);
      if (onError) onError();
    }
  };

  return (
    <>
      <Button
        variant="outline"
        className={className}
        onClick={() => setShowConfirmDialog(true)}
        disabled={deleteReportMutation.isPending}
      >
        {deleteReportMutation.isPending ? (
          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
        ) : (
          <Trash2 className="h-4 w-4 mr-2 text-red-500" />
        )}
        Unsave from Database
      </Button>
      
      <AlertDialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete this reconciliation report and all associated data from the database.
              This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleDelete} className="bg-red-500 hover:bg-red-600">
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
