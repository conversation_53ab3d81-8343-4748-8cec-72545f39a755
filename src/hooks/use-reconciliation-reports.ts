import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { 
  saveReconciliationReport, 
  getReconciliationReport, 
  listReconciliationReports,
  ReconciliationReport 
} from '@/lib/supabase-service';
import { queryKeys, optimisticUpdates, cacheInvalidation } from '@/lib/query-client';
import { toast } from 'sonner';

// Hook to fetch list of reconciliation reports
export function useReconciliationReports(limit = 10) {
  return useQuery({
    queryKey: queryKeys.reconciliationReports.list({ limit }),
    queryFn: () => listReconciliationReports(limit),
    staleTime: 2 * 60 * 1000, // 2 minutes for list data
    gcTime: 5 * 60 * 1000, // 5 minutes cache time
    refetchOnWindowFocus: true,
    meta: {
      errorMessage: 'Failed to fetch reconciliation reports',
    },
  });
}

// Hook to fetch a specific reconciliation report
export function useReconciliationReport(reportId: string | null) {
  return useQuery({
    queryKey: queryKeys.reconciliationReports.detail(reportId || ''),
    queryFn: () => reportId ? getReconciliationReport(reportId) : null,
    enabled: !!reportId,
    staleTime: 5 * 60 * 1000, // 5 minutes for detail data
    gcTime: 10 * 60 * 1000, // 10 minutes cache time
    retry: (failureCount, error) => {
      // Don't retry if report not found
      if (error && 'status' in error && error.status === 404) {
        return false;
      }
      return failureCount < 3;
    },
    meta: {
      errorMessage: 'Failed to fetch reconciliation report details',
    },
  });
}

// Hook to save reconciliation report with optimistic updates
export function useSaveReconciliationReport() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: saveReconciliationReport,
    onMutate: async (newReport: ReconciliationReport) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ 
        queryKey: queryKeys.reconciliationReports.all 
      });

      // Snapshot previous value
      const previousReports = queryClient.getQueryData(
        queryKeys.reconciliationReports.lists()
      );

      // Optimistically update the cache
      const optimisticReport = {
        id: newReport.id,
        reportId: newReport.id,
        uploadedAt: newReport.uploadedAt,
        bankStatementFilename: newReport.bankStatementInfo.filename,
        ledgerFilename: newReport.ledgerInfo.filename,
        matchedTransactions: newReport.summary.matchedTransactions,
        discrepancies: newReport.summary.discrepancies,
        matchingRate: newReport.summary.matchingRate || '0%',
        createdAt: new Date().toISOString(),
      };

      optimisticUpdates.addReportOptimistically(optimisticReport);

      // Return context for rollback
      return { previousReports, optimisticReport };
    },
    onSuccess: (reportId, variables, context) => {
      toast.success('Reconciliation report saved successfully!');
      
      // Update the optimistic entry with the actual server response
      if (reportId && context?.optimisticReport) {
        queryClient.setQueryData(
          queryKeys.reconciliationReports.detail(reportId),
          variables
        );
      }
    },
    onError: (error, variables, context) => {
      // Rollback optimistic update
      if (context?.previousReports) {
        queryClient.setQueryData(
          queryKeys.reconciliationReports.lists(),
          context.previousReports
        );
      }
      
      toast.error('Failed to save reconciliation report');
      console.error('Save report error:', error);
    },
    onSettled: () => {
      // Refetch to ensure consistency
      cacheInvalidation.invalidateReports();
    },
  });
}

// Hook to delete reconciliation report (if needed in the future)
export function useDeleteReconciliationReport() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async () => {
      // This would be implemented when we add delete functionality
      throw new Error('Delete functionality not implemented yet');
    },
    onMutate: async (reportId: string) => {
      await queryClient.cancelQueries({ 
        queryKey: queryKeys.reconciliationReports.all 
      });

      const previousReports = queryClient.getQueryData(
        queryKeys.reconciliationReports.lists()
      );

      // Optimistically remove from cache
      queryClient.setQueryData(
        queryKeys.reconciliationReports.lists(),
        (old: Array<{ id: string }> = []) => old.filter(report => report.id !== reportId)
      );

      return { previousReports };
    },
    onSuccess: (_, reportId) => {
      toast.success('Report deleted successfully');
      cacheInvalidation.removeReport(reportId);
    },
    onError: (error, reportId, context) => {
      if (context?.previousReports) {
        queryClient.setQueryData(
          queryKeys.reconciliationReports.lists(),
          context.previousReports
        );
      }
      toast.error('Failed to delete report');
    },
  });
}

// Hook for prefetching report details on hover
export function usePrefetchReportDetails() {
  const queryClient = useQueryClient();

  return (reportId: string) => {
    queryClient.prefetchQuery({
      queryKey: queryKeys.reconciliationReports.detail(reportId),
      queryFn: () => getReconciliationReport(reportId),
      staleTime: 5 * 60 * 1000,
    });
  };
}
