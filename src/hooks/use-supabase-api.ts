// React imports
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { queryKeys } from '@/lib/query-client';
import { toast } from 'sonner';
import { ReconciliationReport } from '@/lib/supabase-service';

// Helper function for API calls
// Define type for API parameters
type ApiParams = Record<string, unknown>;

async function callSupabaseApi<T>(operation: string, params?: ApiParams): Promise<T> {
  const response = await fetch('/api/supabase', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      operation,
      params
    })
  });

  const data = await response.json();

  if (!data.success) {
    throw new Error(data.error || 'Unknown error');
  }

  return data as T;
}

// Hook to check Supabase connection status
export function useSupabaseConnection() {
  return useQuery({
    queryKey: ['supabase', 'connection'],
    queryFn: async () => {
      const result = await callSupabaseApi<{ success: boolean }>('test_connection');
      return result.success;
    },
    staleTime: 30 * 1000, // 30 seconds
    gcTime: 60 * 1000, // 1 minute
    refetchOnWindowFocus: false,
    refetchOnReconnect: true,
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 10000),
  });
}

// Define a type for the report list items that matches the API response
export type ReportListItem = {
  id: string;
  reportId: string;
  uploadedAt: string;
  bankStatementFilename: string;
  ledgerFilename: string;
  matchedTransactions: number;
  discrepancies: number;
  matchingRate: string;
  createdAt: string;
};

// Hook to fetch list of reconciliation reports
export function useReconciliationReports(limit = 10) {
  return useQuery({
    queryKey: queryKeys.reconciliationReports.list({ limit }),
    queryFn: async () => {
      const result = await callSupabaseApi<{ reports: ReportListItem[] }>('list_reports', { limit });
      return result.reports;
    },
    staleTime: 2 * 60 * 1000, // 2 minutes for list data
    gcTime: 5 * 60 * 1000, // 5 minutes cache time
    refetchOnWindowFocus: true,
    meta: {
      errorMessage: 'Failed to fetch reconciliation reports',
    },
  });
}

// Hook to fetch a specific reconciliation report
export function useReconciliationReport(reportId: string | null) {
  return useQuery({
    queryKey: queryKeys.reconciliationReports.detail(reportId || ''),
    queryFn: async () => {
      if (!reportId) return null;
      const result = await callSupabaseApi<{ report: Partial<ReportListItem> }>('get_report', { reportId });
      return result.report;
    },
    enabled: !!reportId,
    staleTime: 5 * 60 * 1000, // 5 minutes for detail data
    gcTime: 10 * 60 * 1000, // 10 minutes cache time
    retry: (failureCount, error: Error | unknown) => {
      // Don't retry if report not found
      const errorMessage = error instanceof Error ? error.message : String(error);
      if (errorMessage.includes('not found')) {
        return false;
      }
      return failureCount < 3;
    },
    meta: {
      errorMessage: 'Failed to fetch reconciliation report details',
    },
  });
}

// Hook to save reconciliation report with optimistic updates
export function useSaveReconciliationReport() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (report: ReconciliationReport) => {
      const result = await callSupabaseApi<{ reportId: string }>('save_report', { report });
      return result.reportId;
    },
    onMutate: async (newReport: ReconciliationReport) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ 
        queryKey: queryKeys.reconciliationReports.all 
      });

      // Snapshot previous value
      const previousReports = queryClient.getQueryData(
        queryKeys.reconciliationReports.lists()
      );

      // Define a type for the report list item that matches the API response
      type ReportListItem = {
        id: string;
        reportId: string;
        uploadedAt: string;
        bankStatementFilename: string;
        ledgerFilename: string;
        matchedTransactions: number;
        discrepancies: number;
        matchingRate: string;
        createdAt: string;
      };
      
      // Optimistically update the cache
      const optimisticReport: ReportListItem = {
        id: newReport.id,
        reportId: newReport.id,
        uploadedAt: newReport.uploadedAt,
        bankStatementFilename: newReport.bankStatementInfo.filename,
        ledgerFilename: newReport.ledgerInfo.filename,
        matchedTransactions: newReport.summary.matchedTransactions,
        discrepancies: newReport.summary.discrepancies,
        matchingRate: newReport.summary.matchingRate || '0%',
        createdAt: new Date().toISOString(),
      };

      queryClient.setQueryData<ReportListItem[]>(
        queryKeys.reconciliationReports.lists(),
        (old = []) => [optimisticReport, ...old]
      );

      // Return context for rollback
      return { previousReports, optimisticReport };
    },
    onSuccess: (reportId, variables, context) => {
      toast.success('Reconciliation report saved successfully!');
      
      // Update the optimistic entry with the actual server response
      if (reportId && context?.optimisticReport) {
        queryClient.setQueryData(
          queryKeys.reconciliationReports.detail(reportId),
          variables
        );
      }
    },
    onError: (error, variables, context) => {
      // Rollback optimistic update
      if (context?.previousReports) {
        queryClient.setQueryData(
          queryKeys.reconciliationReports.lists(),
          context.previousReports
        );
      }
      
      toast.error('Failed to save reconciliation report');
      console.error('Save report error:', error);
    },
    onSettled: () => {
      // Refetch to ensure consistency
      queryClient.invalidateQueries({ 
        queryKey: queryKeys.reconciliationReports.all 
      });
    },
  });
}
