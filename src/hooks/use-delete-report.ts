// React imports
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { queryKeys } from '@/lib/query-client';
import { toast } from 'sonner';
import { ReportListItem } from './use-supabase-api';

// Hook to delete a reconciliation report
export function useDeleteReconciliationReport() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (reportId: string) => {
      const response = await fetch('/api/delete-report', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ reportId })
      });

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.error || 'Unknown error');
      }

      return data.success;
    },
    onMutate: async (reportId: string) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ 
        queryKey: queryKeys.reconciliationReports.all 
      });

      // Snapshot previous value
      const previousReports = queryClient.getQueryData(
        queryKeys.reconciliationReports.lists()
      );

      // Optimistically update the cache by removing the report
      queryClient.setQueryData<ReportListItem[]>(
        queryKeys.reconciliationReports.lists(),
        (old = []) => old.filter(report => report.id !== reportId)
      );

      // Return context for rollback
      return { previousReports };
    },
    onSuccess: () => {
      toast.success('Reconciliation report removed successfully!');
    },
    onError: (error, variables, context) => {
      // Rollback optimistic update
      if (context?.previousReports) {
        queryClient.setQueryData(
          queryKeys.reconciliationReports.lists(),
          context.previousReports
        );
      }
      
      toast.error('Failed to remove reconciliation report');
      console.error('Delete report error:', error);
    },
    onSettled: () => {
      // Refetch to ensure consistency
      queryClient.invalidateQueries({ 
        queryKey: queryKeys.reconciliationReports.all 
      });
    },
  });
}
