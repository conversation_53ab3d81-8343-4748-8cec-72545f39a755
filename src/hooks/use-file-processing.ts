'use client'

import { useMutation } from '@tanstack/react-query'
import { toast } from 'sonner'
import type { ReconciliationResult } from '@/lib/schemas'
import { useErrorHandler, FileValidation } from '@/lib/error-handler'

interface ProcessFilesParams {
  bankStatement: File
  ledger: File
  onProgress?: (step: string, data?: Record<string, unknown>) => void
  onAIExplanationUpdate?: (discrepancyId: string, explanation: string) => void
  onInitialResult?: (result: ReconciliationResult) => void
}

interface StreamMessage {
  type: 'progress' | 'result' | 'error' | 'ai_explanation_update'
  step?: string
  data?: ReconciliationResult | Record<string, unknown> | {
    discrepancyId: string
    aiExplanation: string
  }
  error?: string
  timestamp: string
}

export function useFileProcessing() {
  const { handleAsyncError } = useErrorHandler()

  return useMutation({
    mutationFn: async ({ bankStatement, ledger, onProgress, onAIExplanationUpdate, onInitialResult }: ProcessFilesParams): Promise<ReconciliationResult> => {
      return handleAsyncError(async () => {
        // Validate files before processing
        FileValidation.validateBoth(bankStatement, ledger)

        const formData = new FormData()

        // Explicitly set content types to ensure proper validation
        const bankStatementType = bankStatement.type || 'application/pdf'
        const ledgerType = ledger.type || 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'

        // Create new File objects with correct MIME types if needed
        const bankStatementWithType = bankStatement.type
          ? bankStatement
          : new File([bankStatement], bankStatement.name, { type: bankStatementType })
        const ledgerWithType = ledger.type
          ? ledger
          : new File([ledger], ledger.name, { type: ledgerType })

        // Explicitly log what we're sending to help with debugging
        console.log(`[CLIENT] Appending bank_statement: ${bankStatementWithType.name}, type: ${bankStatementWithType.type}`)
        console.log(`[CLIENT] Appending ledger: ${ledgerWithType.name}, type: ${ledgerWithType.type}`)

        formData.append('bank_statement', bankStatementWithType)
        formData.append('ledger', ledgerWithType)

        // Check total file size - Vercel has 4.5MB limit, use backend for larger files
        const totalSize = bankStatement.size + ledger.size
        const VERCEL_LIMIT = 4.5 * 1024 * 1024 // 7.5MB in bytes

        // Determine which backend to use
        // 1. E2B - if enabled in settings
        // 2. FastAPI - if files are larger than Vercel limit
        // 3. Next.js API - default for smaller files
        const useE2B = process.env.NEXT_PUBLIC_USE_E2B === 'true'
        const useBackend = !useE2B && totalSize > VERCEL_LIMIT

        // Ensure backend URL is available if needed
        if (useBackend && !process.env.NEXT_PUBLIC_BACKEND_URL) {
          throw new Error('Backend URL not configured but required for large files. Please contact support.')
        }

        const endpoint = useE2B
          ? '/api/process-files-e2b'
          : useBackend
            ? `${process.env.NEXT_PUBLIC_BACKEND_URL}/api/process-files`
            : '/api/process-files-stream'

        console.log(`[FILE_PROCESSING] Files total: ${(totalSize / 1024 / 1024).toFixed(2)}MB, size limit: ${(VERCEL_LIMIT / 1024 / 1024).toFixed(2)}MB`)
        console.log(`[FILE_PROCESSING] Using ${useE2B ? 'E2B secure sandbox' : useBackend ? 'FastAPI backend' : 'Next.js API'}`)
        console.log(`[FILE_PROCESSING] Endpoint: ${endpoint}`)

        // Use streaming endpoint for better stability
        // For E2B, use a longer timeout since sandbox creation can take time
        const controller = new AbortController();
        const timeoutMs = useE2B ? 600000 : 120000; // 10 minutes for E2B, 2 minutes for others
        
        const timeoutId = setTimeout(() => controller.abort(), timeoutMs);
        console.log(`[FILE_PROCESSING] Setting request timeout to ${timeoutMs/1000}s`);
        
        try {
          const response = await fetch(endpoint, {
            method: 'POST',
            body: formData,
            signal: controller.signal
          });
          
          // Clear the timeout since the request completed
          clearTimeout(timeoutId);

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`)
          }

          // Handle different response types: streaming for Next.js/E2B, JSON for FastAPI
          if (useBackend && !useE2B) {
            // FastAPI backend returns JSON directly
            const result = await response.json() as ReconciliationResult
            onProgress?.('Processing completed')
            onInitialResult?.(result)
            return result
          } else {
            // Next.js API and E2B return streaming response
            const reader = response.body?.getReader()
            if (!reader) {
              throw new Error('Failed to get response reader')
            }

            const decoder = new TextDecoder()
            let result: ReconciliationResult | null = null
            let buffer = '' // Buffer for incomplete JSON messages

            try {
              while (true) {
                const { done, value } = await reader.read()

                if (done) break

                const chunk = decoder.decode(value, { stream: true })
                buffer += chunk
                const lines = buffer.split('\n')

                // Keep the last incomplete line in buffer
                buffer = lines.pop() || ''

                for (const line of lines) {
                  if (line.startsWith('data: ') && line.trim().length > 6) {
                    try {
                      const jsonData = line.slice(6).trim()
                      if (jsonData) {
                        const message: StreamMessage = JSON.parse(jsonData)

                        if (message.type === 'progress') {
                          onProgress?.(message.step || 'Processing...', message.data)
                          console.log(`[PROGRESS] ${message.step}`, message.data)
                        } else if (message.type === 'result') {
                          result = message.data as ReconciliationResult
                          console.log('[RESULT] Initial result received via stream', result)
                          // Notify caller immediately so UI can render and accept AI updates
                          onInitialResult?.(result)
                        } else if (message.type === 'ai_explanation_update') {
                          const updateData = message.data as { discrepancyId: string; aiExplanation: string }
                          onAIExplanationUpdate?.(updateData.discrepancyId, updateData.aiExplanation)
                          console.log('[AI_UPDATE] Received explanation for', updateData.discrepancyId)
                        } else if (message.type === 'error') {
                          throw new Error(message.error || 'Processing failed')
                        }
                      }
                    } catch (parseError) {
                      console.warn('Failed to parse SSE message:', line.slice(0, 100) + '...', parseError)
                      // Log the full message for debugging
                      console.log('Full message content:', line)
                      // Don't throw here, continue processing other messages
                    }
                  }
                }
              }

              // Process any remaining data in buffer
              if (buffer.trim() && buffer.startsWith('data: ')) {
                try {
                  const jsonData = buffer.slice(6).trim()
                  if (jsonData) {
                    const message: StreamMessage = JSON.parse(jsonData)
                    if (message.type === 'result') {
                      result = message.data as ReconciliationResult
                      console.log('[RESULT] Processing completed from buffer', result)
                    }
                  }
                } catch (parseError) {
                  console.warn('Failed to parse final buffer message:', buffer.slice(0, 100) + '...', parseError)
                }
              }
            } finally {
              reader.releaseLock()
            }

            if (!result) {
              throw new Error('No result received from processing')
            }

            return result
          }
        } catch (error) {
          // Make sure to clear the timeout if there's an error
          clearTimeout(timeoutId);
          
          // Check if this was an abort error due to our timeout
          if (error instanceof DOMException && error.name === 'AbortError') {
            throw new Error(`Request timed out after ${timeoutMs/1000} seconds. ${useE2B ? 'E2B sandbox creation may be taking too long.' : ''}`)
          }
          
          // Re-throw other errors
          throw error;
        }
      }, 'FILE_PROCESSING', 1) // Reduced retries since streaming is more reliable
    },
    onSuccess: (data) => {
      if (data?.summary) {
        toast.success('Files processed successfully!', {
          description: `Processed ${data.summary.totalBankTransactions} bank transactions and ${data.summary.totalLedgerTransactions} ledger entries.`
        })
      } else {
        toast.success('Files processed successfully!')
      }
      console.log('Processing result:', data)
    },
    onError: (error) => {
      console.error('Processing failed:', error)
      // Error handling is already done in handleAsyncError, so we don't need to show another toast
    },
  })
}
