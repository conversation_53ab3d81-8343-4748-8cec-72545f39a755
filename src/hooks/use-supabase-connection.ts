import { useQuery } from '@tanstack/react-query';
import { checkSupabaseConnection } from '@/lib/supabase-client';
import { queryKeys } from '@/lib/query-client';

// Hook to check Supabase connection status
export function useSupabaseConnection() {
  return useQuery({
    queryKey: queryKeys.supabase.connection,
    queryFn: checkSupabaseConnection,
    staleTime: 30 * 1000, // 30 seconds
    gcTime: 60 * 1000, // 1 minute
    refetchOnWindowFocus: false,
    refetchOnReconnect: true,
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 10000),
    meta: {
      errorMessage: 'Failed to connect to Supabase',
    },
  });
}
