'use client'

import { E2BTest } from '@/components/e2b-test'
import { BackendSelector } from '@/components/backend-selector'
import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from '@/components/ui/tabs'
import { BackendOption } from '@/components/backend-selector'

export default function E2BTestPage() {
  const [selectedBackend, setSelectedBackend] = useState<BackendOption>('e2b')
  const [fileSize, setFileSize] = useState(0)

  // Simulate different file sizes
  const handleFileSizeChange = (size: number) => {
    setFileSize(size)
  }

  return (
    <div className="container mx-auto py-8 space-y-8">
      <Card>
        <CardHeader>
          <CardTitle className="text-2xl">E2B Integration Test</CardTitle>
          <CardDescription>
            Test the E2B secure sandbox integration for bank reconciliation
          </CardDescription>
        </CardHeader>
        <CardContent>
          <p className="mb-4">
            This page allows you to test the E2B integration and backend selection logic.
            You can simulate different file sizes to see how the backend selection works.
          </p>
          
          <div className="mb-8">
            <h3 className="text-lg font-medium mb-2">Simulate File Size</h3>
            <div className="flex gap-4">
              <Button 
                variant={fileSize === 2 * 1024 * 1024 ? "default" : "outline"}
                onClick={() => handleFileSizeChange(2 * 1024 * 1024)}
              >
                Small (2MB)
              </Button>
              <Button 
                variant={fileSize === 6 * 1024 * 1024 ? "default" : "outline"}
                onClick={() => handleFileSizeChange(6 * 1024 * 1024)}
              >
                Medium (6MB)
              </Button>
              <Button 
                variant={fileSize === 10 * 1024 * 1024 ? "default" : "outline"}
                onClick={() => handleFileSizeChange(10 * 1024 * 1024)}
              >
                Large (10MB)
              </Button>
            </div>
            <p className="text-sm text-muted-foreground mt-2">
              Current file size: {(fileSize / (1024 * 1024)).toFixed(1)}MB
            </p>
          </div>
          
          <Tabs defaultValue="backend-selector">
            <TabsList className="mb-4">
              <TabsTrigger value="backend-selector">Backend Selector</TabsTrigger>
              <TabsTrigger value="e2b-test">E2B Test</TabsTrigger>
            </TabsList>
            
            <TabsContent value="backend-selector" className="space-y-4">
              <BackendSelector 
                selectedBackend={selectedBackend}
                onBackendChange={setSelectedBackend}
                fileSize={fileSize}
              />
              
              <Card className="bg-muted/30">
                <CardContent className="pt-6">
                  <h3 className="font-medium mb-2">Selected Backend: {selectedBackend}</h3>
                  <p className="text-sm text-muted-foreground">
                    {selectedBackend === 'nextjs' && 'Using Next.js API for fast processing'}
                    {selectedBackend === 'fastapi' && 'Using FastAPI backend for enhanced processing'}
                    {selectedBackend === 'e2b' && 'Using E2B secure sandbox for maximum security'}
                  </p>
                </CardContent>
              </Card>
            </TabsContent>
            
            <TabsContent value="e2b-test">
              <E2BTest />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}
