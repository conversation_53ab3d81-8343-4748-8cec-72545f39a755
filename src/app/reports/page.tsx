'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { useRouter } from 'next/navigation';
import { useReconciliationReports } from '@/hooks/use-supabase-api';
import { ReconciliationStorage, StoredReconciliation } from '@/lib/local-storage';
import { SyncButton } from '@/components/sync-button';
import { StorageTypeBadge } from '@/components/storage-type-badge';
import { DeleteReportButton } from '@/components/delete-report-button';
import { Database, FileText, Clock, ArrowLeft, Cloud, HardDrive, RefreshCw, Loader2 } from 'lucide-react';

export default function ReportsPage() {
  const router = useRouter();
  const [localReports, setLocalReports] = useState<StoredReconciliation[]>([]);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [reportInBothStorages, setReportInBothStorages] = useState<Record<string, boolean>>({});
  
  // Fetch database reports
  const {
    data: dbReports = [],
    isLoading: dbLoading,
    error: dbError,
    refetch: refetchDbReports
  } = useReconciliationReports(20);

  // Load local reports
  useEffect(() => {
    loadLocalReports();
  }, []);

  // Identify reports that exist in both storages
  useEffect(() => {
    if (localReports.length && dbReports.length) {
      const bothStorages: Record<string, boolean> = {};
      
      // Check which reports exist in both storages
      localReports.forEach(localReport => {
        const existsInDb = dbReports.some(dbReport => dbReport.reportId === localReport.id);
        if (existsInDb) {
          bothStorages[localReport.id] = true;
        }
      });
      
      setReportInBothStorages(bothStorages);
    }
  }, [localReports, dbReports]);

  const loadLocalReports = () => {
    const reports = ReconciliationStorage.getAllResults();
    setLocalReports(reports);
  };

  const handleRefresh = async () => {
    setIsRefreshing(true);
    loadLocalReports();
    await refetchDbReports();
    setIsRefreshing(false);
  };

  const handleLoadReport = (reportId: string) => {
    const report = ReconciliationStorage.getResult(reportId);
    if (report) {
      // Store as latest result and navigate to main page
      ReconciliationStorage.saveResult(report);
      router.push('/');
    }
  };
  
  const handleDeleteLocalReport = (reportId: string) => {
    ReconciliationStorage.deleteResult(reportId);
    loadLocalReports();
  };

  const formatDate = (dateString: string) => {
    // Ensure we have a valid date string
    if (!dateString) return 'Unknown date';
    
    try {
      // Parse the date and format it
      const date = new Date(dateString);
      
      // Check if date is valid
      if (isNaN(date.getTime())) {
        return 'Invalid date';
      }
      
      return date.toLocaleString(undefined, {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
    } catch (error) {
      console.error('Error formatting date:', error);
      return 'Date error';
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-ET', {
      style: 'currency',
      currency: 'ETB',
    }).format(amount);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100">
      <header className="border-b bg-white/80 backdrop-blur-sm">
        <div className="container mx-auto px-2 sm:px-4 py-3 sm:py-4">
          <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-3 sm:gap-0">
            <div className="flex items-center gap-2">
              <FileText className="h-5 w-5 sm:h-6 sm:w-6 text-primary" />
              <h1 className="text-lg sm:text-xl font-semibold">Reconciliation Reports</h1>
            </div>
            <div className="flex flex-wrap items-center gap-2 w-full sm:w-auto">
              <SyncButton />
              <Button variant="outline" size="sm" onClick={handleRefresh} disabled={isRefreshing} className="h-8 px-2 sm:px-3">
                {isRefreshing ? (
                  <Loader2 className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2 animate-spin" />
                ) : (
                  <RefreshCw className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
                )}
                <span className="text-xs sm:text-sm">Refresh</span>
              </Button>
              <Button variant="outline" size="sm" onClick={() => router.push('/')} className="h-8 px-2 sm:px-3 ml-auto sm:ml-0">
                <ArrowLeft className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
                <span className="text-xs sm:text-sm">Back to Home</span>
              </Button>
            </div>
          </div>
        </div>
      </header>

      <main className="container mx-auto px-2 sm:px-4 py-4 sm:py-8">
        <Tabs defaultValue="local" className="w-full">
          <TabsList className="grid w-full max-w-md mx-auto grid-cols-2 mb-4 sm:mb-8">
            <TabsTrigger value="local" className="flex items-center gap-1 sm:gap-2 px-2 sm:px-4">
              <HardDrive className="h-3 w-3 sm:h-4 sm:w-4" />
              <span className="hidden sm:inline">Local Storage</span>
              <span className="sm:hidden">Local</span>
              <Badge variant="secondary" className="ml-1 sm:ml-2 text-xs">{localReports.length}</Badge>
            </TabsTrigger>
            <TabsTrigger value="database" className="flex items-center gap-1 sm:gap-2 px-2 sm:px-4">
              <Database className="h-3 w-3 sm:h-4 sm:w-4" />
              <span className="hidden sm:inline">Database</span>
              <span className="sm:hidden">DB</span>
              <Badge variant="secondary" className="ml-1 sm:ml-2 text-xs">{dbReports.length}</Badge>
            </TabsTrigger>
          </TabsList>

          {/* Local Storage Tab */}
          <TabsContent value="local">
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4">
              {localReports.length === 0 ? (
                <div className="col-span-full text-center py-12">
                  <HardDrive className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                  <h3 className="text-lg font-medium mb-2">No Local Reports Found</h3>
                  <p className="text-muted-foreground">
                    Complete a reconciliation to see reports here
                  </p>
                </div>
              ) : (
                localReports.map((report) => (
                  <Card key={report.id} className="overflow-hidden">
                    <CardHeader className="pb-2">
                      <div className="flex items-center justify-between mb-2">
                        <CardTitle className="text-lg">
                          <div className="truncate max-w-[180px] sm:max-w-[250px]">{report.bankFileName}</div>
                        </CardTitle>
                        <div className="flex items-center gap-2 flex-shrink-0">
                          <StorageTypeBadge 
                            type={reportInBothStorages[report.id] ? 'both' : 'local'} 
                            showLabel={false}
                            size="sm"
                          />
                          <DeleteReportButton 
                            reportId={report.id} 
                            storageType={reportInBothStorages[report.id] ? 'both' : 'local'}
                            onSuccess={loadLocalReports}
                          />
                        </div>
                      </div>
                      <CardDescription className="flex items-center gap-1">
                        <Clock className="h-3 w-3" />
                        {formatDate(report.timestamp)}
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        <div className="text-sm">
                          <span className="font-medium">Ledger:</span> {report.ledgerFileName}
                        </div>
                        <div className="grid grid-cols-2 gap-2 text-sm">
                          <div>
                            <span className="font-medium">Matches:</span>{' '}
                            <Badge variant="secondary">{report.result.summary.matchedTransactions}</Badge>
                          </div>
                          <div>
                            <span className="font-medium">Discrepancies:</span>{' '}
                            <Badge variant="outline">{report.result.summary.discrepancies}</Badge>
                          </div>
                        </div>
                        <div className="text-sm">
                          <span className="font-medium">Balance Difference:</span>{' '}
                          {formatCurrency(Math.abs(report.result.summary.balanceDifference))}
                        </div>
                        <Separator className="my-3" />
                        <Button 
                          className="w-full" 
                          onClick={() => handleLoadReport(report.id)}
                        >
                          Load Report
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))
              )}
            </div>
          </TabsContent>

          {/* Database Tab */}
          <TabsContent value="database">
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4">
              {dbLoading ? (
                <div className="col-span-full text-center py-12">
                  <Loader2 className="h-12 w-12 mx-auto mb-4 text-gray-400 animate-spin" />
                  <h3 className="text-lg font-medium mb-2">Loading Database Reports</h3>
                  <p className="text-muted-foreground">
                    Please wait while we fetch your reports...
                  </p>
                </div>
              ) : dbError ? (
                <div className="col-span-full text-center py-12">
                  <Database className="h-12 w-12 mx-auto mb-4 text-red-400" />
                  <h3 className="text-lg font-medium mb-2 text-red-600">Error Loading Reports</h3>
                  <p className="text-muted-foreground">
                    {dbError.message || 'Failed to load database reports'}
                  </p>
                  <Button
                    variant="outline"
                    className="mt-4"
                    onClick={() => refetchDbReports()}
                  >
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Try Again
                  </Button>
                </div>
              ) : dbReports.length === 0 ? (
                <div className="col-span-full text-center py-12">
                  <Cloud className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                  <h3 className="text-lg font-medium mb-2">No Database Reports Found</h3>
                  <p className="text-muted-foreground">
                    Save reconciliations to the database to see them here
                  </p>
                </div>
              ) : (
                dbReports.map((report) => (
                  <Card key={report.id} className="overflow-hidden">
                    <CardHeader className="pb-2">
                      <div className="flex items-center justify-between mb-2">
                        <CardTitle className="text-lg">
                          <div className="truncate max-w-[180px] sm:max-w-[250px]">{report.bankStatementFilename}</div>
                        </CardTitle>
                        <div className="flex items-center gap-2 flex-shrink-0">
                          <StorageTypeBadge 
                            type={reportInBothStorages[report.reportId] ? 'both' : 'database'} 
                            showLabel={false}
                            size="sm"
                          />
                          <DeleteReportButton 
                            reportId={report.reportId} 
                            storageType={reportInBothStorages[report.reportId] ? 'both' : 'database'}
                            onSuccess={() => refetchDbReports()}
                          />
                        </div>
                      </div>
                      <CardDescription className="flex items-center gap-1">
                        <Clock className="h-3 w-3" />
                        {formatDate(report.uploadedAt)}
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        <div className="text-sm">
                          <span className="font-medium">Ledger:</span> {report.ledgerFilename}
                        </div>
                        <div className="grid grid-cols-2 gap-2 text-sm">
                          <div>
                            <span className="font-medium">Matches:</span>{' '}
                            <Badge variant="secondary">{report.matchedTransactions}</Badge>
                          </div>
                          <div>
                            <span className="font-medium">Discrepancies:</span>{' '}
                            <Badge variant="outline">{report.discrepancies}</Badge>
                          </div>
                        </div>
                        <div className="text-sm">
                          <span className="font-medium">Match Rate:</span>{' '}
                          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                            {report.matchingRate}
                          </Badge>
                        </div>
                        <Separator className="my-3" />
                        <div className="flex gap-2">
                          <Button 
                            variant="outline" 
                            className="w-full"
                            disabled
                          >
                            View Details
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))
              )}
            </div>
          </TabsContent>
        </Tabs>
      </main>
    </div>
  );
}
