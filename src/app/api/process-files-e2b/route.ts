/**
 * E2B-powered file processing API route
 * 
 * This API route processes bank statements and ledger files using E2B's secure sandboxed execution.
 * It provides a streaming response with progress updates and final results.
 */

import { NextRequest } from 'next/server';
import { E2BService } from '@/lib/e2b-service';

// Set a longer timeout for this API route to handle E2B sandbox creation
export const maxDuration = 600; // 10 minutes in seconds

export async function POST(request: NextRequest) {
  const encoder = new TextEncoder();
  
  const stream = new ReadableStream({
    async start(controller) {
      try {
        // Send progress update
        const sendProgress = (step: string, data?: Record<string, unknown>) => {
          const message = JSON.stringify({ 
            type: 'progress', 
            step, 
            data, 
            timestamp: new Date().toISOString() 
          });
          controller.enqueue(encoder.encode(`data: ${message}\n\n`));
        };
        
        // Send final result
        const sendResult = (result: Record<string, unknown>) => {
          const message = JSON.stringify({ 
            type: 'result', 
            data: result, 
            timestamp: new Date().toISOString() 
          });
          controller.enqueue(encoder.encode(`data: ${message}\n\n`));
        };
        
        // Send error
        const sendError = (error: string) => {
          const message = JSON.stringify({
            type: 'error',
            error,
            timestamp: new Date().toISOString()
          });
          controller.enqueue(encoder.encode(`data: ${message}\n\n`));
        };
        
        sendProgress('Starting E2B secure processing');
        
        // Parse form data
        const formData = await request.formData();
        
        // Log available form fields for debugging
        const formDataEntries = Array.from(formData.entries());
        console.log(`[E2B API] Form data fields: ${formDataEntries.map(entry => entry[0]).join(', ')}`);
        
        const bankStatement = formData.get('bank_statement') as File;
        const ledger = formData.get('ledger') as File;
        
        if (!bankStatement || !ledger) {
          const errorMsg = `Missing required files: ${!bankStatement ? 'bank_statement' : ''} ${!ledger ? 'ledger' : ''}`;
          console.error(`[E2B API] ${errorMsg}`);
          sendError(errorMsg);
          controller.close();
          return;
        }
        
        // Validate file types
        console.log(`[E2B API] Bank statement: ${bankStatement.name}, type: ${bankStatement.type || 'unknown'}`);
        console.log(`[E2B API] Ledger: ${ledger.name}, type: ${ledger.type || 'unknown'}`);
        
        sendProgress('Files received', {
          bankStatement: { name: bankStatement.name, size: bankStatement.size },
          ledger: { name: ledger.name, size: ledger.size }
        });
        
        // Initialize E2B service
        let sandbox = null;
        try {
          const e2bService = new E2BService();
          
          // Create sandbox
          sendProgress('Creating secure sandbox environment');
          sandbox = await e2bService.createSandbox();
          
          // Upload files to sandbox
          sendProgress('Uploading files to secure environment');
          const filePaths = await e2bService.uploadFiles(sandbox, bankStatement, ledger);
          
          // Process files in sandbox
          const result = await e2bService.processFiles(sandbox, filePaths, {
            onProgress: (step: string, data?: Record<string, unknown>) => sendProgress(step, data)
          });
          
          // Send final result
          sendResult(result);
          
          // Clean up sandbox
          await e2bService.destroySandbox(sandbox);
          sandbox = null;
          
        } catch (error) {
          console.error('[E2B API] Processing error:', error);
          
          // Clean up sandbox if it exists
          if (sandbox) {
            try {
              const e2bService = new E2BService();
              await e2bService.destroySandbox(sandbox);
            } catch (cleanupError) {
              console.error('[E2B API] Failed to clean up sandbox:', cleanupError);
            }
          }
          
          const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
          sendError(`E2B processing failed: ${errorMessage}`);
        }
        
        controller.close();
        
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
        const message = JSON.stringify({
          type: 'error',
          error: errorMessage,
          timestamp: new Date().toISOString()
        });
        controller.enqueue(encoder.encode(`data: ${message}\n\n`));
        controller.close();
      }
    }
  });
  
  return new Response(stream, {
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}
