import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { Database } from '@/lib/database.types';
import { ReconciliationReport } from '@/lib/supabase-service';

// Define types for API responses
type ReportListItem = {
  id: string;
  reportId: string;
  uploadedAt: string;
  bankStatementFilename: string;
  ledgerFilename: string;
  matchedTransactions: number;
  discrepancies: number;
  matchingRate: string;
  createdAt: string;
};

type ReportRow = Database['public']['Tables']['reconciliations']['Row'];


// Type for Supabase client with our Database schema
type TypedSupabaseClient = ReturnType<typeof createClient<Database>>;

// Create a direct Supabase client for server-side operations
const createServerClient = (): TypedSupabaseClient => {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY;
  
  if (!supabaseUrl || !supabaseServiceKey) {
    throw new Error('Missing Supabase environment variables');
  }
  
  return createClient<Database>(supabaseUrl, supabaseServiceKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  });
};

export async function POST(request: NextRequest) {
  try {
    const { operation, params } = await request.json();
    
    // Create a direct Supabase client
    const supabase = createServerClient();
    
    // Handle different operations
    switch (operation) {
      case 'test_connection':
        return handleTestConnection(supabase);
      
      case 'list_reports':
        return handleListReports(supabase, params?.limit || 10);
      
      case 'get_report':
        return handleGetReport(supabase, params?.reportId);
      
      case 'save_report':
        return handleSaveReport(supabase, params?.report);
      
      default:
        return NextResponse.json({
          success: false,
          error: `Unknown operation: ${operation}`
        }, { status: 400 });
    }
  } catch (error) {
    console.error('Error in Supabase API route:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

// Handler functions for different operations
async function handleTestConnection(supabase: TypedSupabaseClient) {
  try {
    const { data, error } = await supabase
      .from('reconciliations')
      .select('id')
      .limit(1);
    
    if (error) {
      return NextResponse.json({
        success: false,
        error: error.message,
        code: error.code
      }, { status: 500 });
    }
    
    return NextResponse.json({
      success: true,
      message: 'Supabase connection successful',
      recordCount: data?.length || 0
    });
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

async function handleListReports(supabase: TypedSupabaseClient, limit: number) {
  try {
    const { data, error } = await supabase
      .from('reconciliations')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(limit);
    
    if (error) {
      return NextResponse.json({
        success: false,
        error: error.message
      }, { status: 500 });
    }
    
    const reports: ReportListItem[] = (data || []).map((report: ReportRow) => ({
      id: report.id,
      reportId: report.id, // Using id as reportId since there's no separate report_id column
      uploadedAt: report.created_at,
      bankStatementFilename: report.bank_statement_filename,
      ledgerFilename: report.ledger_filename,
      matchedTransactions: report.matched_transactions,
      discrepancies: report.discrepancies_count,
      matchingRate: `${((report.matched_transactions / report.total_bank_transactions) * 100).toFixed(1)}%`,
      createdAt: report.created_at
    }));
    
    return NextResponse.json({
      success: true,
      reports
    });
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

async function handleGetReport(supabase: TypedSupabaseClient, reportId: string) {
  if (!reportId) {
    return NextResponse.json({
      success: false,
      error: 'Report ID is required'
    }, { status: 400 });
  }
  
  try {
    // Get report data
    const { data: reportData, error: reportError } = await supabase
      .from('reconciliations')
      .select('*')
      .eq('id', reportId)
      .single();
    
    if (reportError || !reportData) {
      return NextResponse.json({
        success: false,
        error: reportError?.message || 'Report not found'
      }, { status: reportError ? 500 : 404 });
    }
    
    // Get all transactions for this report
    const { error: transactionsError } = await supabase
      .from('transactions')
      .select('*')
      .eq('reconciliation_id', reportId);
    
    if (transactionsError) {
      return NextResponse.json({
        success: false,
        error: transactionsError.message
      }, { status: 500 });
    }
    
    // Get all matches for this report
    const { error: matchesError } = await supabase
      .from('matches')
      .select('*')
      .eq('reconciliation_id', reportId);
    
    if (matchesError) {
      return NextResponse.json({
        success: false,
        error: matchesError.message
      }, { status: 500 });
    }
    
    // Get all discrepancies for this report
    const { error: discrepanciesError } = await supabase
      .from('discrepancies')
      .select('*')
      .eq('reconciliation_id', reportId);
    
    if (discrepanciesError) {
      return NextResponse.json({
        success: false,
        error: discrepanciesError.message
      }, { status: 500 });
    }
    
    // Process the data and return the report
    const typedReportData = reportData as Database['public']['Tables']['reconciliations']['Row'];
    const report: Partial<ReportListItem> = {
      id: typedReportData.id,
      reportId: typedReportData.id, // Using id as reportId
      uploadedAt: typedReportData.created_at,
      bankStatementFilename: typedReportData.bank_statement_filename,
      ledgerFilename: typedReportData.ledger_filename,
      matchedTransactions: typedReportData.matched_transactions,
      discrepancies: typedReportData.discrepancies_count,
      matchingRate: `${((typedReportData.matched_transactions / typedReportData.total_bank_transactions) * 100).toFixed(1)}%`,
      createdAt: typedReportData.created_at
    };
    
    return NextResponse.json({
      success: true,
      report
    });
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

async function handleSaveReport(supabase: TypedSupabaseClient, report: ReconciliationReport) {
  if (!report) {
    return NextResponse.json({
      success: false,
      error: 'Report data is required'
    }, { status: 400 });
  }
  
  try {
    console.log('Saving reconciliation report to Supabase');
    
    // Create the insert data with proper typing
    const insertData: Database['public']['Tables']['reconciliations']['Insert'] = {
      bank_statement_filename: report.bankStatementInfo.filename,
      ledger_filename: report.ledgerInfo.filename,
      total_bank_transactions: report.bankStatementInfo.totalTransactions,
      total_ledger_transactions: report.ledgerInfo.totalTransactions,
      matched_transactions: report.summary.matchedTransactions,
      discrepancies_count: report.summary.discrepancies,
      bank_opening_balance: report.summary.bankBalance.opening,
      bank_closing_balance: report.summary.bankBalance.closing,
      ledger_opening_balance: report.summary.ledgerBalance.opening,
      ledger_closing_balance: report.summary.ledgerBalance.closing,
      balance_difference: report.summary.balanceDifference,
      status: 'completed',
      // Add any additional fields required by your schema
      processing_time_ms: 0, // Placeholder value
      // Create a matching rate string
      metadata: {
        totalDebits: report.summary.bankBalance.totalDebits,
        totalCredits: report.summary.bankBalance.totalCredits,
        matchingRate: report.summary.matchingRate || `${(report.summary.matchedTransactions / report.summary.totalBankTransactions * 100).toFixed(1)}%`
      }
    };
    
    // Start a transaction
    const { data: reportData, error: reportError } = await supabase
      .from('reconciliations')
      .insert(insertData as any) // eslint-disable-line @typescript-eslint/no-explicit-any
      .select('id')
      .single();

    if (reportError) {
      console.error('Error saving reconciliation report:', reportError);
      return NextResponse.json({
        success: false,
        error: reportError.message
      }, { status: 500 });
    }

    // Type assertion for reportData to access the id property
    const reportId = reportData ? (reportData as { id: string }).id : undefined;
    
    if (!reportId) {
      return NextResponse.json({
        success: false,
        error: 'Failed to get report ID'
      }, { status: 500 });
    }
    
    console.log(`Saved reconciliation report with ID: ${reportId}`);
    
    // Save all transactions (both bank and ledger)
    const transactions: Array<Database['public']['Tables']['transactions']['Insert']> = [];
    
    // Format date for database
    const formatDateForDB = (dateString: string): string => {
      try {
        const date = new Date(dateString);
        if (isNaN(date.getTime())) {
          // Try to parse DD MM YYYY format
          const parts = dateString.split(/[\s-\/]+/);
          if (parts.length === 3) {
            // Check if first part is day or year
            if (parseInt(parts[0]) <= 31 && parseInt(parts[1]) <= 12) {
              // DD MM YYYY format
              return `${parts[2]}-${parts[1].padStart(2, '0')}-${parts[0].padStart(2, '0')}`;
            } else if (parseInt(parts[2]) <= 31 && parseInt(parts[1]) <= 12) {
              // YYYY MM DD format
              return `${parts[0]}-${parts[1].padStart(2, '0')}-${parts[2].padStart(2, '0')}`;
            }
          }
          console.warn(`Could not parse date: ${dateString}, using fallback`);
          return '2025-01-01'; // Fallback date
        }
        
        // Format as YYYY-MM-DD
        return date.toISOString().split('T')[0];
      } catch (error) {
        console.error('Error formatting date:', error);
        return '2025-01-01'; // Fallback date
      }
    };
    
    // Add bank transactions from matches
    report.matches.forEach(match => {
      transactions.push({
        transaction_id: match.bankTransaction.id,
        reconciliation_id: reportId,
        date: formatDateForDB(match.bankTransaction.date),
        description: match.bankTransaction.description || 'No description',
        reference: match.bankTransaction.reference,
        amount: match.bankTransaction.amount,
        type: match.bankTransaction.type,
        source: match.bankTransaction.source,
        matched: true,
        match_confidence: match.confidence
      });
      
      transactions.push({
        transaction_id: match.ledgerTransaction.id,
        reconciliation_id: reportId,
        date: formatDateForDB(match.ledgerTransaction.date),
        description: match.ledgerTransaction.description || 'No description',
        reference: match.ledgerTransaction.reference,
        amount: match.ledgerTransaction.amount,
        type: match.ledgerTransaction.type,
        source: match.ledgerTransaction.source,
        matched: true,
        match_confidence: match.confidence
      });
    });
    
    // Add transactions from discrepancies
    report.discrepancies.forEach(discrepancy => {
      if (discrepancy.bankTransaction) {
        transactions.push({
          transaction_id: discrepancy.bankTransaction.id,
          reconciliation_id: reportId,
          date: formatDateForDB(discrepancy.bankTransaction.date),
          description: discrepancy.bankTransaction.description || 'No description',
          reference: discrepancy.bankTransaction.reference,
          amount: discrepancy.bankTransaction.amount,
          type: discrepancy.bankTransaction.type,
          source: discrepancy.bankTransaction.source,
          matched: false
        });
      }
      
      if (discrepancy.ledgerTransaction) {
        transactions.push({
          transaction_id: discrepancy.ledgerTransaction.id,
          reconciliation_id: reportId,
          date: formatDateForDB(discrepancy.ledgerTransaction.date),
          description: discrepancy.ledgerTransaction.description || 'No description',
          reference: discrepancy.ledgerTransaction.reference,
          amount: discrepancy.ledgerTransaction.amount,
          type: discrepancy.ledgerTransaction.type,
          source: discrepancy.ledgerTransaction.source,
          matched: false
        });
      }
    });
    
    console.log(`Saving ${transactions.length} transactions`);
    
    // Save transactions in batches of 100
    for (let i = 0; i < transactions.length; i += 100) {
      const batch = transactions.slice(i, i + 100);
      const { error: transactionError } = await supabase
        .from('transactions')
        .insert(batch as any);
      
      if (transactionError) {
        console.error(`Error saving transactions batch ${i / 100 + 1}:`, transactionError);
        return NextResponse.json({
          success: false,
          error: `Error saving transactions: ${transactionError.message}`
        }, { status: 500 });
      }
    }
    
    console.log(`Saved ${transactions.length} transactions`);
    
    // Get transaction IDs for matches
    const transactionMap = new Map<string, string>();
    
    const { data: transactionData, error: transactionQueryError } = await supabase
      .from('transactions')
      .select('id, transaction_id')
      .eq('reconciliation_id', reportId);
    
    if (transactionQueryError) {
      console.error('Error fetching transaction IDs:', transactionQueryError);
      return NextResponse.json({
        success: false,
        error: `Error fetching transaction IDs: ${transactionQueryError.message}`
      }, { status: 500 });
    }
    
    if (transactionData) {
      transactionData.forEach((transaction: any) => {
        transactionMap.set(transaction.transaction_id, transaction.id);
      });
    }
    
    // Save matches
    const matches: Array<Database['public']['Tables']['matches']['Insert']> = [];
    
    report.matches.forEach(match => {
      const bankTransactionId = transactionMap.get(match.bankTransaction.id);
      const ledgerTransactionId = transactionMap.get(match.ledgerTransaction.id);
      
      if (bankTransactionId && ledgerTransactionId) {
        matches.push({
          reconciliation_id: reportId,
          bank_transaction_id: bankTransactionId,
          ledger_transaction_id: ledgerTransactionId,
          confidence_score: match.confidence,
          match_type: match.matchType
        });
      }
    });
    
    console.log(`Saving ${matches.length} matches`);
    
    // Save matches in batches of 100
    for (let i = 0; i < matches.length; i += 100) {
      const batch = matches.slice(i, i + 100);
      const { error: matchError } = await supabase
        .from('matches')
        .insert(batch as any);
      
      if (matchError) {
        console.error(`Error saving matches batch ${i / 100 + 1}:`, matchError);
        return NextResponse.json({
          success: false,
          error: `Error saving matches: ${matchError.message}`
        }, { status: 500 });
      }
    }
    
    console.log(`Saved ${matches.length} matches`);
    
    // Save discrepancies
    const discrepancies: Array<Database['public']['Tables']['discrepancies']['Insert']> = [];
    
    report.discrepancies.forEach(discrepancy => {
      let transactionId: string | undefined;
      
      if (discrepancy.bankTransaction) {
        transactionId = transactionMap.get(discrepancy.bankTransaction.id);
      } else if (discrepancy.ledgerTransaction) {
        transactionId = transactionMap.get(discrepancy.ledgerTransaction.id);
      }
      
      if (transactionId) {
        discrepancies.push({
          reconciliation_id: reportId,
          transaction_id: transactionId,
          type: discrepancy.type,
          description: discrepancy.type === 'missing_from_ledger' ? 
            `Transaction found in bank but missing from ledger: ${discrepancy.bankTransaction?.description || 'No description'}` : 
            `Transaction found in ledger but missing from bank: ${discrepancy.ledgerTransaction?.description || 'No description'}`,
          ai_explanation: discrepancy.aiExplanation,
          suggested_action: null,
          resolved: false
        });
      }
    });
    
    console.log(`Saving ${discrepancies.length} discrepancies`);
    
    // Save discrepancies in batches of 100
    for (let i = 0; i < discrepancies.length; i += 100) {
      const batch = discrepancies.slice(i, i + 100);
      const { error: discrepancyError } = await supabase
        .from('discrepancies')
        .insert(batch as any);
      
      if (discrepancyError) {
        console.error(`Error saving discrepancies batch ${i / 100 + 1}:`, discrepancyError);
        return NextResponse.json({
          success: false,
          error: `Error saving discrepancies: ${discrepancyError.message}`
        }, { status: 500 });
      }
    }
    
    console.log(`Saved ${discrepancies.length} discrepancies`);
    
    // Return the saved report ID
    return NextResponse.json({
      success: true,
      reportId
    });
  } catch (error) {
    console.error('Error in handleSaveReport:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
