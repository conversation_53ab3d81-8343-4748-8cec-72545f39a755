import { NextRequest } from 'next/server'
import { parseBankStatementPDF, parseLedgerFile, ParsedTransaction } from '@/lib/file-parsers'
import { enhancedTransactionMatching } from '@/lib/enhanced-matching'
import { RateLimiter } from '@/lib/ai-queue-manager'
import { GoogleGenerativeAI } from '@google/generative-ai'
import { ReconciliationResult } from '@/lib/schemas'
import { saveReconciliationReport, ReconciliationReport } from '@/lib/supabase-service'

// Streaming response for long-running processes
export async function POST(request: NextRequest) {
  const encoder = new TextEncoder()

  const stream = new ReadableStream({
    async start(controller) {
      try {
        // Send progress update
        const sendProgress = (step: string, data?: Record<string, unknown>) => {
          const message = JSON.stringify({ type: 'progress', step, data, timestamp: new Date().toISOString() })
          controller.enqueue(encoder.encode(`data: ${message}\n\n`))
        }

        // Send final result
        const sendResult = (result: Record<string, unknown>) => {
          const message = JSON.stringify({ type: 'result', data: result, timestamp: new Date().toISOString() })
          controller.enqueue(encoder.encode(`data: ${message}\n\n`))
        }

        // Send error
        const sendError = (error: string) => {
          const message = JSON.stringify({ type: 'error', error, timestamp: new Date().toISOString() })
          controller.enqueue(encoder.encode(`data: ${message}\n\n`))
        }

        sendProgress('Starting file processing')

        // Parse form data
        const formData = await request.formData()
        
        // Log available form fields for debugging
        const formDataEntries = Array.from(formData.entries())
        console.log(`[API] Form data fields: ${formDataEntries.map(entry => entry[0]).join(', ')}`)
        
        const bankStatement = formData.get('bank_statement') as File
        const ledger = formData.get('ledger') as File

        if (!bankStatement || !ledger) {
          const errorMsg = `Missing required files: ${!bankStatement ? 'bank_statement' : ''} ${!ledger ? 'ledger' : ''}`
          console.error(`[API] ${errorMsg}`)
          sendError(errorMsg)
          controller.close()
          return
        }
        
        // Validate file types
        console.log(`[API] Bank statement: ${bankStatement.name}, type: ${bankStatement.type || 'unknown'}`)
        console.log(`[API] Ledger: ${ledger.name}, type: ${ledger.type || 'unknown'}`)

        sendProgress('Files received', {
          bankStatement: { name: bankStatement.name, size: bankStatement.size },
          ledger: { name: ledger.name, size: ledger.size }
        })

        // Parse files
        sendProgress('Parsing files')

        const [bankResult, ledgerResult] = await Promise.all([
          parseBankStatementPDF(bankStatement),
          parseLedgerFile(ledger)
        ])

        const bankTransactions = bankResult.transactions
        const ledgerTransactions = ledgerResult.transactions

        sendProgress('File parsing completed', {
          bankTransactions: bankTransactions.length,
          ledgerTransactions: ledgerTransactions.length
        })

        // Enhanced matching with progress updates
        sendProgress('Starting enhanced transaction matching')

        const matches = enhancedTransactionMatching(bankTransactions, ledgerTransactions)

        sendProgress('Transaction matching completed', {
          matches: matches.length,
          matchRate: ((matches.length / bankTransactions.length) * 100).toFixed(1) + '%'
        })

        // Calculate proper summary structure
        const bankCredits = bankTransactions.filter((t: ParsedTransaction) => t.type === 'credit').reduce((sum: number, t: ParsedTransaction) => sum + Math.abs(t.amount), 0)
        const bankDebits = bankTransactions.filter((t: ParsedTransaction) => t.type === 'debit').reduce((sum: number, t: ParsedTransaction) => sum + Math.abs(t.amount), 0)
        const ledgerCredits = ledgerTransactions.filter((t: ParsedTransaction) => t.type === 'credit').reduce((sum: number, t: ParsedTransaction) => sum + Math.abs(t.amount), 0)
        const ledgerDebits = ledgerTransactions.filter((t: ParsedTransaction) => t.type === 'debit').reduce((sum: number, t: ParsedTransaction) => sum + Math.abs(t.amount), 0)

        const summary = {
          totalBankTransactions: bankTransactions.length,
          totalLedgerTransactions: ledgerTransactions.length,
          matchedTransactions: matches.length,
          discrepancies: bankTransactions.length + ledgerTransactions.length - (matches.length * 2),
          bankBalance: {
            opening: 0, // Default as we don't have this from the parser
            closing: bankCredits - bankDebits,
            totalDebits: bankDebits,
            totalCredits: bankCredits,
          },
          ledgerBalance: {
            opening: 0, // Default as we don't have this from the parser
            closing: ledgerCredits - ledgerDebits,
            totalDebits: ledgerDebits,
            totalCredits: ledgerCredits,
          },
          balanceDifference: (bankCredits - bankDebits) - (ledgerCredits - ledgerDebits)
        }

        // Create discrepancies
        const matchedBankIds = new Set(matches.map(m => m.bankTransaction.id))
        const matchedLedgerIds = new Set(matches.map(m => m.ledgerTransaction.id))

        const unmatchedBank = bankTransactions.filter((t: ParsedTransaction) => !matchedBankIds.has(t.id))
        const unmatchedLedger = ledgerTransactions.filter((t: ParsedTransaction) => !matchedLedgerIds.has(t.id))

        const discrepancies = [
          ...unmatchedBank.map((t: ParsedTransaction) => ({
            id: `bank_${t.id}`,
            type: 'missing_from_ledger' as const,
            bankTransaction: t,
            ledgerTransaction: null,
            aiExplanation: 'Processing AI explanation...'
          })),
          ...unmatchedLedger.map((t: ParsedTransaction) => ({
            id: `ledger_${t.id}`,
            type: 'missing_from_bank' as const,
            bankTransaction: null,
            ledgerTransaction: t,
            aiExplanation: 'Processing AI explanation...'
          }))
        ]

        // Validation
        const highValueThreshold = 50000
        const highValueUnmatchedBank = unmatchedBank.filter((t: ParsedTransaction) => Math.abs(t.amount) >= highValueThreshold).length
        const highValueUnmatchedLedger = unmatchedLedger.filter((t: ParsedTransaction) => Math.abs(t.amount) >= highValueThreshold).length

        const validation = {
          totalBankTransactions: bankTransactions.length,
          totalLedgerTransactions: ledgerTransactions.length,
          matchedTransactions: matches.length,
          unmatchedBank: unmatchedBank.length,
          unmatchedLedger: unmatchedLedger.length,
          highValueUnmatchedBank,
          highValueUnmatchedLedger,
          potentialMissedMatches: 0,
          missedMatchDetails: [],
          matchingRate: ((matches.length / bankTransactions.length) * 100).toFixed(1) + '%'
        }

        sendProgress('Validation completed', validation)

        // Start AI explanation generation asynchronously
        sendProgress('Starting AI explanation generation')

        interface Discrepancy {
          id: string
          type: 'missing_from_ledger' | 'missing_from_bank'
          bankTransaction: ParsedTransaction | null
          ledgerTransaction: ParsedTransaction | null
          aiExplanation: string
        }

        // Initialize AI services with batch processing
        const genAI = new GoogleGenerativeAI(process.env.GOOGLE_GENERATIVE_AI_API_KEY!)
        const rateLimiter = new RateLimiter(25, 1) // 25 requests per 60 seconds (conservative for 30 RPM limit of gemini-2.0-flash-lite)

        // Batch processing function for AI explanations
        const processBatchedDiscrepancies = async (discrepancies: Discrepancy[]): Promise<{ id: string; explanation: string }[]> => {
          if (discrepancies.length === 0) return []

          try {
            console.log(`[AI Batch] Processing ${discrepancies.length} discrepancies in batch`)
            await rateLimiter.waitForToken(30000) // Longer timeout to ensure we get a token

            // Use the highest rate limit model available (Flash-Lite: 30 RPM)
            const model = genAI.getGenerativeModel({
              model: 'gemini-2.0-flash-lite', // Highest RPM (30) according to documentation
              generationConfig: {
                temperature: 0.3,
                maxOutputTokens: Math.min(discrepancies.length * 150, 1000), // Cap tokens to avoid exceeding limits
              }
            })

            // Create batch prompt with clear separators
            const batchPrompt = `You are a financial expert analyzing bank reconciliation discrepancies. Analyze each discrepancy below and provide a concise explanation (max 100 words each). Separate each response with "=== NEXT DISCREPANCY ===" exactly.

${discrepancies.map((discrepancy, index) => {
              const transaction = discrepancy.type === 'missing_from_ledger'
                ? discrepancy.bankTransaction
                : discrepancy.ledgerTransaction;

              return `DISCREPANCY ${index + 1} (ID: ${discrepancy.id}):
Type: ${discrepancy.type.replace('_', ' ')}
Amount: ${transaction?.amount || 0}
Description: ${transaction?.description || 'N/A'}
Date: ${transaction?.date || 'N/A'}
${discrepancy.type === 'missing_from_ledger'
  ? 'This transaction appears in the bank statement but not in the accounting ledger.'
  : 'This transaction appears in the ledger but not on the bank statement.'
}`;
            }).join('\n\n')}

Please provide explanations for each discrepancy above, separating each with "=== NEXT DISCREPANCY ===" exactly.`

            // Add timeout for batch processing with longer timeout for reliability
            const timeoutPromise = new Promise<never>((_, reject) =>
              setTimeout(() => reject(new Error('AI batch processing timeout')), 60000)
            )

            const generationPromise = model.generateContent(batchPrompt)
            const result = await Promise.race([generationPromise, timeoutPromise])
            const responseText = result.response.text()

            // Parse batch response
            const explanations = responseText.split('=== NEXT DISCREPANCY ===')
            const results: { id: string; explanation: string }[] = []

            discrepancies.forEach((discrepancy, index) => {
              const explanation = explanations[index]?.trim() || 'Analysis in progress...'
              results.push({
                id: discrepancy.id,
                explanation: explanation.length > 500 ? explanation.substring(0, 500) + '...' : explanation
              })
            })

            console.log(`[AI Batch] Successfully processed ${results.length} discrepancies`)
            return results

          } catch (error) {
            console.error('[AI Batch] Error processing batch:', error)

            // Fallback: return error messages for all discrepancies
            return discrepancies.map(discrepancy => ({
              id: discrepancy.id,
              explanation: error instanceof Error && error.message.includes('429')
                ? 'Rate limit reached. Analysis will be available shortly.'
                : 'AI analysis temporarily unavailable. Please try again later.'
            }))
          }
        }

        // Process all discrepancies with AI explanations using progressive batch processing
        // We'll process them in chunks to respect rate limits while ensuring all get processed
        const discrepanciesToProcess = discrepancies

        // Transform matches to match the schema format
        const transformedMatches = matches.map(match => ({
          bankTransaction: match.bankTransaction,
          ledgerTransaction: match.ledgerTransaction,
          confidence: match.confidence,
          matchType: (match.matchType === 'fin_reference' || match.matchType === 'amount_date') ? 'fuzzy' : match.matchType
        }))

        // Transform discrepancies to match the schema format
        const transformedDiscrepancies = [
          ...unmatchedBank.map((t: ParsedTransaction) => ({
            id: `bank_${t.id}`,
            type: 'missing_from_ledger' as const,
            transaction: t,
            aiExplanation: 'AI analysis in progress...'
          })),
          ...unmatchedLedger.map((t: ParsedTransaction) => ({
            id: `ledger_${t.id}`,
            type: 'missing_from_bank' as const,
            transaction: t,
            aiExplanation: 'AI analysis in progress...'
          }))
        ]

        // Send initial result without waiting for AI explanations
        const result: ReconciliationResult = {
          id: `reconciliation_${Date.now()}`,
          uploadedAt: new Date().toISOString(),
          bankStatementInfo: {
            filename: bankStatement.name,
            totalTransactions: bankTransactions.length,
            accountNumber: undefined,
            statementPeriod: undefined,
          },
          ledgerInfo: {
            filename: ledger.name,
            totalTransactions: ledgerTransactions.length,
          },
          summary,
          matches: transformedMatches,
          discrepancies: transformedDiscrepancies,
          status: 'completed' as const,
        }

        sendResult(result)
        sendProgress('Processing completed - Starting AI analysis in batches')

        // Process AI explanations using batch processing
        if (discrepanciesToProcess.length > 0) {
          try {
            const batchSize = 5 // Optimal batch size for gemini-2.0-flash-lite with 30 RPM limit
            const batches = []

            // Split discrepancies into batches
            for (let i = 0; i < discrepanciesToProcess.length; i += batchSize) {
              batches.push(discrepanciesToProcess.slice(i, i + batchSize))
            }

            // Calculate total processing time estimate with exponential backoff consideration
            // Average delay will be around 3s per batch with backoff
            const estimatedTimeMinutes = Math.ceil((batches.length * 3) / 60)
            sendProgress(`Processing all ${discrepanciesToProcess.length} discrepancies in ${batches.length} batches (est. ${estimatedTimeMinutes} min)`)
            
            // Add a more detailed progress message
            sendProgress(`Using gemini-2.0-flash-lite with 30 RPM limit. Processing in batches of ${batchSize} with rate limiting.`)

            let processedCount = 0
            for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
              const batch = batches[batchIndex]

              try {
                const explanations = await processBatchedDiscrepancies(batch)

                // Send individual updates for each discrepancy in the batch
                for (const { id, explanation } of explanations) {
                  const message = JSON.stringify({
                    type: 'ai_explanation_update',
                    data: {
                      discrepancyId: id,
                      aiExplanation: explanation
                    },
                    timestamp: new Date().toISOString()
                  })

                  try {
                    controller.enqueue(encoder.encode(`data: ${message}\n\n`))
                    processedCount++
                  } catch (error) {
                    console.error('Failed to send AI explanation update:', error)
                  }
                }

                sendProgress(`Completed batch ${batchIndex + 1}/${batches.length} (${processedCount}/${discrepanciesToProcess.length} analyses)`)

                // Add delay between batches to respect rate limits
                if (batchIndex < batches.length - 1) {
                  // Add exponential backoff with base delay of 2 seconds
                  const backoffDelay = 2000 * Math.pow(1.1, Math.min(batchIndex, 10))
                  await new Promise(resolve => setTimeout(resolve, backoffDelay))
                  
                  // Send progress update about waiting
                  const remainingBatches = batches.length - (batchIndex + 1)
                  const remainingTimeEstimate = Math.ceil((remainingBatches * backoffDelay/1000) / 60)
                  sendProgress(`Waiting between batches. Estimated ${remainingTimeEstimate} minutes remaining for ${remainingBatches} batches.`)
                }

              } catch (batchError) {
                console.error(`Error processing batch ${batchIndex + 1}:`, batchError)
                sendProgress(`Batch ${batchIndex + 1} failed - continuing with next batch`)
              }
            }

            sendProgress(`AI analysis completed: ${processedCount}/${discrepanciesToProcess.length} explanations generated`)

          } catch (error) {
            console.error('AI batch processing failed:', error)
            sendProgress('AI analysis encountered errors - basic reconciliation data is available')
          }
        }

        // Save to Supabase after processing is complete
        try {
          sendProgress('Saving reconciliation report to database')
          
          // Convert the result to ReconciliationReport format
          const supabaseReport: ReconciliationReport = {
            id: result.id,
            uploadedAt: result.uploadedAt,
            bankStatementInfo: result.bankStatementInfo,
            ledgerInfo: result.ledgerInfo,
            summary: {
              ...result.summary,
              matchingRate: ((result.matches.length / result.summary.totalBankTransactions) * 100).toFixed(1) + '%'
            },
            matches: result.matches.map(match => ({
              bankTransaction: {
                ...match.bankTransaction,
                reference: match.bankTransaction.reference || ''
              },
              ledgerTransaction: {
                ...match.ledgerTransaction,
                reference: match.ledgerTransaction.reference || ''
              },
              confidence: match.confidence,
              matchType: match.matchType
            })),
            discrepancies: result.discrepancies
              .filter(discrepancy => 
                discrepancy.type === 'missing_from_ledger' || 
                discrepancy.type === 'missing_from_bank'
              )
              .map(discrepancy => ({
                id: discrepancy.id,
                type: discrepancy.type as 'missing_from_ledger' | 'missing_from_bank',
                bankTransaction: discrepancy.type === 'missing_from_ledger' ? {
                  ...discrepancy.transaction,
                  reference: discrepancy.transaction.reference || ''
                } : null,
                ledgerTransaction: discrepancy.type === 'missing_from_bank' ? {
                  ...discrepancy.transaction,
                  reference: discrepancy.transaction.reference || ''
                } : null,
                aiExplanation: discrepancy.aiExplanation
              }))
          }

          const savedReportId = await saveReconciliationReport(supabaseReport)
          
          if (savedReportId) {
            sendProgress('Reconciliation report saved successfully', { reportId: savedReportId })
          } else {
            sendProgress('Warning: Failed to save report to database, but processing completed successfully')
          }
        } catch (saveError) {
          console.error('Error saving to Supabase:', saveError)
          sendProgress('Warning: Failed to save report to database, but processing completed successfully')
        }

        controller.close()

      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred'
        const message = JSON.stringify({
          type: 'error',
          error: errorMessage,
          timestamp: new Date().toISOString()
        })
        controller.enqueue(encoder.encode(`data: ${message}\n\n`))
        controller.close()
      }
    }
  })

  return new Response(stream, {
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  })
}
