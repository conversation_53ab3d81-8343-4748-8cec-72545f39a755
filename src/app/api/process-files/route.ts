import { NextRequest, NextResponse } from 'next/server'
import { GoogleGenerativeAI } from '@google/generative-ai'
import { parseBankStatementPDF, parseLedgerFile, ParsedTransaction } from '@/lib/file-parsers'
import { enhancedTransactionMatching } from '@/lib/enhanced-matching'
import { Discrepancy } from '@/lib/transaction-matcher'
import { AIQueueManager, RateLimiter } from '@/lib/ai-queue-manager'

// Initialize Gemini AI
const genAI = new GoogleGenerativeAI(process.env.GOOGLE_GENERATIVE_AI_API_KEY || '')

// Initialize rate limiter for Gemini 2.0 Flash (200 RPD, 1M TPM)
const rateLimiter = new RateLimiter(200, 200 / (24 * 60 * 60)) // 200 requests per day

// Create AI processor function for queue manager
const processDiscrepancyWithAI = async (discrepancy: Discrepancy) => {
  try {
    await rateLimiter.waitForToken()
    const explanation = await generateSingleAIExplanation(discrepancy)
    return {
      success: true,
      data: { ...discrepancy, aiExplanation: explanation }
    }
  } catch (error) {
    const isRateLimit = error instanceof Error && error.message.includes('429')
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      shouldRetry: isRateLimit
    }
  }
}

// Initialize AI Queue Manager
const aiQueueManager = new AIQueueManager(processDiscrepancyWithAI, {
  maxConcurrent: 1,
  baseDelay: 2000,
  maxRetries: 5
})

// Real file processing with parsing
async function processFiles(bankStatement: File, ledger: File) {
  try {
    logProcessingStep('Starting file processing')
    logProcessingStep('Files received', {
      bankStatement: { name: bankStatement.name, size: bankStatement.size },
      ledger: { name: ledger.name, size: ledger.size }
    })
    
    // Parse both files concurrently with error handling
    logProcessingStep('Starting file parsing')
    const [bankData, ledgerData] = await Promise.all([
      parseBankStatementPDF(bankStatement).catch((error: Error | unknown) => {
        handleProcessingError(error, 'PDF parsing failed')
      }),
      parseLedgerFile(ledger).catch((error: Error | unknown) => {
        handleProcessingError(error, 'Ledger parsing failed')
      })
    ])

    const bankTransactions = bankData.transactions
    const ledgerTransactions = ledgerData.transactions
    
    logProcessingStep('File parsing completed', {
      bankTransactions: bankTransactions.length,
      ledgerTransactions: ledgerTransactions.length
    })

    // Perform enhanced transaction matching
    logProcessingStep('Starting enhanced transaction matching')
    const enhancedMatches = enhancedTransactionMatching(bankTransactions, ledgerTransactions)
    
    // Convert enhanced matches to original format and get remaining discrepancies
    const matches = enhancedMatches.map(match => ({
      bankTransaction: match.bankTransaction,
      ledgerTransaction: match.ledgerTransaction,
      confidence: match.confidence,
      matchType: match.matchType,
      reasons: match.reasons
    }))
    
    const matchedBankIds = new Set(matches.map(m => m.bankTransaction.id))
    const matchedLedgerIds = new Set(matches.map(m => m.ledgerTransaction.id))
    
    const discrepancies = [
      ...bankTransactions
        .filter((t: ParsedTransaction) => !matchedBankIds.has(t.id))
        .map((t: ParsedTransaction) => ({
          id: `disc_${t.id}`,
          type: 'missing_from_ledger' as const,
          transaction: t,
          suggestedAction: 'Create journal entry to record this transaction in the ledger'
        })),
      ...ledgerTransactions
        .filter((t: ParsedTransaction) => !matchedLedgerIds.has(t.id))
        .map((t: ParsedTransaction) => ({
          id: `disc_${t.id}`,
          type: 'missing_from_bank' as const,
          transaction: t,
          suggestedAction: 'Verify this transaction appears on the bank statement'
        }))
    ]
    
    const matchingResult = {
      matches,
      discrepancies,
      matchingStats: {
        totalBankTransactions: bankTransactions.length,
        totalLedgerTransactions: ledgerTransactions.length,
        exactMatches: matches.filter(m => m.matchType === 'exact').length,
        fuzzyMatches: matches.filter(m => m.matchType === 'fuzzy').length,
        partialMatches: matches.filter(m => m.matchType === 'fin_reference' || m.matchType === 'amount_date').length,
        unmatched: discrepancies.length,
        averageConfidence: matches.length > 0 ? matches.reduce((sum, m) => sum + m.confidence, 0) / matches.length : 0
      }
    }
    logProcessingStep('Transaction matching completed', {
      matches: matchingResult.matches.length,
      discrepancies: matchingResult.discrepancies.length,
      averageConfidence: matchingResult.matchingStats.averageConfidence
    })
    
    // Generate AI explanations for discrepancies with intelligent batching
    logProcessingStep('Starting AI explanation generation')
    const discrepanciesWithAI = await generateBatchAIExplanations(matchingResult.discrepancies).catch(error => {
      console.warn('[WARNING] AI explanation generation failed, continuing with generic explanations:', error)
      // Return discrepancies with generic explanations as fallback
      return matchingResult.discrepancies.map(d => ({
        ...d,
        aiExplanation: 'AI explanation unavailable. This transaction requires manual review.'
      }))
    })
    
    logProcessingStep('AI explanation generation completed', {
      processedDiscrepancies: discrepanciesWithAI.length
    })

    // Calculate balances from parsed data
    const bankCredits = bankTransactions.filter((t: ParsedTransaction) => t.type === 'credit').reduce((sum: number, t: ParsedTransaction) => sum + Math.abs(t.amount), 0)
    const bankDebits = bankTransactions.filter((t: ParsedTransaction) => t.type === 'debit').reduce((sum: number, t: ParsedTransaction) => sum + Math.abs(t.amount), 0)
    const ledgerCredits = ledgerData.totalCredits
    const ledgerDebits = ledgerData.totalDebits

    const bankClosingBalance = bankData.closingBalance || (bankData.openingBalance || 0) + bankCredits - bankDebits
    const ledgerClosingBalance = (bankData.openingBalance || 0) + ledgerCredits - ledgerDebits

    logProcessingStep('Calculating final results')
    const result = {
      id: `reconciliation_${Date.now()}`,
      uploadedAt: new Date().toISOString(),
      bankStatementInfo: {
        filename: bankStatement.name,
        totalTransactions: bankTransactions.length,
      },
      ledgerInfo: {
        filename: ledger.name,
        totalTransactions: ledgerTransactions.length,
      },
      summary: {
        totalBankTransactions: bankTransactions.length,
        totalLedgerTransactions: ledgerTransactions.length,
        matchedTransactions: matchingResult.matches.length,
        discrepancies: discrepanciesWithAI.length,
        bankBalance: {
          opening: bankData.openingBalance || 0,
          closing: bankClosingBalance,
          totalDebits: -bankDebits,
          totalCredits: bankCredits,
        },
        ledgerBalance: {
          opening: bankData.openingBalance || 0,
          closing: ledgerClosingBalance,
          totalDebits: -ledgerDebits,
          totalCredits: ledgerCredits,
        },
        balanceDifference: bankClosingBalance - ledgerClosingBalance,
        matchingStats: matchingResult.matchingStats,
      },
      matches: matchingResult.matches,
      discrepancies: discrepanciesWithAI,
      status: 'completed' as const,
    }
    
    logProcessingStep('File processing completed successfully', {
      totalMatches: matchingResult.matches.length,
      totalDiscrepancies: discrepanciesWithAI.length,
      processingTime: Date.now() - new Date(result.uploadedAt).getTime()
    })
    return result
  } catch (error) {
    console.error('[PROCESSING] File processing error:', error)
    throw new Error('Failed to process files: ' + (error instanceof Error ? error.message : 'Unknown error'))
  }
}


// Helper function to delay execution
function delay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms))
}

// Enhanced AI explanation generation with queue management
async function generateBatchAIExplanations(discrepancies: Discrepancy[]): Promise<Discrepancy[]> {
  const MAX_DISCREPANCIES = 50 // Increased limit with better rate limiting
  const HIGH_VALUE_THRESHOLD = 100000 // Prioritize high-value transactions
  
  console.log(`[AI] Processing up to ${MAX_DISCREPANCIES} discrepancies with intelligent queuing`)
  
  // Sort discrepancies by value (high-value first) and limit
  const sortedDiscrepancies = discrepancies
    .sort((a, b) => Math.abs(b.transaction.amount) - Math.abs(a.transaction.amount))
    .slice(0, MAX_DISCREPANCIES)
  
  // Add discrepancies to AI queue with priority based on amount
  for (const discrepancy of sortedDiscrepancies) {
    const priority = Math.abs(discrepancy.transaction.amount) > HIGH_VALUE_THRESHOLD ? 10 : 5
    // Ensure aiExplanation is set before adding to queue
    const discrepancyWithExplanation = {
      ...discrepancy,
      aiExplanation: discrepancy.aiExplanation || 'Pending AI analysis...'
    }
    await aiQueueManager.addToQueue(discrepancyWithExplanation, priority)
  }
  
  // Wait for all AI processing to complete
  await aiQueueManager.waitForCompletion()
  
  // For now, return discrepancies with placeholder explanations
  // In a real implementation, you'd collect results from the queue
  const processedDiscrepancies = sortedDiscrepancies.map(d => ({
    ...d,
    aiExplanation: `Processing queued for ${d.transaction.source} transaction of ${Math.abs(d.transaction.amount).toLocaleString()}`
  }))
  
  // Add remaining discrepancies without AI explanations
  const remainingDiscrepancies = discrepancies.slice(MAX_DISCREPANCIES).map(d => ({
    ...d,
    aiExplanation: 'AI explanation queued for later processing due to volume limits.'
  }))
  
  return [...processedDiscrepancies, ...remainingDiscrepancies]
}


// Generate single AI explanation with improved error handling
async function generateSingleAIExplanation(discrepancy: Discrepancy): Promise<string> {
  const maxRetries = 3
  let retryCount = 0
  
  while (retryCount < maxRetries) {
    try {
      const model = genAI.getGenerativeModel({ 
        model: 'gemini-2.0-flash-exp',
        generationConfig: {
          temperature: 0.3,
          maxOutputTokens: 100,
          topK: 40,
          topP: 0.95,
        },
        systemInstruction: 'You are a financial reconciliation expert. Provide a concise explanation for this transaction discrepancy.'
      })
      
      const transaction = discrepancy.transaction
      const prompt = `Transaction discrepancy analysis:
- Type: ${discrepancy.type}
- Amount: ${transaction.amount}
- Date: ${transaction.date}
- Description: ${transaction.description || 'N/A'}
- Reference: ${transaction.reference || 'N/A'}
- Source: ${transaction.source}

Provide a brief explanation for why this transaction might be unmatched:`
      
      const result = await model.generateContent(prompt)
      return result.response.text().trim()
    } catch (error) {
      retryCount++
      const isRateLimit = error instanceof Error && error.message.includes('429')
      
      if (isRateLimit && retryCount < maxRetries) {
        const backoffDelay = Math.pow(2, retryCount) * 1000 + Math.random() * 1000
        console.log(`[AI] Rate limited, retrying in ${Math.round(backoffDelay)}ms (attempt ${retryCount + 1})`)
        await delay(backoffDelay)
        continue
      }
      
      console.error(`[AI] Error generating explanation (attempt ${retryCount}):`, error)
      if (retryCount >= maxRetries) {
        return `Unable to generate AI explanation after ${maxRetries} attempts. Manual review required.`
      }
    }
  }
  
  return 'AI explanation generation failed. Manual review required.'
}

// Enhanced error handling and performance monitoring
function logProcessingStep(step: string, data?: Record<string, unknown>) {
  const timestamp = new Date().toISOString()
  console.log(`[${timestamp}] ${step}`, data ? JSON.stringify(data, null, 2) : '')
}

function handleProcessingError(error: unknown, context: string): never {
  const errorMessage = error instanceof Error ? error.message : 'Unknown error'
  const errorStack = error instanceof Error ? error.stack : 'No stack trace'
  
  console.error(`[ERROR] ${context}:`, {
    message: errorMessage,
    stack: errorStack,
    timestamp: new Date().toISOString()
  })
  
  throw new Error(`${context}: ${errorMessage}`)
}

export async function POST(request: NextRequest) {
  // Set longer timeout for large file processing
  const timeoutId = setTimeout(() => {
    console.log('[TIMEOUT] Request processing taking longer than expected, but continuing...')
  }, 180000) // 3 minutes warning

  try {
    logProcessingStep('Starting file processing')
    
    const formData = await request.formData()
    const bankStatement = formData.get('bankStatement') as File
    const ledger = formData.get('ledger') as File

    if (!bankStatement || !ledger) {
      clearTimeout(timeoutId)
      return NextResponse.json(
        { success: false, error: 'Both bank statement and ledger files are required' },
        { status: 400 }
      )
    }

    // Validate file types with more permissive checks
    const validPdfTypes = ['application/pdf', 'application/x-pdf'];
    const isPdfValid = validPdfTypes.includes(bankStatement.type) || 
                      bankStatement.name.toLowerCase().endsWith('.pdf');
                      
    if (!isPdfValid) {
      return NextResponse.json(
        { success: false, error: 'Bank statement must be a PDF file' },
        { status: 400 }
      )
    }

    const validLedgerTypes = [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-excel',
      'text/csv',
      'application/csv',
      'text/x-csv',
      'application/x-csv'
    ]
    
    const fileName = ledger.name.toLowerCase();
    const isLedgerValid = validLedgerTypes.includes(ledger.type) || 
                         fileName.endsWith('.xlsx') || 
                         fileName.endsWith('.xls') || 
                         fileName.endsWith('.csv');
    
    if (!isLedgerValid) {
      return NextResponse.json(
        { success: false, error: 'Ledger must be an Excel or CSV file' },
        { status: 400 }
      )
    }

    // Process files
    const result = await processFiles(bankStatement, ledger)

    clearTimeout(timeoutId)
    return NextResponse.json({ success: true, data: result })

  } catch (error) {
    clearTimeout(timeoutId)
    handleProcessingError(error, 'File processing failed')
  }
}
