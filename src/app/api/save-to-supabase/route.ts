import { NextRequest } from 'next/server';
import { parseSSEStreamData } from '@/lib/parse-sse-stream';
import { saveReconciliationReport } from '@/lib/supabase-service';

export async function POST(request: NextRequest) {
  try {
    // Get the SSE stream data from the request
    const { sseData } = await request.json();
    
    if (!sseData) {
      return Response.json({ success: false, error: 'No SSE data provided' }, { status: 400 });
    }
    
    // Parse the SSE stream data
    const report = parseSSEStreamData(sseData);
    
    if (!report) {
      return Response.json({ success: false, error: 'Failed to parse SSE data' }, { status: 400 });
    }
    
    // Save the report to Supabase
    const reportId = await saveReconciliationReport(report);
    
    if (!reportId) {
      return Response.json({ success: false, error: 'Failed to save report to Supabase' }, { status: 500 });
    }
    
    return Response.json({ success: true, reportId });
  } catch (error) {
    console.error('Error saving report to Supabase:', error);
    return Response.json({ 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error occurred' 
    }, { status: 500 });
  }
}
