import { NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

export async function GET() {
  try {
    // Get environment variables directly in the API route
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY;
    
    // Log environment variables (without revealing full values)
    console.log('NEXT_PUBLIC_SUPABASE_URL exists:', !!supabaseUrl);
    console.log('SUPABASE_SERVICE_KEY exists:', !!supabaseServiceKey);
    
    // Check if environment variables are available
    if (!supabaseUrl || !supabaseServiceKey) {
      return NextResponse.json({
        success: false,
        error: 'Missing Supabase environment variables',
        envVars: {
          url: !!supabaseUrl,
          serviceKey: !!supabaseServiceKey
        }
      }, { status: 500 });
    }
    
    // Create a direct client for this API route
    const supabase = createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    });
    
    // Test the connection with the direct client
    const { data, error } = await supabase
      .from('reconciliation_reports')
      .select('*')
      .limit(5);
    
    if (error) {
      console.error('Supabase connection error:', error);
      return NextResponse.json({ 
        success: false, 
        error: error.message,
        code: error.code,
        details: error.details,
        hint: error.hint
      }, { status: 500 });
    }
    
    return NextResponse.json({ 
      success: true, 
      message: 'Supabase connection successful',
      recordCount: data?.length || 0,
      tables: ['reconciliation_reports', 'transactions', 'matches', 'discrepancies']
    });
  } catch (error) {
    console.error('Error testing Supabase connection:', error);
    return NextResponse.json({ 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
