import { NextResponse } from 'next/server'
import { BackendAPI } from '@/lib/backend-api'

export async function GET() {
  try {
    // Test backend connection
    const backendHealth = await BackendAPI.healthCheck()

    return NextResponse.json({
      success: true,
      frontend: 'healthy',
      backend: backendHealth,
      backendUrl: BackendAPI.getBackendUrl(),
      timestamp: new Date().toISOString()
    })
  } catch (error) {
    console.error('Backend health check failed:', error)

    return NextResponse.json({
      success: false,
      frontend: 'healthy',
      backend: 'unhealthy',
      backendUrl: BackendAPI.getBackendUrl(),
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 503 })
  }
}
