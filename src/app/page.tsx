'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { FileUploadNew } from '@/components/file-upload-new'
import { ResultsDashboard } from '@/components/results-dashboard'
import { StorageManager } from '@/components/storage-manager'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import { Brain, FileText, Zap, Shield, History } from 'lucide-react'
import { useFileProcessing } from '@/hooks/use-file-processing'
import { ReconciliationResult } from '@/lib/schemas'
import { ReconciliationStorage } from '@/lib/local-storage'

export default function Home() {
  const router = useRouter()
  const [result, setResult] = useState<ReconciliationResult | null>(null)
  const [processingStep, setProcessingStep] = useState<string>('')
  const [processingData, setProcessingData] = useState<Record<string, unknown> | null>(null)

  const processingMutation = useFileProcessing()

  // Load latest result from storage on component mount
  useEffect(() => {
    const latestResult = ReconciliationStorage.getLatestResult()
    if (latestResult) {
      console.log('🔍 Loading result from localStorage:', latestResult.id);
      console.log('🔍 Discrepancies with AI:', latestResult.discrepancies?.filter(d =>
        d.aiExplanation && d.aiExplanation !== "AI analysis in progress..."
      ).length || 0);
      setResult(latestResult)
    } else {
      console.log('🔍 No stored result found');
    }
  }, [])

  const handleProgress = (step: string, data?: Record<string, unknown>) => {
    setProcessingStep(step)
    setProcessingData(data || null)
  }

  const handleAIExplanationUpdate = (discrepancyId: string, explanation: string) => {
    console.log(`[AI_UPDATE] Updating discrepancy ${discrepancyId} with explanation:`, explanation.substring(0, 100) + '...');

    // Update the current result with the new AI explanation
    setResult(prevResult => {
      if (!prevResult) {
        console.log(`[AI_UPDATE] No previous result to update for ${discrepancyId}`);
        return prevResult;
      }

      console.log(`[AI_UPDATE] Updating result for ${discrepancyId}, current discrepancies:`, prevResult.discrepancies.length);

      // Check if this discrepancy already has the same explanation (avoid unnecessary updates)
      const existingDiscrepancy = prevResult.discrepancies.find(d => d.id === discrepancyId);
      if (existingDiscrepancy && existingDiscrepancy.aiExplanation === explanation) {
        console.log(`[AI_UPDATE] Discrepancy ${discrepancyId} already has this explanation, skipping update`);
        return prevResult;
      }

      const updatedDiscrepancies = prevResult.discrepancies.map(discrepancy => {
        if (discrepancy.id === discrepancyId) {
          console.log(`[AI_UPDATE] Found matching discrepancy ${discrepancyId}, updating explanation`);
          return { ...discrepancy, aiExplanation: explanation }
        }
        return discrepancy
      })

      const updatedResult = { ...prevResult, discrepancies: updatedDiscrepancies }

      // Also update local storage
      ReconciliationStorage.saveResult(updatedResult)

      console.log(`[AI_UPDATE] Updated result saved for ${discrepancyId}`);
      return updatedResult
    })
  }

  const handleInitialResult = (initial: ReconciliationResult) => {
    console.log('🟢 Setting initial result from stream so AI updates can apply');
    setResult(initial);
    ReconciliationStorage.saveResult(initial);
  }

  const handleFilesSelected = async (files: { bankStatement: File; ledger: File }) => {
    // Reset progress state
    setProcessingStep('')
    setProcessingData(null)

    processingMutation.mutate({
      ...files,
      onProgress: handleProgress,
      onAIExplanationUpdate: handleAIExplanationUpdate,
      onInitialResult: handleInitialResult
    }, {
      onSuccess: (data) => {
        console.log('Processing completed successfully, received data:', data);

        // If we already set initial result (with possible AI updates), prefer preserving it
        setResult(prevResult => {
          if (prevResult) {
            console.log('Merging final data with existing result (which may have AI updates)');
            const mergedResult = { ...prevResult };
            // Update only metadata and counts from final data
            mergedResult.id = data.id;
            mergedResult.uploadedAt = data.uploadedAt;
            mergedResult.status = data.status;
            mergedResult.summary = data.summary;
            mergedResult.matches = data.matches;
            // Keep discrepancies from prevResult since they may include AI updates
            ReconciliationStorage.saveResult(mergedResult);
            return mergedResult;
          } else {
            // No existing results (unlikely), use final data
            console.log('No existing result found at onSuccess, using final data');
            ReconciliationStorage.saveResult(data);
            return data;
          }
        });

        // Clear progress state
        setProcessingStep('')
        setProcessingData(null)
      },
      onError: () => {
        // Clear progress state on error
        setProcessingStep('')
        setProcessingData(null)
      }
    })
  }

  const handleNewReconciliation = () => {
    setResult(null)
    processingMutation.reset()
  }

  const handleLoadStoredResult = (storedResult: ReconciliationResult) => {
    setResult(storedResult)
  }


  // Show results dashboard if processing is complete
  if (result) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100">
        <header className="border-b bg-white/80 backdrop-blur-sm">
          <div className="container mx-auto px-4 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Brain className="h-8 w-8 text-primary" />
                <h1 className="text-xl font-semibold">Bank Reconciliation AI</h1>
              </div>
              <Button 
                variant="outline" 
                size="sm" 
                className="flex items-center gap-2"
                onClick={() => router.push('/reports')}
              >
                <History className="h-4 w-4" />
                Reports
              </Button>
            </div>
          </div>
        </header>
        <main className="container mx-auto px-4 py-8">
          <ResultsDashboard
            result={result}
            onNewReconciliation={handleNewReconciliation}
          />
        </main>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100">
      {/* Header */}
      <header className="border-b bg-white/80 backdrop-blur-sm">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Brain className="h-8 w-8 text-primary" />
              <h1 className="text-2xl font-bold">Bank Reconciliation AI</h1>
            </div>
            <div className="flex items-center gap-2">
              <Button 
                variant="outline" 
                size="sm" 
                className="flex items-center gap-2"
                onClick={() => router.push('/reports')}
              >
                <History className="h-4 w-4" />
                Reports
              </Button>
              <Badge variant="secondary">MVP</Badge>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="container mx-auto px-4 py-8 sm:py-12">
        <div className="text-center mb-8 sm:mb-12">
          <h2 className="text-2xl sm:text-3xl lg:text-4xl font-bold mb-4">
            Automate Your Bank Reconciliation
          </h2>
          <p className="text-lg sm:text-xl text-muted-foreground max-w-2xl mx-auto px-4">
            Upload your bank statement and ledger files. Our AI will automatically match transactions,
            identify discrepancies, and provide intelligent insights.
          </p>
        </div>

        {/* Features */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 sm:gap-6 mb-8 sm:mb-12">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="h-5 w-5 text-yellow-500" />
                AI-Powered Matching
              </CardTitle>
            </CardHeader>
            <CardContent>
              <CardDescription>
                Advanced algorithms automatically match transactions using reference numbers, amounts, and dates.
              </CardDescription>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5 text-blue-500" />
                Smart Analysis
              </CardTitle>
            </CardHeader>
            <CardContent>
              <CardDescription>
                Get detailed explanations for discrepancies and suggested journal entries powered by Google Gemini.
              </CardDescription>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5 text-green-500" />
                Secure & Compliant
              </CardTitle>
            </CardHeader>
            <CardContent>
              <CardDescription>
                Your financial data is processed securely with enterprise-grade encryption and audit trails.
              </CardDescription>
            </CardContent>
          </Card>
        </div>

        {/* File Upload Section */}
        <Card className="max-w-4xl mx-auto">
          <CardHeader className="text-center sm:text-left">
            <CardTitle className="text-xl sm:text-2xl">Upload Your Files</CardTitle>
            <CardDescription className="text-sm sm:text-base">
              Upload your PDF bank statement and Excel/CSV ledger file to get started
            </CardDescription>
          </CardHeader>
          <CardContent className="px-4 sm:px-6">
            <FileUploadNew
              onFilesSelected={handleFilesSelected}
              isProcessing={processingMutation.isPending}
              processingStep={processingStep}
              processingData={processingData}
            />
          </CardContent>
        </Card>

        {/* Storage Manager Section */}
        <div className="max-w-4xl mx-auto mt-8">
          <Separator className="mb-8" />
          <StorageManager onLoadResult={handleLoadStoredResult} />
        </div>
      </section>

      {/* Footer */}
      <footer className="border-t bg-white/80 backdrop-blur-sm mt-20">
        <div className="container mx-auto px-4 py-8">
          <div className="text-center text-sm text-muted-foreground">
            <p> 2025 Bank Reconciliation AI. Built with Next.js, shadcn/ui, and Google Gemini.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}
