#!/usr/bin/env python3
"""
Test script to verify our RFSA transaction matching improvements
"""

import pandas as pd
import sys
import os

# Add backend to path
sys.path.append('backend')

def test_excel_parsing():
    """Test Excel parsing with RFSA format"""
    print("Testing Excel parsing...")
    
    try:
        # Load the Excel file directly
        df = pd.read_excel('../example-docs/RFSA_July_2025_CBE_bank_statement.xlsx')
        print(f"✓ Excel file loaded: {df.shape[0]} rows, {df.shape[1]} columns")
        
        # Check for unnamed columns (RFSA format indicator)
        unnamed_cols = [col for col in df.columns if 'unnamed' in str(col).lower()]
        print(f"✓ Found {len(unnamed_cols)} unnamed columns (RFSA format detected)")
        
        # Look for the specific 24,000 transactions we identified
        amount_matches = []
        for idx, row in df.iterrows():
            for val in row:
                if pd.notna(val):
                    val_str = str(val).replace(',', '').replace(' ', '')
                    if val_str == '24000' or val_str == '24000.0':
                        amount_matches.append((idx, row))
                        break
        
        print(f"✓ Found {len(amount_matches)} transactions with 24,000 amount")
        
        # Show the key transaction we want to match
        for idx, (row_idx, row) in enumerate(amount_matches):
            print(f"  Transaction {idx+1} (Row {row_idx}):")
            row_data = [str(val) for val in row if pd.notna(val)]
            print(f"    Data: {' | '.join(row_data[:8])}")  # Show first 8 columns
            
            # Look for ********** pattern
            if any('**********' in str(val) for val in row):
                print(f"    ✓ Found ********** reference - this should match bank CHQ NO ********")
        
        return True
        
    except Exception as e:
        print(f"✗ Excel parsing failed: {e}")
        return False

def test_matching_algorithm():
    """Test our improved matching algorithm"""
    print("\nTesting matching algorithm...")
    
    try:
        from improved_matching_algorithm import ImprovedTransactionMatcher, Transaction
        from datetime import datetime
        
        # Create the bank transaction from our analysis
        bank_tx = Transaction(
            id="bank_1",
            date=datetime(2025, 7, 1),
            amount=-24000.00,
            description="CHQ NO ********",
            reference="TT25182CV3YG",
            type="debit",
            source="bank",
            raw_data={}
        )
        
        # Create the matching ledger transaction
        ledger_tx = Transaction(
            id="ledger_246",
            date=datetime(2025, 7, 17),
            amount=24000.00,
            description="Bank CBE-RFSA *************",
            reference="**********",
            type="credit",
            source="ledger",
            raw_data={}
        )
        
        # Test matching
        matcher = ImprovedTransactionMatcher()
        matches = matcher.match_transactions([bank_tx], [ledger_tx])
        
        if matches:
            match = matches[0]
            print(f"✓ Match found with {match.confidence:.2f} confidence")
            print(f"  Bank: {match.bank_transaction.description} ({match.bank_transaction.amount})")
            print(f"  Ledger: {match.ledger_transaction.reference} ({match.ledger_transaction.amount})")
            print(f"  Criteria: {', '.join(match.matching_criteria)}")
            return True
        else:
            print("✗ No matches found")
            return False
            
    except Exception as e:
        print(f"✗ Matching algorithm test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_reference_extraction():
    """Test reference number extraction"""
    print("\nTesting reference extraction...")
    
    try:
        from improved_matching_algorithm import ImprovedTransactionMatcher
        
        matcher = ImprovedTransactionMatcher()
        
        # Test check number extraction
        bank_text = "CHQ NO ******** TT25182CV3YG"
        ledger_text = "********** PV-353206"
        
        bank_refs = matcher.extract_reference_numbers(bank_text)
        ledger_refs = matcher.extract_reference_numbers(ledger_text)
        
        print(f"✓ Bank references: {bank_refs}")
        print(f"✓ Ledger references: {ledger_refs}")
        
        # Check if check numbers match
        if 'bank_check_number' in bank_refs and 'ledger_check_number' in ledger_refs:
            if bank_refs['bank_check_number'] == ledger_refs['ledger_check_number']:
                print("✓ Check numbers match: CHQ NO ******** = **********")
                return True
        
        print("✗ Check numbers don't match as expected")
        return False
        
    except Exception as e:
        print(f"✗ Reference extraction test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("RFSA Transaction Matching Improvements Test")
    print("=" * 50)
    
    results = []
    
    # Test 1: Excel parsing
    results.append(test_excel_parsing())
    
    # Test 2: Matching algorithm
    results.append(test_matching_algorithm())
    
    # Test 3: Reference extraction
    results.append(test_reference_extraction())
    
    # Summary
    print("\n" + "=" * 50)
    print("TEST SUMMARY")
    print("=" * 50)
    
    passed = sum(results)
    total = len(results)
    
    print(f"Tests passed: {passed}/{total}")
    
    if passed == total:
        print("✓ All tests passed! The improvements are working correctly.")
        print("\nKey improvements implemented:")
        print("- Enhanced reference matching (CHQ NO ******** = **********)")
        print("- RFSA Excel format detection and parsing")
        print("- Improved amount compatibility checking")
        print("- Better date tolerance handling")
        print("- Comprehensive confidence scoring")
    else:
        print("✗ Some tests failed. Check the output above for details.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
