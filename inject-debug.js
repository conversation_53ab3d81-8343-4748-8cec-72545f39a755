// Debug script to inject into browser console
// This will help us trace what's happening with AI explanation updates

console.log('🔍 Injecting AI Explanation Debug Tools');

// Override console.log to capture our debug messages
const originalLog = console.log;
const debugLogs = [];

console.log = function(...args) {
  const message = args.join(' ');
  if (message.includes('[AI_UPDATE]') || message.includes('[RESULT]') || message.includes('Processing completed')) {
    debugLogs.push({
      timestamp: new Date().toISOString(),
      message: message
    });
  }
  originalLog.apply(console, args);
};

// Function to check current state
window.debugAIState = function() {
  console.log('🔍 Current AI Debug State:');
  console.log('Debug logs captured:', debugLogs.length);
  
  debugLogs.forEach((log, index) => {
    console.log(`${index + 1}. [${log.timestamp}] ${log.message}`);
  });
  
  // Try to access React state if possible
  const reactRoot = document.querySelector('#__next');
  if (reactRoot && reactRoot._reactInternalFiber) {
    console.log('React fiber found, attempting to access state...');
  }
  
  return {
    debugLogs,
    totalLogs: debugLogs.length
  };
};

// Function to monitor fetch requests
const originalFetch = window.fetch;
window.fetch = function(...args) {
  const url = args[0];
  if (typeof url === 'string' && url.includes('process-files-stream')) {
    console.log('🌊 Streaming request detected:', url);
  }
  return originalFetch.apply(this, args);
};

// Monitor for AI explanation updates in localStorage
const originalSetItem = localStorage.setItem;
localStorage.setItem = function(key, value) {
  if (key === 'bank-reconciliation-results') {
    try {
      const data = JSON.parse(value);
      if (data && data[0] && data[0].result && data[0].result.discrepancies) {
        const aiCount = data[0].result.discrepancies.filter(d => 
          d.aiExplanation && d.aiExplanation !== "AI analysis in progress..."
        ).length;
        console.log(`💾 LocalStorage updated: ${aiCount} AI explanations found`);
      }
    } catch (e) {
      // Ignore parsing errors
    }
  }
  return originalSetItem.apply(this, arguments);
};

console.log('✅ Debug tools injected. Use window.debugAIState() to check status');
console.log('✅ Monitoring fetch requests and localStorage updates');
