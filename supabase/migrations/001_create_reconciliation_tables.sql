-- Create reconciliation_reports table
CREATE TABLE IF NOT EXISTS reconciliation_reports (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  report_id TEXT UNIQUE NOT NULL,
  uploaded_at TIMESTAMP WITH TIME ZONE NOT NULL,
  bank_statement_filename TEXT NOT NULL,
  bank_total_transactions INTEGER NOT NULL,
  ledger_filename TEXT NOT NULL,
  ledger_total_transactions INTEGER NOT NULL,
  matched_transactions INTEGER NOT NULL,
  discrepancies INTEGER NOT NULL,
  bank_balance_opening NUMERIC NOT NULL,
  bank_balance_closing NUMERIC NOT NULL,
  bank_total_debits NUMERIC NOT NULL,
  bank_total_credits NUMERIC NOT NULL,
  ledger_balance_opening NUMERIC NOT NULL,
  ledger_balance_closing NUMERIC NOT NULL,
  ledger_total_debits NUMERIC NOT NULL,
  ledger_total_credits NUMERIC NOT NULL,
  balance_difference NUMERIC NOT NULL,
  matching_rate TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- Create transactions table for storing bank and ledger transactions
CREATE TABLE IF NOT EXISTS transactions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  transaction_id TEXT NOT NULL,
  report_id UUID REFERENCES reconciliation_reports(id) ON DELETE CASCADE,
  date TIMESTAMP WITH TIME ZONE NOT NULL,
  description TEXT,
  reference TEXT,
  amount NUMERIC NOT NULL,
  type TEXT NOT NULL,
  source TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  UNIQUE(transaction_id, report_id)
);

-- Create matches table for storing matched transactions
CREATE TABLE IF NOT EXISTS matches (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  report_id UUID REFERENCES reconciliation_reports(id) ON DELETE CASCADE,
  bank_transaction_id UUID REFERENCES transactions(id) ON DELETE CASCADE,
  ledger_transaction_id UUID REFERENCES transactions(id) ON DELETE CASCADE,
  confidence NUMERIC NOT NULL,
  match_type TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  UNIQUE(bank_transaction_id, ledger_transaction_id)
);

-- Create discrepancies table for storing unmatched transactions with AI explanations
CREATE TABLE IF NOT EXISTS discrepancies (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  report_id UUID REFERENCES reconciliation_reports(id) ON DELETE CASCADE,
  discrepancy_id TEXT NOT NULL,
  transaction_id UUID REFERENCES transactions(id) ON DELETE CASCADE,
  type TEXT NOT NULL,
  ai_explanation TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  UNIQUE(discrepancy_id, report_id)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_reconciliation_reports_report_id ON reconciliation_reports(report_id);
CREATE INDEX IF NOT EXISTS idx_reconciliation_reports_created_at ON reconciliation_reports(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_transactions_report_id ON transactions(report_id);
CREATE INDEX IF NOT EXISTS idx_transactions_transaction_id ON transactions(transaction_id);
CREATE INDEX IF NOT EXISTS idx_matches_report_id ON matches(report_id);
CREATE INDEX IF NOT EXISTS idx_discrepancies_report_id ON discrepancies(report_id);

-- Add RLS policies
ALTER TABLE reconciliation_reports ENABLE ROW LEVEL SECURITY;
ALTER TABLE transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE matches ENABLE ROW LEVEL SECURITY;
ALTER TABLE discrepancies ENABLE ROW LEVEL SECURITY;

-- Create policies for authenticated users (read access)
CREATE POLICY "Allow authenticated users to read reconciliation_reports"
  ON reconciliation_reports FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Allow authenticated users to read transactions"
  ON transactions FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Allow authenticated users to read matches"
  ON matches FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Allow authenticated users to read discrepancies"
  ON discrepancies FOR SELECT
  TO authenticated
  USING (true);

-- Create policies for service role (full access)
CREATE POLICY "Allow service role full access to reconciliation_reports"
  ON reconciliation_reports FOR ALL
  TO service_role
  USING (true)
  WITH CHECK (true);

CREATE POLICY "Allow service role full access to transactions"
  ON transactions FOR ALL
  TO service_role
  USING (true)
  WITH CHECK (true);

CREATE POLICY "Allow service role full access to matches"
  ON matches FOR ALL
  TO service_role
  USING (true)
  WITH CHECK (true);

CREATE POLICY "Allow service role full access to discrepancies"
  ON discrepancies FOR ALL
  TO service_role
  USING (true)
  WITH CHECK (true);
