-- Bank Reconciliation AI Database Schema
-- This schema supports the complete reconciliation workflow with audit trails

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Note: JWT secret should be configured in Supabase dashboard under Settings > API
-- This cannot be set via SQL due to security restrictions

-- Reconciliations table - Main reconciliation records
CREATE TABLE reconciliations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    
    -- File information
    bank_statement_filename TEXT NOT NULL,
    ledger_filename TEXT NOT NULL,
    
    -- Summary statistics
    total_bank_transactions INTEGER NOT NULL DEFAULT 0,
    total_ledger_transactions INTEGER NOT NULL DEFAULT 0,
    matched_transactions INTEGER NOT NULL DEFAULT 0,
    discrepancies_count INTEGER NOT NULL DEFAULT 0,
    
    -- Balance information
    bank_opening_balance DECIMAL(15,2) NOT NULL DEFAULT 0,
    bank_closing_balance DECIMAL(15,2) NOT NULL DEFAULT 0,
    ledger_opening_balance DECIMAL(15,2) NOT NULL DEFAULT 0,
    ledger_closing_balance DECIMAL(15,2) NOT NULL DEFAULT 0,
    balance_difference DECIMAL(15,2) NOT NULL DEFAULT 0,
    
    -- Processing information
    status TEXT CHECK (status IN ('processing', 'completed', 'failed')) DEFAULT 'processing',
    processing_time_ms INTEGER,
    
    -- Additional metadata (JSON)
    metadata JSONB DEFAULT '{}'::jsonb
);

-- Transactions table - All parsed transactions
CREATE TABLE transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    reconciliation_id UUID REFERENCES reconciliations(id) ON DELETE CASCADE,
    
    -- Transaction details
    transaction_id TEXT NOT NULL, -- Original ID from source
    date DATE NOT NULL,
    description TEXT NOT NULL,
    reference TEXT,
    amount DECIMAL(15,2) NOT NULL,
    type TEXT CHECK (type IN ('credit', 'debit')) NOT NULL,
    source TEXT CHECK (source IN ('bank', 'ledger')) NOT NULL,
    
    -- Matching information
    matched BOOLEAN DEFAULT FALSE,
    match_confidence DECIMAL(3,2), -- 0.00 to 1.00
    
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Matches table - Transaction matching relationships
CREATE TABLE matches (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    reconciliation_id UUID REFERENCES reconciliations(id) ON DELETE CASCADE,
    bank_transaction_id UUID REFERENCES transactions(id) ON DELETE CASCADE,
    ledger_transaction_id UUID REFERENCES transactions(id) ON DELETE CASCADE,
    
    -- Match details
    confidence_score DECIMAL(3,2) NOT NULL, -- 0.00 to 1.00
    match_type TEXT CHECK (match_type IN ('exact', 'fuzzy', 'amount', 'manual')) NOT NULL,
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Ensure unique matches
    UNIQUE(bank_transaction_id, ledger_transaction_id)
);

-- Discrepancies table - Unmatched transactions and issues
CREATE TABLE discrepancies (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    reconciliation_id UUID REFERENCES reconciliations(id) ON DELETE CASCADE,
    transaction_id UUID REFERENCES transactions(id) ON DELETE CASCADE,
    
    -- Discrepancy details
    type TEXT CHECK (type IN ('missing_from_ledger', 'missing_from_bank', 'amount_mismatch', 'date_discrepancy')) NOT NULL,
    description TEXT NOT NULL,
    ai_explanation TEXT,
    suggested_action TEXT,
    
    -- Resolution tracking
    resolved BOOLEAN DEFAULT FALSE,
    
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX idx_reconciliations_user_id ON reconciliations(user_id);
CREATE INDEX idx_reconciliations_created_at ON reconciliations(created_at DESC);
CREATE INDEX idx_reconciliations_status ON reconciliations(status);

CREATE INDEX idx_transactions_reconciliation_id ON transactions(reconciliation_id);
CREATE INDEX idx_transactions_source ON transactions(source);
CREATE INDEX idx_transactions_matched ON transactions(matched);
CREATE INDEX idx_transactions_date ON transactions(date);

CREATE INDEX idx_matches_reconciliation_id ON matches(reconciliation_id);
CREATE INDEX idx_matches_bank_transaction_id ON matches(bank_transaction_id);
CREATE INDEX idx_matches_ledger_transaction_id ON matches(ledger_transaction_id);

CREATE INDEX idx_discrepancies_reconciliation_id ON discrepancies(reconciliation_id);
CREATE INDEX idx_discrepancies_type ON discrepancies(type);
CREATE INDEX idx_discrepancies_resolved ON discrepancies(resolved);

-- Row Level Security (RLS) Policies
ALTER TABLE reconciliations ENABLE ROW LEVEL SECURITY;
ALTER TABLE transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE matches ENABLE ROW LEVEL SECURITY;
ALTER TABLE discrepancies ENABLE ROW LEVEL SECURITY;

-- Policies for reconciliations
CREATE POLICY "Users can view their own reconciliations" ON reconciliations
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own reconciliations" ON reconciliations
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own reconciliations" ON reconciliations
    FOR UPDATE USING (auth.uid() = user_id);

-- Policies for transactions
CREATE POLICY "Users can view transactions from their reconciliations" ON transactions
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM reconciliations 
            WHERE reconciliations.id = transactions.reconciliation_id 
            AND reconciliations.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert transactions for their reconciliations" ON transactions
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM reconciliations 
            WHERE reconciliations.id = transactions.reconciliation_id 
            AND reconciliations.user_id = auth.uid()
        )
    );

-- Policies for matches
CREATE POLICY "Users can view matches from their reconciliations" ON matches
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM reconciliations 
            WHERE reconciliations.id = matches.reconciliation_id 
            AND reconciliations.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert matches for their reconciliations" ON matches
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM reconciliations 
            WHERE reconciliations.id = matches.reconciliation_id 
            AND reconciliations.user_id = auth.uid()
        )
    );

-- Policies for discrepancies
CREATE POLICY "Users can view discrepancies from their reconciliations" ON discrepancies
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM reconciliations 
            WHERE reconciliations.id = discrepancies.reconciliation_id 
            AND reconciliations.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert discrepancies for their reconciliations" ON discrepancies
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM reconciliations 
            WHERE reconciliations.id = discrepancies.reconciliation_id 
            AND reconciliations.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update discrepancies from their reconciliations" ON discrepancies
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM reconciliations 
            WHERE reconciliations.id = discrepancies.reconciliation_id 
            AND reconciliations.user_id = auth.uid()
        )
    );

-- Functions for automatic timestamp updates
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger for automatic timestamp updates
CREATE TRIGGER update_reconciliations_updated_at 
    BEFORE UPDATE ON reconciliations 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- View for reconciliation summary with statistics
CREATE VIEW reconciliation_summary AS
SELECT 
    r.*,
    COALESCE(t_stats.total_transactions, 0) as total_transactions,
    COALESCE(m_stats.total_matches, 0) as total_matches,
    COALESCE(d_stats.total_discrepancies, 0) as total_discrepancies,
    COALESCE(d_stats.resolved_discrepancies, 0) as resolved_discrepancies,
    CASE 
        WHEN r.total_bank_transactions + r.total_ledger_transactions > 0 
        THEN ROUND((COALESCE(m_stats.total_matches, 0)::decimal / (r.total_bank_transactions + r.total_ledger_transactions)) * 100, 2)
        ELSE 0 
    END as matching_percentage
FROM reconciliations r
LEFT JOIN (
    SELECT 
        reconciliation_id,
        COUNT(*) as total_transactions
    FROM transactions
    GROUP BY reconciliation_id
) t_stats ON r.id = t_stats.reconciliation_id
LEFT JOIN (
    SELECT 
        reconciliation_id,
        COUNT(*) as total_matches
    FROM matches
    GROUP BY reconciliation_id
) m_stats ON r.id = m_stats.reconciliation_id
LEFT JOIN (
    SELECT 
        reconciliation_id,
        COUNT(*) as total_discrepancies,
        COUNT(*) FILTER (WHERE resolved = true) as resolved_discrepancies
    FROM discrepancies
    GROUP BY reconciliation_id
) d_stats ON r.id = d_stats.reconciliation_id;

-- Function to get reconciliation statistics
CREATE OR REPLACE FUNCTION get_reconciliation_stats(reconciliation_uuid UUID)
RETURNS JSON AS $$
DECLARE
    result JSON;
BEGIN
    SELECT json_build_object(
        'reconciliation_id', r.id,
        'total_bank_transactions', r.total_bank_transactions,
        'total_ledger_transactions', r.total_ledger_transactions,
        'matched_transactions', r.matched_transactions,
        'discrepancies_count', r.discrepancies_count,
        'balance_difference', r.balance_difference,
        'matching_accuracy', 
            CASE 
                WHEN (r.total_bank_transactions + r.total_ledger_transactions) > 0 
                THEN ROUND((r.matched_transactions::decimal / (r.total_bank_transactions + r.total_ledger_transactions)) * 100, 2)
                ELSE 0 
            END,
        'processing_time_ms', r.processing_time_ms,
        'status', r.status
    ) INTO result
    FROM reconciliations r
    WHERE r.id = reconciliation_uuid;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
