const fs = require('fs')
const path = require('path')

// Enhanced matching with improved FIN patterns
function extractAndMatchFinReference(bank, ledger) {
  const bankDesc = bank.description || ''
  const ledgerRef = ledger.reference || ''

  // Pattern 1: "FIN ********" -> "FIN/3024/25"
  const finPattern1 = bankDesc.match(/FIN\s*(\d{4})(\d{4})/i)
  if (finPattern1) {
    const [, num, year] = finPattern1
    const expectedRef = `FIN/${num}/${year.slice(-2)}`
    if (ledgerRef.includes(expectedRef)) {
      return { matched: true, pattern: expectedRef }
    }
  }

  // Pattern 2: "FIN/2052/2025" -> "FIN/2052/25"
  const finPattern2 = bankDesc.match(/FIN\/(\d{4})\/(\d{4})/i)
  if (finPattern2) {
    const [, num, year] = finPattern2
    const expectedRef = `FIN/${num}/${year.slice(-2)}`
    if (ledgerRef.includes(expectedRef)) {
      return { matched: true, pattern: expectedRef }
    }
  }

  // Pattern 3: Direct FIN number match with flexible formatting
  const finNumber = bankDesc.match(/FIN[\/\s]*(\d{4})/i)
  if (finNumber) {
    const num = finNumber[1]
    if (ledgerRef.includes(`FIN/${num}/25`) || 
        ledgerRef.includes(`FIN/${num}`) ||
        ledgerRef.includes(`Fin/${num}`)) {
      return { matched: true, pattern: `FIN/${num}` }
    }
  }

  // Pattern 4: Reverse match - extract from ledger and match to bank
  const ledgerFinMatch = ledgerRef.match(/FIN\/(\d{4})\/(\d{2})/i)
  if (ledgerFinMatch) {
    const [, num, shortYear] = ledgerFinMatch
    const fullYear = `20${shortYear}`
    if (bankDesc.includes(`FIN ${num}${fullYear}`) ||
        bankDesc.includes(`FIN/${num}/${fullYear}`) ||
        bankDesc.includes(`FIN${num}${fullYear}`)) {
      return { matched: true, pattern: `FIN/${num}/${shortYear}` }
    }
  }

  return { matched: false }
}

function extractAndMatchCheckNumber(bank, ledger) {
  const bankText = `${bank.description || ''} ${bank.reference || ''}`
  const ledgerRef = ledger.reference || ''

  // Pattern: "********" in bank -> "CD********" or "CD-********" in ledger
  const checkNum = bankText.match(/(\d{8,})/i)
  if (checkNum) {
    const number = checkNum[1]
    if (ledgerRef.includes(`CD${number}`) || 
        ledgerRef.includes(`CD-${number}`) ||
        ledgerRef.includes(number)) {
      return { matched: true, pattern: number }
    }
  }

  // Also try shorter check numbers (6+ digits)
  const shortCheckNum = bankText.match(/(\d{6,})/i)
  if (shortCheckNum) {
    const number = shortCheckNum[1]
    if (ledgerRef.includes(`CD${number}`) || 
        ledgerRef.includes(`CD-${number}`)) {
      return { matched: true, pattern: number }
    }
  }

  return { matched: false }
}

function calculateMatchConfidence(bank, ledger) {
  let confidence = 0
  const reasons = []
  let matchType = 'fuzzy'

  // 1. Exact amount match (40% weight)
  const amountMatch = Math.abs(Math.abs(bank.amount) - Math.abs(ledger.amount)) < 1
  if (amountMatch) {
    confidence += 0.4
    reasons.push('Exact amount match')
  }

  // 2. FIN reference matching (35% weight)
  const finMatch = extractAndMatchFinReference(bank, ledger)
  if (finMatch.matched) {
    confidence += 0.35
    reasons.push(`FIN reference match: ${finMatch.pattern}`)
    matchType = 'fin_reference'
  }

  // 3. Check number pattern (20% bonus)
  const checkMatch = extractAndMatchCheckNumber(bank, ledger)
  if (checkMatch.matched) {
    confidence += 0.2
    reasons.push(`Check number match: ${checkMatch.pattern}`)
    matchType = 'exact'
  }

  // 4. Date proximity (15% weight)
  try {
    const d1 = new Date(bank.date)
    const d2 = new Date(ledger.date)
    const diffMs = Math.abs(d1.getTime() - d2.getTime())
    const daysDiff = Math.floor(diffMs / (1000 * 60 * 60 * 24))
    
    if (daysDiff === 0) {
      confidence += 0.15
      reasons.push('Same date')
    } else if (daysDiff <= 3) {
      confidence += 0.12
      reasons.push(`Date within ${daysDiff} days`)
    } else if (daysDiff <= 7) {
      confidence += 0.08
      reasons.push(`Date within ${daysDiff} days`)
    }
  } catch {
    // Date parsing failed, no score
  }

  // 5. Transaction type consistency (10% weight)
  if ((bank.type === 'debit' && ledger.type === 'credit') || 
      (bank.type === 'credit' && ledger.type === 'debit') ||
      (bank.type === ledger.type)) {
    confidence += 0.1
    reasons.push('Transaction type consistent')
  }

  return {
    bankTransaction: bank,
    ledgerTransaction: ledger,
    confidence,
    matchType,
    reasons
  }
}

function testEnhancedMatchingV2() {
  console.log('=== TESTING ENHANCED MATCHING V2 ===')
  
  const dataPath = path.join(process.cwd(), 'responses', 'real-extracted-data.json')
  if (!fs.existsSync(dataPath)) {
    console.error('Real extracted data not found.')
    return
  }
  
  const data = JSON.parse(fs.readFileSync(dataPath, 'utf8'))
  const bankTransactions = data.transactions.bank.slice(0, 50) // Test with more transactions
  const ledgerTransactions = data.transactions.ledger
  
  console.log(`Testing with ${bankTransactions.length} bank and ${ledgerTransactions.length} ledger transactions`)
  
  const matches = []
  const usedLedgerIds = new Set()
  
  for (const bankTxn of bankTransactions) {
    let bestMatch = null
    
    for (const ledgerTxn of ledgerTransactions) {
      if (usedLedgerIds.has(ledgerTxn.id)) continue
      
      const match = calculateMatchConfidence(bankTxn, ledgerTxn)
      if (match.confidence > 0.6 && (!bestMatch || match.confidence > bestMatch.confidence)) {
        bestMatch = match
      }
    }
    
    if (bestMatch) {
      matches.push(bestMatch)
      usedLedgerIds.add(bestMatch.ledgerTransaction.id)
    }
  }
  
  console.log(`\n=== ENHANCED MATCHING V2 RESULTS ===`)
  console.log(`Total matches found: ${matches.length}/${bankTransactions.length}`)
  console.log(`Match rate: ${((matches.length / bankTransactions.length) * 100).toFixed(1)}%`)
  
  // Categorize matches by type
  const matchTypes = {}
  matches.forEach(m => {
    matchTypes[m.matchType] = (matchTypes[m.matchType] || 0) + 1
  })
  
  console.log('\nMatch types:')
  Object.entries(matchTypes).forEach(([type, count]) => {
    console.log(`  ${type}: ${count}`)
  })
  
  console.log('\n=== TOP MATCHES ===')
  matches
    .sort((a, b) => b.confidence - a.confidence)
    .slice(0, 15)
    .forEach((match, i) => {
      console.log(`\n${i + 1}. Confidence: ${(match.confidence * 100).toFixed(1)}% (${match.matchType})`)
      console.log(`   Bank: ${match.bankTransaction.amount} | ${match.bankTransaction.reference} | ${match.bankTransaction.description}`)
      console.log(`   Ledger: ${match.ledgerTransaction.amount} | ${match.ledgerTransaction.reference} | ${match.ledgerTransaction.description}`)
      console.log(`   Reasons: ${match.reasons.join(', ')}`)
    })
  
  // Test specific FIN patterns from our data
  console.log('\n=== TESTING SPECIFIC FIN PATTERNS ===')
  
  // Test "FIN ********" pattern
  const finBankTxn = bankTransactions.find(t => t.description && t.description.includes('FIN ********'))
  if (finBankTxn) {
    const finLedgerTxn = ledgerTransactions.find(t => t.reference && t.reference.includes('FIN/3024/25'))
    if (finLedgerTxn) {
      const finMatch = calculateMatchConfidence(finBankTxn, finLedgerTxn)
      console.log(`FIN ******** test: ${(finMatch.confidence * 100).toFixed(1)}% confidence`)
      console.log(`  Bank: ${finBankTxn.description} | Amount: ${finBankTxn.amount}`)
      console.log(`  Ledger: ${finLedgerTxn.reference} | Amount: ${finLedgerTxn.amount}`)
      console.log(`  Reasons: ${finMatch.reasons.join(', ')}`)
    }
  }
  
  return {
    totalMatches: matches.length,
    matchRate: (matches.length / bankTransactions.length) * 100,
    averageConfidence: matches.length > 0 ? matches.reduce((sum, m) => sum + m.confidence, 0) / matches.length : 0,
    matchTypes,
    matches: matches.slice(0, 10)
  }
}

const results = testEnhancedMatchingV2()
console.log('\n=== FINAL SUMMARY ===')
console.log(`Match rate: ${results.matchRate.toFixed(1)}%`)
console.log(`Average confidence: ${(results.averageConfidence * 100).toFixed(1)}%`)
console.log(`Total exact matches: ${results.matchTypes.exact || 0}`)
console.log(`Total FIN matches: ${results.matchTypes.fin_reference || 0}`)
console.log(`Total fuzzy matches: ${results.matchTypes.fuzzy || 0}`)

// Save results for analysis
const outputPath = path.join(process.cwd(), 'responses', 'enhanced-matching-results.json')
fs.writeFileSync(outputPath, JSON.stringify(results, null, 2))
console.log(`\nResults saved to: ${outputPath}`)
