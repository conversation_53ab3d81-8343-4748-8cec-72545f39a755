/* eslint-disable @typescript-eslint/no-var-requires */
const path = require('path')
const XLSX = require('xlsx')
const fs = require('fs')

function parseAmount(v) {
  if (v == null) return 0
  const s = String(v).trim()
  if (!s) return 0
  if (/^\d{4}-\d{2}-\d{2}$/.test(s)) return 0
  if (/^\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4}$/.test(s)) return 0
  if (/^\d{2}\s+\d{2}\s+\d{4}$/.test(s)) return 0
  const paren = s.includes('(') && s.includes(')')
  let cleaned = s.replace(/[^0-9.,\-+]/g, '')
  const hasComma = cleaned.includes(',')
  const hasDot = cleaned.includes('.')
  if (hasComma && hasDot) cleaned = cleaned.replace(/,/g, '')
  else if (hasComma && !hasDot) cleaned = cleaned.replace(/,/g, '.')
  const parts = cleaned.split('.')
  if (parts.length > 2) { const dec = parts.pop(); cleaned = parts.join('') + '.' + dec }
  let n = parseFloat(cleaned)
  if (Number.isNaN(n)) n = 0
  if (paren) n = -Math.abs(n)
  return n
}

function inspect(filePath) {
  const buf = fs.readFileSync(filePath)
  const wb = XLSX.read(buf, { type: 'buffer' })
  const sheet = wb.SheetNames[0]
  const ws = wb.Sheets[sheet]
  const data = XLSX.utils.sheet_to_json(ws, { header: 1, defval: '', raw: false })
  console.log('Rows:', data.length)
  let maxCols = 0
  data.forEach(r => { if (Array.isArray(r)) maxCols = Math.max(maxCols, r.length) })
  console.log('Max columns:', maxCols)

  let countTxnRows = 0
  let rowsWithDebit = 0, rowsWithCredit = 0
  // Column indices for debit and credit (used in parseAmount calls below)
  const debitColIdx = 5, creditColIdx = 6

  // Heuristic: scan first 50 data rows for best debit/credit columns
  const candidates = new Map()
  for (let i = 0; i < Math.min(80, data.length); i++) {
    const row = data[i]
    if (!Array.isArray(row)) continue
    for (let j = 0; j < Math.min(row.length, 12); j++) {
      const amt = parseAmount(row[j])
      if (Math.abs(amt) > 0) {
        const entry = candidates.get(j) || { hits: 0 }
        entry.hits++
        candidates.set(j, entry)
      }
    }
  }
  const ranked = Array.from(candidates.entries()).sort((a,b)=>b[1].hits-a[1].hits)
  console.log('Top numeric columns (index:hits):', ranked.slice(0,6).map(([i,o])=>`${i}:${o.hits}`).join(', '))

  // Print sample rows
  for (let i = 0; i < Math.min(10, data.length); i++) {
    const row = data[i]
    console.log(i, JSON.stringify(row))
  }

  // Evaluate rows
  for (let i = 0; i < data.length; i++) {
    const row = data[i]
    if (!Array.isArray(row)) continue
    const dateCell = String(row[0] || row[4] || '').trim()
    const debitVal = parseAmount(row[5])
    const creditVal = parseAmount(row[6])
    const hasAmt = Math.abs(debitVal) > 0 || Math.abs(creditVal) > 0
    const isDateLike = /\d/.test(dateCell) && (/(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4})|(\d{2}\s+\d{2}\s+\d{4})|(\d{4}-\d{2}-\d{2})/.test(dateCell) || /^\d{5,6}$/.test(dateCell))
    if (isDateLike && hasAmt) countTxnRows++
    if (Math.abs(debitVal)>0) rowsWithDebit++
    if (Math.abs(creditVal)>0) rowsWithCredit++
  }

  console.log('Estimated transaction rows (layout pass):', countTxnRows)
  console.log('Rows with debit at col5:', rowsWithDebit)
  console.log('Rows with credit at col6:', rowsWithCredit)
}

const filePath = path.join(process.cwd(), 'example-docs', 'RFSA_July_2025_CBE_bank_statement.xlsx')
inspect(filePath)
