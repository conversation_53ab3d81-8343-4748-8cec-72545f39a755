const FormData = require('form-data')
const fetch = require('node-fetch')
const fs = require('fs')
const path = require('path')

async function quickAPITest() {
  try {
    console.log('=== QUICK API TEST WITH ENHANCED MATCHING ===')
    
    const form = new FormData()
    
    // Add files
    const pdfPath = path.join(process.cwd(), 'example-docs', 'RFSA bank statement July 2025_compressed.pdf')
    const xlsxPath = path.join(process.cwd(), 'example-docs', 'RFSA_July_2025_CBE_bank_statement.xlsx')
    
    form.append('bankStatement', fs.createReadStream(pdfPath), 'RFSA_July_2025.pdf')
    form.append('ledger', fs.createReadStream(xlsxPath), 'RFSA_July_2025_CBE_bank_statement.xlsx')
    
    console.log('Calling API with 60s timeout...')
    const response = await fetch('http://localhost:3003/api/process-files', {
      method: 'POST',
      body: form,
      timeout: 60000 // 1 minute timeout
    })
    
    if (!response.ok) {
      const text = await response.text()
      console.error(`API error: ${response.status} - ${text}`)
      return
    }
    
    const result = await response.json()
    
    if (result.success) {
      const summary = result.data.summary
      console.log('\n=== API RESPONSE SUMMARY ===')
      console.log(`Bank transactions: ${summary.totalBankTransactions}`)
      console.log(`Ledger transactions: ${summary.totalLedgerTransactions}`)
      console.log(`Matched transactions: ${summary.matchedTransactions}`)
      console.log(`Discrepancies: ${summary.discrepancies}`)
      console.log(`Average confidence: ${(summary.matchingStats.averageConfidence * 100).toFixed(1)}%`)
      
      console.log('\nMatch breakdown:')
      console.log(`  Exact matches: ${summary.matchingStats.exactMatches}`)
      console.log(`  Partial matches: ${summary.matchingStats.partialMatches}`)
      console.log(`  Fuzzy matches: ${summary.matchingStats.fuzzyMatches}`)
      
      // Show sample matches
      if (result.data.matches && result.data.matches.length > 0) {
        console.log('\n=== SAMPLE MATCHES ===')
        result.data.matches.slice(0, 5).forEach((match, i) => {
          console.log(`${i + 1}. ${match.matchType} (${(match.confidence * 100).toFixed(1)}%)`)
          console.log(`   Bank: ${match.bankTransaction.amount} | ${match.bankTransaction.reference}`)
          console.log(`   Ledger: ${match.ledgerTransaction.amount} | ${match.ledgerTransaction.reference}`)
          if (match.reasons) console.log(`   Reasons: ${match.reasons.join(', ')}`)
        })
      }
      
      return summary
    } else {
      console.error('API returned failure:', result)
    }
    
  } catch (error) {
    if (error.code === 'ECONNREFUSED') {
      console.error('Connection refused. Make sure the dev server is running on localhost:3003')
    } else if (error.name === 'FetchError' && error.type === 'request-timeout') {
      console.error('Request timed out after 60 seconds')
    } else {
      console.error('API test failed:', error.message)
    }
  }
}

quickAPITest()
