const fs = require('fs')
const path = require('path')
const XLSX = require('xlsx')

// Import parsing functions (we'll simulate them here)
function parseAmount(value) {
  if (typeof value === 'number') return value
  const raw = String(value ?? '').trim()
  if (!raw) return 0
  if (/^\d{4}-\d{2}-\d{2}$/.test(raw)) return 0
  if (/^\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4}$/.test(raw)) return 0
  if (/^\d{2}\s+\d{2}\s+\d{4}$/.test(raw)) return 0
  const parenNegative = raw.includes('(') && raw.includes(')')
  let cleaned = raw.replace(/[^0-9.,\-+]/g, '')
  const hasComma = cleaned.includes(',')
  const hasDot = cleaned.includes('.')
  if (hasComma && hasDot) cleaned = cleaned.replace(/,/g, '')
  else if (hasComma && !hasDot) cleaned = cleaned.replace(/,/g, '.')
  const parts = cleaned.split('.')
  if (parts.length > 2) { const dec = parts.pop(); cleaned = parts.join('') + '.' + dec }
  let parsed = parseFloat(cleaned)
  if (isNaN(parsed)) parsed = 0
  if (parenNegative) parsed = -Math.abs(parsed)
  return parsed
}

function standardizeDate(dateStr) {
  try {
    const trimmed = dateStr.trim()
    // Excel serial date
    if (/^\d{5,6}$/.test(trimmed)) {
      const serial = parseInt(trimmed, 10)
      if (!Number.isNaN(serial) && serial > 20000 && serial < 60000) {
        const epoch = Date.UTC(1899, 11, 30)
        const ms = serial * 24 * 60 * 60 * 1000
        return new Date(epoch + ms).toISOString()
      }
    }
    // Space delimited: "01 07 2025"
    const spaced = trimmed.replace(/\s+/g, ' ')
    if (/^\d{2}\s\d{2}\s\d{4}$/.test(spaced)) {
      const [d, m, y] = spaced.split(' ')
      return `${y}-${m.padStart(2,'0')}-${d.padStart(2,'0')}T00:00:00Z`
    }
    // ISO format
    if (/^\d{4}-\d{2}-\d{2}$/.test(trimmed)) {
      return `${trimmed}T00:00:00Z`
    }
    // dd/mm/yyyy formats
    const cleaned = trimmed.replace(/[^\d\/\-]/g, '')
    const parts = cleaned.split(/[\/\-]/)
    if (parts.length === 3) {
      let day = '01', month = '01', year = '2000'
      const [a,b,c] = parts
      if (a.length === 4 && Number(b) <= 12 && Number(c) <= 31) {
        year = a; month = b.padStart(2,'0'); day = c.padStart(2,'0')
      } else if (c.length === 4 || c.length === 2) {
        let fullYear = c
        if (c.length === 2) {
          const yy = Number(c)
          const pivot = 69
          fullYear = (yy > pivot ? `19${c}` : `20${c}`)
        }
        if (Number(a) > 12) { day = a.padStart(2,'0'); month = b.padStart(2,'0'); year = fullYear }
        else { month = a.padStart(2,'0'); day = b.padStart(2,'0'); year = fullYear }
      }
      if (Number(day) >= 1 && Number(day) <= 31 && Number(month) >=1 && Number(month) <=12) {
        return `${year}-${month}-${day}T00:00:00Z`
      }
    }
    return new Date().toISOString()
  } catch {
    return new Date().toISOString()
  }
}

async function extractExcelData() {
  console.log('=== EXTRACTING EXCEL LEDGER DATA ===')
  const xlsxPath = path.join(process.cwd(), 'example-docs', 'RFSA_July_2025_CBE_bank_statement.xlsx')
  const buffer = fs.readFileSync(xlsxPath)
  const workbook = XLSX.read(buffer, { type: 'buffer' })
  const sheetName = workbook.SheetNames[0]
  const worksheet = workbook.Sheets[sheetName]
  const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1, defval: '', raw: false })
  
  console.log(`Raw Excel rows: ${jsonData.length}`)
  
  // Find header row
  let headerIdx = -1
  let idxDate = -1, idxRef = -1, idxDesc = -1, idxDebit = -1, idxCredit = -1
  for (let i = 0; i < Math.min(30, jsonData.length); i++) {
    const row = jsonData[i]
    if (!row || !Array.isArray(row)) continue
    const norm = row.map(v => String(v || '').toLowerCase())
    const dateIndex = norm.findIndex(c => c === 'date' || c.includes('transaction date'))
    const debitIndex = norm.findIndex(c => c.includes('debit'))
    const creditIndex = norm.findIndex(c => c.includes('credit'))
    if (dateIndex !== -1 && (debitIndex !== -1 || creditIndex !== -1)) {
      headerIdx = i
      idxDate = dateIndex
      idxRef = norm.findIndex(c => c === 'reference' || c.includes('ref'))
      idxDesc = norm.findIndex(c => c.includes('trans') && c.includes('desc'))
      idxDebit = debitIndex
      idxCredit = creditIndex
      console.log(`Header found at row ${i}: date=${idxDate}, ref=${idxRef}, desc=${idxDesc}, debit=${idxDebit}, credit=${idxCredit}`)
      break
    }
  }
  
  const transactions = []
  let parsed = 0, skipped = 0
  
  for (let i = headerIdx + 1; i < jsonData.length; i++) {
    const row = jsonData[i]
    if (!row || !Array.isArray(row)) continue
    
    const cells = row.map(v => (v === null || v === undefined) ? '' : String(v).trim())
    if (cells.every(c => !c)) continue
    
    // Skip balance rows
    const rowText = cells.join(' ').toLowerCase()
    if (rowText.includes('beginning balance') || rowText.includes('opening balance')) continue
    
    const dateCell = String(row[idxDate] ?? '').trim()
    const refCell = String(row[idxRef] ?? '').trim()
    const descCell = String(row[idxDesc] ?? '').trim()
    const debitVal = parseAmount(row[idxDebit])
    const creditVal = parseAmount(row[idxCredit])
    
    const isDateLike = /\d/.test(dateCell) && (
      /^\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4}$/.test(dateCell) ||
      /^\d{2}\s+\d{2}\s+\d{4}$/.test(dateCell) ||
      /^\d{4}-\d{2}-\d{2}$/.test(dateCell) ||
      /^\d{5,6}$/.test(dateCell)
    )
    const hasAmt = Math.abs(debitVal) > 0 || Math.abs(creditVal) > 0
    
    if (!isDateLike || !hasAmt) {
      skipped++
      continue
    }
    
    const amount = Math.abs(creditVal) > 0 ? Math.abs(creditVal) : -Math.abs(debitVal)
    const type = amount >= 0 ? 'credit' : 'debit'
    
    transactions.push({
      id: `ledger_${i}`,
      rowIndex: i,
      date: standardizeDate(dateCell),
      dateRaw: dateCell,
      description: descCell || 'Transaction',
      reference: refCell || undefined,
      amount,
      debitRaw: row[idxDebit],
      creditRaw: row[idxCredit],
      type,
      source: 'ledger',
      rawRow: cells
    })
    parsed++
  }
  
  console.log(`Parsed ${parsed} transactions, skipped ${skipped}`)
  return transactions
}

async function extractPDFData() {
  console.log('=== SIMULATING PDF EXTRACTION ===')
  // Since we can't easily parse PDF in Node.js here, we'll create a mock structure
  // based on what we know from the API response
  
  // This would normally come from OCR/PDF parsing
  const mockBankTransactions = [
    {
      id: 'bank_1',
      date: '2025-07-01T00:00:00Z',
      description: 'FIN/0612/25',
      reference: 'FT25182H2FL5\\AAB',
      amount: ********,
      type: 'credit',
      source: 'bank'
    },
    {
      id: 'bank_2', 
      date: '2025-07-01T00:00:00Z',
      description: '********',
      reference: 'TT25182CV3YG',
      amount: 24000,
      type: 'debit',
      source: 'bank'
    },
    {
      id: 'bank_3',
      date: '2025-07-03T00:00:00Z', 
      description: 'FIN ********',
      reference: 'FT25184GZLG1',
      amount: 2001,
      type: 'credit',
      source: 'bank'
    }
  ]
  
  console.log(`Mock PDF transactions: ${mockBankTransactions.length}`)
  return mockBankTransactions
}

async function main() {
  try {
    const ledgerTransactions = await extractExcelData()
    const bankTransactions = await extractPDFData()
    
    const output = {
      extractedAt: new Date().toISOString(),
      summary: {
        ledgerTransactions: ledgerTransactions.length,
        bankTransactions: bankTransactions.length
      },
      ledger: ledgerTransactions.slice(0, 50), // First 50 for analysis
      bank: bankTransactions,
      sampleMatching: {
        // Look for potential matches by amount
        potentialMatches: []
      }
    }
    
    // Find potential amount matches for analysis
    for (const bank of bankTransactions.slice(0, 10)) {
      const matches = ledgerTransactions.filter(ledger => 
        Math.abs(Math.abs(bank.amount) - Math.abs(ledger.amount)) < 1
      )
      if (matches.length > 0) {
        output.sampleMatching.potentialMatches.push({
          bankTransaction: bank,
          ledgerMatches: matches
        })
      }
    }
    
    const outputPath = path.join(process.cwd(), 'responses', 'extracted-data.json')
    fs.mkdirSync(path.dirname(outputPath), { recursive: true })
    fs.writeFileSync(outputPath, JSON.stringify(output, null, 2))
    
    console.log(`\n=== EXTRACTION COMPLETE ===`)
    console.log(`Ledger transactions: ${ledgerTransactions.length}`)
    console.log(`Bank transactions: ${bankTransactions.length}`)
    console.log(`Potential matches found: ${output.sampleMatching.potentialMatches.length}`)
    console.log(`Data saved to: ${outputPath}`)
    
    // Print sample transactions for quick review
    console.log('\n=== SAMPLE LEDGER TRANSACTIONS ===')
    ledgerTransactions.slice(0, 5).forEach(t => {
      console.log(`${t.date} | ${t.amount} | ${t.reference} | ${t.description}`)
    })
    
    console.log('\n=== SAMPLE BANK TRANSACTIONS ===')
    bankTransactions.slice(0, 5).forEach(t => {
      console.log(`${t.date} | ${t.amount} | ${t.reference} | ${t.description}`)
    })
    
  } catch (error) {
    console.error('Extraction failed:', error)
  }
}

main()
