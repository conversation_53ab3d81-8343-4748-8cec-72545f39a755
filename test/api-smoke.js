/*
  Smoke test for Next.js API route /api/process-files
  - Uses global fetch, File, and FormData (Node 18+)
  - Sends sample RFSA PDF and the real RFSA Excel ledger
*/

const fs = require('fs')
const path = require('path')

async function waitForServer(url, timeoutMs = 30000) {
  const start = Date.now()
  while (Date.now() - start < timeoutMs) {
    try {
      const resp = await fetch(url, { method: 'HEAD' })
      if (resp.ok || resp.status === 405) return true
    } catch {}
    await new Promise(r => setTimeout(r, 1000))
  }
  return false
}

async function detectServerBaseURL() {
  const candidates = [3000, 3001, 3002, 3003].map(p => `http://localhost:${p}`)
  for (const base of candidates) {
    const ok = await waitForServer(base, 5000)
    if (ok) return base
  }
  return null
}

async function run() {
  try {
    const baseURL = await detectServerBaseURL()
    if (!baseURL) {
      console.error('Dev server is not responding at ports 3000-3003')
      process.exit(1)
    }
    const apiUrl = `${baseURL}/api/process-files`

    // Prepare files
    const pdfPath = path.join(process.cwd(), 'example-docs', 'RFSA bank statement July 2025_compressed.pdf')
    if (!fs.existsSync(pdfPath)) {
      console.error('Sample PDF not found at', pdfPath)
      process.exit(1)
    }

    const pdfBuffer = fs.readFileSync(pdfPath)
    const pdfFile = new File([pdfBuffer], 'RFSA_July_2025.pdf', { type: 'application/pdf' })

    const xlsxPath = path.join(process.cwd(), 'example-docs', 'RFSA_July_2025_CBE_bank_statement.xlsx')
    if (!fs.existsSync(xlsxPath)) {
      console.error('Excel ledger not found at', xlsxPath)
      process.exit(1)
    }
    const xlsxBuffer = fs.readFileSync(xlsxPath)
    const xlsxFile = new File([xlsxBuffer], 'RFSA_July_2025_CBE_bank_statement.xlsx', {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    })

    const form = new FormData()
    // Field names must match route.ts
    form.append('bankStatement', pdfFile)
    form.append('ledger', xlsxFile)

    console.log('Posting files to API route...')
    const controller = new AbortController()
    const timeout = setTimeout(() => controller.abort(), 180000) // 3 minutes
    let resp
    try {
      resp = await fetch(apiUrl, {
        method: 'POST',
        body: form,
        signal: controller.signal,
      })
    } finally {
      clearTimeout(timeout)
    }

    const text = await resp.text()
    if (!resp.ok) {
      console.error('API error:', resp.status, text)
      process.exit(1)
    }

    let data
    try { data = JSON.parse(text) } catch { data = { raw: text } }

    console.log('Success:', JSON.stringify({
      success: data?.success,
      totals: {
        bank: data?.data?.summary?.totalBankTransactions,
        ledger: data?.data?.summary?.totalLedgerTransactions,
        matches: data?.data?.summary?.matchedTransactions,
        discrepancies: data?.data?.summary?.discrepancies,
      }
    }, null, 2))

    process.exit(0)
  } catch (err) {
    console.error('Smoke test failed:', err?.message || err)
    process.exit(1)
  }
}

run()
