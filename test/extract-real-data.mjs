import fs from 'fs'
import path from 'path'
import FormData from 'form-data'
import fetch from 'node-fetch'

async function extractRealData() {
  try {
    console.log('=== EXTRACTING REAL DATA FROM API ===')
    
    const form = new FormData()
    
    // Add PDF file
    const pdfPath = path.join(process.cwd(), 'example-docs', 'RFSA bank statement July 2025_compressed.pdf')
    const pdfBuffer = fs.readFileSync(pdfPath)
    form.append('bankStatement', pdfBuffer, {
      filename: 'RFSA_July_2025.pdf',
      contentType: 'application/pdf'
    })
    
    // Add Excel file
    const xlsxPath = path.join(process.cwd(), 'example-docs', 'RFSA_July_2025_CBE_bank_statement.xlsx')
    const xlsxBuffer = fs.readFileSync(xlsxPath)
    form.append('ledger', xlsxBuffer, {
      filename: 'RFSA_July_2025_CBE_bank_statement.xlsx',
      contentType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    })
    
    console.log('Calling API to extract real data...')
    const response = await fetch('http://localhost:3002/api/process-files', {
      method: 'POST',
      body: form,
      timeout: 300000 // 5 minutes
    })
    
    if (!response.ok) {
      const text = await response.text()
      throw new Error(`API error: ${response.status} ${response.statusText} - ${text}`)
    }
    
    const result = await response.json()
    
    if (!result.success) {
      throw new Error('API returned failure')
    }
    
    // Extract the raw transaction data from discrepancies
    const bankTransactions = []
    const ledgerTransactions = []
    
    // Get bank transactions from discrepancies
    result.data.discrepancies.forEach(disc => {
      if (disc.type === 'missing_from_ledger' && disc.transaction.source === 'bank') {
        bankTransactions.push(disc.transaction)
      }
      if (disc.type === 'missing_from_bank' && disc.transaction.source === 'ledger') {
        ledgerTransactions.push(disc.transaction)
      }
      // Also collect potential matches
      if (disc.potentialMatches) {
        disc.potentialMatches.forEach(match => {
          if (match.source === 'ledger' && !ledgerTransactions.find(t => t.id === match.id)) {
            ledgerTransactions.push(match)
          }
        })
      }
    })
    
    // Get matched transactions
    result.data.matches.forEach(match => {
      if (!bankTransactions.find(t => t.id === match.bankTransaction.id)) {
        bankTransactions.push(match.bankTransaction)
      }
      if (!ledgerTransactions.find(t => t.id === match.ledgerTransaction.id)) {
        ledgerTransactions.push(match.ledgerTransaction)
      }
    })
    
    const analysis = {
      extractedAt: new Date().toISOString(),
      apiResponse: {
        summary: result.data.summary,
        totalMatches: result.data.matches.length,
        totalDiscrepancies: result.data.discrepancies.length
      },
      transactions: {
        bank: bankTransactions.slice(0, 100), // First 100 for analysis
        ledger: ledgerTransactions.slice(0, 100)
      },
      patterns: {
        bankReferences: [...new Set(bankTransactions.map(t => t.reference).filter(Boolean))].slice(0, 20),
        ledgerReferences: [...new Set(ledgerTransactions.map(t => t.reference).filter(Boolean))].slice(0, 20),
        bankDescriptions: [...new Set(bankTransactions.map(t => t.description).filter(Boolean))].slice(0, 20),
        ledgerDescriptions: [...new Set(ledgerTransactions.map(t => t.description).filter(Boolean))].slice(0, 20)
      },
      amountAnalysis: {
        bankAmounts: bankTransactions.map(t => ({ id: t.id, amount: t.amount, type: t.type })).slice(0, 20),
        ledgerAmounts: ledgerTransactions.map(t => ({ id: t.id, amount: t.amount, type: t.type })).slice(0, 20)
      },
      potentialMatches: [],
      referencePatterns: {
        bankRefs: bankTransactions.map(t => t.reference).filter(Boolean).slice(0, 30),
        ledgerRefs: ledgerTransactions.map(t => t.reference).filter(Boolean).slice(0, 30)
      }
    }
    
    // Find potential matches by amount (within 1 unit)
    bankTransactions.slice(0, 50).forEach(bank => {
      const matches = ledgerTransactions.filter(ledger => {
        const amountMatch = Math.abs(Math.abs(bank.amount) - Math.abs(ledger.amount)) < 1
        return amountMatch
      })
      
      if (matches.length > 0) {
        analysis.potentialMatches.push({
          bankTransaction: {
            id: bank.id,
            amount: bank.amount,
            reference: bank.reference,
            description: bank.description,
            date: bank.date,
            type: bank.type
          },
          ledgerMatches: matches.map(m => ({
            id: m.id,
            amount: m.amount,
            reference: m.reference,
            description: m.description,
            date: m.date,
            type: m.type
          }))
        })
      }
    })
    
    // Reference pattern analysis
    const checkNumberMatches = []
    bankTransactions.forEach(bank => {
      if (bank.reference) {
        // Look for check number patterns
        const checkMatch = bank.reference.match(/(\d{8,})/i)
        if (checkMatch) {
          const checkNum = checkMatch[1]
          const ledgerMatches = ledgerTransactions.filter(ledger => 
            ledger.reference && ledger.reference.includes(checkNum)
          )
          if (ledgerMatches.length > 0) {
            checkNumberMatches.push({
              bankRef: bank.reference,
              checkNumber: checkNum,
              ledgerMatches: ledgerMatches.map(l => l.reference)
            })
          }
        }
      }
    })
    
    analysis.referenceAnalysis = {
      checkNumberMatches,
      sampleBankRefs: bankTransactions.map(t => t.reference).filter(Boolean).slice(0, 10),
      sampleLedgerRefs: ledgerTransactions.map(t => t.reference).filter(Boolean).slice(0, 10)
    }
    
    // Save the analysis
    const outputPath = path.join(process.cwd(), 'responses', 'real-extracted-data.json')
    fs.mkdirSync(path.dirname(outputPath), { recursive: true })
    fs.writeFileSync(outputPath, JSON.stringify(analysis, null, 2))
    
    console.log(`\n=== REAL DATA EXTRACTION COMPLETE ===`)
    console.log(`Bank transactions extracted: ${bankTransactions.length}`)
    console.log(`Ledger transactions extracted: ${ledgerTransactions.length}`)
    console.log(`Potential amount matches: ${analysis.potentialMatches.length}`)
    console.log(`Check number matches: ${checkNumberMatches.length}`)
    console.log(`Data saved to: ${outputPath}`)
    
    // Print key insights
    console.log('\n=== KEY INSIGHTS ===')
    console.log(`Total API matches: ${result.data.matches.length}`)
    console.log(`Total API discrepancies: ${result.data.discrepancies.length}`)
    console.log(`Bank balance: ${result.data.summary.bankBalance.closing}`)
    console.log(`Ledger balance: ${result.data.summary.ledgerBalance.closing}`)
    
    console.log('\n=== SAMPLE POTENTIAL MATCHES ===')
    analysis.potentialMatches.slice(0, 5).forEach((match, i) => {
      console.log(`\nMatch ${i + 1}:`)
      console.log(`  Bank: ${match.bankTransaction.amount} | ${match.bankTransaction.reference} | ${match.bankTransaction.description}`)
      match.ledgerMatches.forEach(ledger => {
        console.log(`  Ledger: ${ledger.amount} | ${ledger.reference} | ${ledger.description}`)
      })
    })
    
    console.log('\n=== REFERENCE PATTERN ANALYSIS ===')
    checkNumberMatches.slice(0, 3).forEach(match => {
      console.log(`Bank ref: ${match.bankRef} -> Check: ${match.checkNumber}`)
      match.ledgerMatches.forEach(ledgerRef => {
        console.log(`  Ledger ref: ${ledgerRef}`)
      })
    })
    
    return analysis
    
  } catch (error) {
    console.error('Real data extraction failed:', error.message)
    return null
  }
}

extractRealData()
