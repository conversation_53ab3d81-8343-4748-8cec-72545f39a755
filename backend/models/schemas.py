"""
Pydantic models and schemas for the Bank Reconciliation API
"""

from pydantic import BaseModel, Field, validator
from typing import List, Dict, Any, Optional, Union, Literal
from datetime import datetime
from enum import Enum

class TransactionType(str, Enum):
    """Transaction type enumeration"""
    DEBIT = "debit"
    CREDIT = "credit"

class TransactionSource(str, Enum):
    """Transaction source enumeration"""
    BANK = "bank"
    LEDGER = "ledger"

class MatchType(str, Enum):
    """Match type enumeration"""
    EXACT = "exact"
    FUZZY = "fuzzy"
    PARTIAL = "partial"
    MANUAL = "manual"

class DiscrepancyType(str, Enum):
    """Discrepancy type enumeration"""
    MISSING_FROM_LEDGER = "missing_from_ledger"
    MISSING_FROM_BANK = "missing_from_bank"
    AMOUNT_MISMATCH = "amount_mismatch"
    DATE_DISCREPANCY = "date_discrepancy"

class ProcessingStatus(str, Enum):
    """Processing status enumeration"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"

class ParsedTransaction(BaseModel):
    """Parsed transaction model"""
    id: str = Field(..., description="Unique transaction identifier")
    date: str = Field(..., description="Transaction date in ISO format")
    description: str = Field(..., description="Transaction description")
    amount: float = Field(..., description="Transaction amount (positive for credit, negative for debit)")
    type: TransactionType = Field(..., description="Transaction type")
    source: TransactionSource = Field(..., description="Transaction source")
    reference: Optional[str] = Field(None, description="Transaction reference number")
    
    @validator('amount')
    def validate_amount(cls, v):
        if v == 0:
            raise ValueError('Transaction amount cannot be zero')
        return v

class TransactionMatch(BaseModel):
    """Transaction match model"""
    bankTransaction: ParsedTransaction = Field(..., description="Bank transaction")
    ledgerTransaction: ParsedTransaction = Field(..., description="Ledger transaction")
    confidence: float = Field(..., ge=0.0, le=1.0, description="Match confidence score")
    matchType: MatchType = Field(..., description="Type of match")
    matchCriteria: Optional[List[str]] = Field(None, description="Criteria used for matching")

class Discrepancy(BaseModel):
    """Discrepancy model"""
    id: str = Field(..., description="Unique discrepancy identifier")
    transaction: ParsedTransaction = Field(..., description="Transaction with discrepancy")
    type: DiscrepancyType = Field(..., description="Type of discrepancy")
    description: str = Field(..., description="Human-readable discrepancy description")
    suggestedAction: str = Field(..., description="Suggested action to resolve discrepancy")
    aiExplanation: Optional[str] = Field(None, description="AI-generated explanation")
    potentialMatches: Optional[List[ParsedTransaction]] = Field(None, description="Potential matching transactions")

class MatchingStats(BaseModel):
    """Matching statistics model"""
    totalBankTransactions: int = Field(..., description="Total bank transactions")
    totalLedgerTransactions: int = Field(..., description="Total ledger transactions")
    exactMatches: int = Field(..., description="Number of exact matches")
    fuzzyMatches: int = Field(..., description="Number of fuzzy matches")
    partialMatches: int = Field(..., description="Number of partial matches")
    unmatched: int = Field(..., description="Number of unmatched transactions")
    averageConfidence: float = Field(..., ge=0.0, le=1.0, description="Average confidence score")

class MatchingResult(BaseModel):
    """Transaction matching result model"""
    matches: List[TransactionMatch] = Field(..., description="Matched transactions")
    discrepancies: List[Discrepancy] = Field(..., description="Identified discrepancies")
    matchingStats: MatchingStats = Field(..., description="Matching statistics")

class BalanceInfo(BaseModel):
    """Balance information model"""
    opening: float = Field(..., description="Opening balance")
    closing: float = Field(..., description="Closing balance")
    totalCredits: float = Field(..., description="Total credit amount")
    totalDebits: float = Field(..., description="Total debit amount")

class ReconciliationSummary(BaseModel):
    """Reconciliation summary model"""
    totalBankTransactions: int = Field(..., description="Total bank transactions")
    totalLedgerTransactions: int = Field(..., description="Total ledger transactions")
    matchedTransactions: int = Field(..., description="Number of matched transactions")
    discrepancies: int = Field(..., description="Number of discrepancies")
    bankBalance: BalanceInfo = Field(..., description="Bank balance information")
    ledgerBalance: BalanceInfo = Field(..., description="Ledger balance information")
    balanceDifference: float = Field(..., description="Difference between bank and ledger balances")
    matchingStats: MatchingStats = Field(..., description="Detailed matching statistics")

class BankStatementInfo(BaseModel):
    """Bank statement information model"""
    filename: str = Field(..., description="Bank statement filename")
    totalTransactions: int = Field(..., description="Total transactions in statement")
    accountNumber: Optional[str] = Field(None, description="Bank account number")
    statementPeriod: Optional[str] = Field(None, description="Statement period")

class LedgerInfo(BaseModel):
    """Ledger information model"""
    filename: str = Field(..., description="Ledger filename")
    totalTransactions: int = Field(..., description="Total transactions in ledger")

class ReconciliationResponse(BaseModel):
    """Complete reconciliation response model"""
    id: str = Field(..., description="Unique reconciliation identifier")
    uploadedAt: str = Field(..., description="Upload timestamp in ISO format")
    bankStatementInfo: BankStatementInfo = Field(..., description="Bank statement information")
    ledgerInfo: LedgerInfo = Field(..., description="Ledger information")
    summary: ReconciliationSummary = Field(..., description="Reconciliation summary")
    matches: List[TransactionMatch] = Field(..., description="Matched transactions")
    discrepancies: List[Discrepancy] = Field(..., description="Identified discrepancies")
    status: ProcessingStatus = Field(..., description="Processing status")

class ReconciliationRequest(BaseModel):
    """Reconciliation request model"""
    bankStatementFile: str = Field(..., description="Bank statement file path")
    ledgerFile: str = Field(..., description="Ledger file path")
    options: Optional[Dict[str, Any]] = Field(None, description="Processing options")

class ParsedBankStatement(BaseModel):
    """Parsed bank statement model"""
    transactions: List[ParsedTransaction] = Field(..., description="Parsed transactions")
    account_number: Optional[str] = Field(None, description="Account number")
    statement_period: Dict[str, str] = Field(..., description="Statement period")
    opening_balance: float = Field(..., description="Opening balance")
    closing_balance: float = Field(..., description="Closing balance")

class ParsedLedger(BaseModel):
    """Parsed ledger model"""
    transactions: List[ParsedTransaction] = Field(..., description="Parsed transactions")
    opening_balance: Optional[float] = Field(None, description="Opening balance")
    closing_balance: Optional[float] = Field(None, description="Closing balance")

class ErrorResponse(BaseModel):
    """Error response model"""
    error: str = Field(..., description="Error type")
    message: str = Field(..., description="Error message")
    details: Optional[Dict[str, Any]] = Field(None, description="Additional error details")
    timestamp: str = Field(..., description="Error timestamp")

class HealthResponse(BaseModel):
    """Health check response model"""
    status: str = Field(..., description="Service status")
    services: Dict[str, str] = Field(..., description="Individual service statuses")
    timestamp: str = Field(..., description="Health check timestamp")

# Export all models
__all__ = [
    "TransactionType",
    "TransactionSource", 
    "MatchType",
    "DiscrepancyType",
    "ProcessingStatus",
    "ParsedTransaction",
    "TransactionMatch",
    "Discrepancy",
    "MatchingStats",
    "MatchingResult",
    "BalanceInfo",
    "ReconciliationSummary",
    "BankStatementInfo",
    "LedgerInfo",
    "ReconciliationResponse",
    "ReconciliationRequest",
    "ParsedBankStatement",
    "ParsedLedger",
    "ErrorResponse",
    "HealthResponse"
]
