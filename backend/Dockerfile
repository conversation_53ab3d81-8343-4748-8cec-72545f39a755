FROM python:3.11-slim

ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1

WORKDIR /app

# Install system deps required by some Python packages (images, pdfs, cryptography)
RUN apt-get update && apt-get install -y --no-install-recommends \
	build-essential \
	gcc \
	g++ \
	libffi-dev \
	libssl-dev \
	libxml2-dev \
	libxslt1-dev \
	libjpeg-dev \
	zlib1g-dev \
	libpq-dev \
	poppler-utils \
	libgl1 \
	libglib2.0-0 \
	libsm6 \
	libxrender1 \
	libxext6 \
 && rm -rf /var/lib/apt/lists/*

# Upgrade pip and install wheels first to improve build stability
RUN pip install --no-cache-dir --upgrade pip wheel setuptools

## When <PERSON><PERSON><PERSON> uses the repository root as the build context the Dockerfile will
## be executed with the context set to repo root. Copy files explicitly from
## the `backend` subdirectory to avoid missing-file errors.
COPY backend/requirements.txt ./requirements.txt
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code from the backend directory only
COPY backend/ .

# Make start script executable (already copied by previous COPY)
RUN chmod +x /app/start.sh

# Expose default port and run (respect $PORT if set by the platform)
EXPOSE 8000

# Use start script as entrypoint to ensure uvicorn is launched reliably
ENV PORT=8000
ENTRYPOINT ["/app/start.sh"]
