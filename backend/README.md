# Bank Reconciliation AI - FastAPI Backend

A robust FastAPI backend for automated bank reconciliation with AI-powered discrepancy analysis.

## Features

- **PDF Parsing**: Extract transactions from bank statement PDFs using PyMuPDF
- **Excel/CSV Processing**: Parse accounting ledger files with intelligent column detection
- **Transaction Matching**: Advanced algorithm with confidence scoring and fuzzy matching
- **AI Analysis**: Google Gemini integration for intelligent discrepancy explanations
- **RESTful API**: Clean, documented endpoints for frontend integration
- **Comprehensive Logging**: Structured logging with file rotation
- **Error Handling**: Custom exceptions and graceful error recovery

## Project Structure

```
backend/
├── core/
│   ├── config.py          # Application configuration
│   ├── exceptions.py      # Custom exception classes
│   └── logging_config.py  # Logging setup
├── models/
│   └── schemas.py         # Pydantic data models
├── services/
│   ├── pdf_parser.py      # PDF parsing service
│   ├── excel_parser.py    # Excel/CSV parsing service
│   ├── transaction_matcher.py  # Transaction matching algorithm
│   └── ai_analyzer.py     # AI analysis service
├── main.py               # FastAPI application
├── start.py              # Startup script
├── requirements.txt      # Python dependencies
└── .env.example         # Environment variables template
```

## Quick Start

### 1. Setup Environment

```bash
# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt
```

### 2. Configure Environment

```bash
# Copy environment template
cp .env.example .env

# Edit .env with your configuration
# Required: GOOGLE_API_KEY for AI analysis
```

### 3. Run the Server

```bash
# Using the startup script
python start.py

# Or directly with uvicorn
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

The API will be available at `http://localhost:8000`

## API Endpoints

### Core Endpoints

- `GET /` - Health check
- `GET /health` - Detailed health status
- `POST /api/v1/process-files` - Process bank statement and ledger files
- `POST /api/v1/parse-pdf` - Parse PDF bank statement
- `POST /api/v1/parse-excel` - Parse Excel/CSV ledger
- `POST /api/v1/reconcile` - Perform transaction reconciliation

### Documentation

- `GET /docs` - Interactive API documentation (Swagger UI)
- `GET /redoc` - Alternative API documentation

## Configuration

Key environment variables:

```env
# AI Configuration
GOOGLE_API_KEY=your_google_api_key_here
AI_MODEL=gemini-2.5-flash
AI_TEMPERATURE=0.1

# File Processing
MAX_FILE_SIZE_MB=50
UPLOAD_DIR=./uploads

# Transaction Matching
MATCHING_CONFIDENCE_THRESHOLD=0.7
DATE_TOLERANCE_DAYS=5
FUZZY_MATCH_THRESHOLD=0.6
```

## Services Overview

### PDF Parser Service

- Extracts account information, balances, and transactions
- Handles various PDF formats with regex patterns
- Fallback to mock data for testing

### Excel Parser Service

- Intelligent column detection for transaction data
- Multi-encoding support (UTF-8, Latin-1, CP1252)
- Data cleaning and standardization

### Transaction Matcher Service

- Multi-phase matching: exact, fuzzy, amount-based
- Confidence scoring with configurable thresholds
- Comprehensive discrepancy generation

### AI Analyzer Service

- Google Gemini integration for discrepancy analysis
- Batch processing with rate limiting
- Fallback explanations when AI is unavailable

## Error Handling

The backend includes comprehensive error handling:

- `ValidationError`: Input validation failures
- `ProcessingError`: File processing issues
- `ParsingError`: PDF/Excel parsing failures
- `MatchingError`: Transaction matching problems
- `AIAnalysisError`: AI service failures
- `DatabaseError`: Database operation failures

## Logging

Structured logging with multiple levels:

- Console output for development
- File rotation for production
- Request/response logging
- Error tracking with stack traces

## Testing

```bash
# Run tests
pytest

# Run with coverage
pytest --cov=.

# Run specific test file
pytest tests/test_pdf_parser.py
```

## Development

### Code Quality

```bash
# Format code
black .
isort .

# Lint code
flake8 .
mypy .
```

### Adding New Features

1. Create service in `services/` directory
2. Add data models to `schemas.py`
3. Register endpoints in `main.py`
4. Add configuration to `config.py`
5. Update tests and documentation

## Production Deployment

### Docker (Recommended)

```dockerfile
FROM python:3.11-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 8000

CMD ["python", "start.py"]
```

### Environment Setup

- Set `DEBUG=False`
- Configure proper `SECRET_KEY`
- Set up database connections
- Configure logging levels
- Set resource limits

## Integration with Frontend

The backend is designed to integrate seamlessly with the Next.js frontend:

1. CORS configured for frontend origins
2. Consistent JSON response format
3. Comprehensive error messages
4. File upload handling
5. Real-time processing status

## Future Enhancements

- [ ] Database integration (Supabase/PostgreSQL)
- [ ] Authentication and authorization
- [ ] Caching layer (Redis)
- [ ] Background job processing
- [ ] Metrics and monitoring
- [ ] Rate limiting
- [ ] API versioning

## Support

For issues and questions:

1. Check the logs in `logs/app.log`
2. Verify environment configuration
3. Test with sample files
4. Review API documentation at `/docs`

## License

This project is part of the Bank Reconciliation AI MVP.
