#!/usr/bin/env python3
"""
Enhanced RFSA Bank Statement Parser
Specifically designed to parse RFSA bank statements that return all transactions properly.
"""

import re
import logging
from typing import List, Optional
from datetime import datetime
from models.schemas import ParsedTransaction, TransactionType, TransactionSource

logger = logging.getLogger(__name__)

class EnhancedRFSAParser:
    """Enhanced parser specifically for RFSA bank statements"""

    def __init__(self):
        # RFSA-specific transaction patterns
        self.transaction_patterns = [
            # Pattern 1: Full transaction line with all components
            r'(\d{2}\s\d{2}\s\d{4})\s+([A-Z0-9\s/]+?)\s+([A-Z0-9]+)\s+.*?(-?\d{1,3}(?:,\d{3})*\.?\d{2})\s+(-?\d{1,3}(?:,\d{3})*\.?\d{2})\s+(\d{1,3}(?:,\d{3})*\.?\d{2})',
            # Pattern 2: Simplified transaction line
            r'(\d{2}\s\d{2}\s\d{4})\s+(.*?)\s+(-?\d{1,3}(?:,\d{3})*\.?\d{2})',
            # Pattern 3: Check number pattern
            r'(\d{2}\s\d{2}\s\d{4})\s+(CHQ\s+NO\s+\d+)\s+(.*?)\s+(-?\d{1,3}(?:,\d{3})*\.?\d{2})',
        ]

    def parse_transactions(self, text: str) -> List[ParsedTransaction]:
        """Parse all transactions from RFSA bank statement text"""
        transactions = []
        lines = text.split('\n')

        logger.info(f"Processing {len(lines)} lines for RFSA transactions")

        transaction_counter = 0

        for i, line in enumerate(lines):
            line = line.strip()

            # Skip header lines and short lines
            if len(line) < 20 or self._is_header_line(line):
                continue

            # Check if line contains date pattern (RFSA uses DD MM YYYY)
            if not re.search(r'\d{2}\s\d{2}\s\d{4}', line):
                continue

            # Try to parse transaction from this line
            transaction = self._parse_transaction_line(line, transaction_counter + 1)
            if transaction:
                transactions.append(transaction)
                transaction_counter += 1

                # Log first few transactions for debugging
                if transaction_counter <= 5:
                    logger.info(f"Parsed transaction {transaction_counter}: {transaction.date} - {transaction.description} - {transaction.amount}")

        logger.info(f"Successfully parsed {len(transactions)} transactions from RFSA statement")
        return transactions

    def _is_header_line(self, line: str) -> bool:
        """Check if line is a header or non-transaction line"""
        header_keywords = [
            'statement', 'account', 'balance', 'period', 'date', 'description',
            'debit', 'credit', 'page', 'continued', 'forward', 'brought'
        ]
        line_lower = line.lower()
        return any(keyword in line_lower for keyword in header_keywords)

    def _parse_transaction_line(self, line: str, transaction_id: int) -> Optional[ParsedTransaction]:
        """Parse a single transaction line"""

        # Try each pattern
        for pattern in self.transaction_patterns:
            match = re.search(pattern, line, re.IGNORECASE)
            if match:
                try:
                    # Extract date (first group is always date)
                    date_str = match.group(1)
                    transaction_date = self._parse_rfsa_date(date_str)

                    # Extract description and amount based on pattern
                    if len(match.groups()) >= 4:
                        # Pattern with multiple components
                        description_parts = []
                        amount_str = None

                        for group_num in range(2, len(match.groups()) + 1):
                            group_value = match.group(group_num)

                            # Check if this group looks like an amount
                            if re.match(r'-?\d{1,3}(?:,\d{3})*\.?\d{2}', group_value):
                                if amount_str is None:  # Take the first amount we find
                                    amount_str = group_value
                            else:
                                description_parts.append(group_value)

                        description = ' '.join(description_parts).strip()

                    else:
                        # Simpler pattern
                        description = match.group(2) if len(match.groups()) >= 2 else "Bank Transaction"
                        amount_str = match.group(-1)  # Last group should be amount

                    # Parse amount
                    amount = self._parse_amount(amount_str)

                    if amount != 0:
                        # Extract reference from description
                        reference = self._extract_reference(description)

                        return ParsedTransaction(
                            id=f"bank_{transaction_id}",
                            date=transaction_date,
                            description=description,
                            amount=amount,
                            type=TransactionType.CREDIT if amount > 0 else TransactionType.DEBIT,
                            source=TransactionSource.BANK,
                            reference=reference
                        )

                except Exception as e:
                    logger.warning(f"Failed to parse transaction line: {line[:50]}... Error: {str(e)}")
                    continue

        return None

    def _parse_rfsa_date(self, date_str: str) -> str:
        """Parse RFSA date format (DD MM YYYY)"""
        try:
            parts = date_str.strip().split()
            if len(parts) == 3:
                day, month, year = parts
                parsed_date = datetime(int(year), int(month), int(day))
                return parsed_date.isoformat()
        except Exception as e:
            logger.warning(f"Failed to parse date: {date_str}, error: {str(e)}")

        return datetime.now().isoformat()

    def _parse_amount(self, amount_str: str) -> float:
        """Parse amount string to float"""
        if not amount_str:
            return 0.0

        try:
            # Remove commas and convert to float
            cleaned = amount_str.replace(',', '').strip()
            return float(cleaned)
        except (ValueError, TypeError):
            logger.warning(f"Failed to parse amount: {amount_str}")
            return 0.0

    def _extract_reference(self, description: str) -> Optional[str]:
        """Extract reference number from description"""
        if not description:
            return None

        # Look for various reference patterns
        patterns = [
            r'(CHQ\s+NO\s+\d+)',
            r'(FIN/\d+/\d+)',
            r'(TT\d+[A-Z0-9]+)',
            r'(FT\d+[A-Z0-9]+)',
            r'(PV-?\d+)',
        ]

        for pattern in patterns:
            match = re.search(pattern, description, re.IGNORECASE)
            if match:
                return match.group(1)

        return None