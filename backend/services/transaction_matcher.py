"""
Transaction Matching Service
Advanced algorithm for matching bank and ledger transactions with confidence scoring
"""

import logging
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timedelta
import asyncio
from difflib import SequenceMatcher
import re

from models.schemas import (
    ParsedTransaction, TransactionMatch, Discrepancy, MatchingResult, 
    MatchingStats, MatchType, DiscrepancyType
)
from core.config import settings

logger = logging.getLogger(__name__)

class TransactionMatcherService:
    """Service for matching bank and ledger transactions"""
    
    def __init__(self):
        self.confidence_threshold = settings.MATCHING_CONFIDENCE_THRESHOLD
        self.date_tolerance_days = settings.DATE_TOLERANCE_DAYS
        self.fuzzy_threshold = settings.FUZZY_MATCH_THRESHOLD

    async def match_transactions(
        self, 
        bank_transactions: List[ParsedTransaction], 
        ledger_transactions: List[ParsedTransaction]
    ) -> MatchingResult:
        """
        Match bank and ledger transactions using multiple criteria
        
        Args:
            bank_transactions: List of bank transactions
            ledger_transactions: List of ledger transactions
            
        Returns:
            MatchingResult with matches, discrepancies, and statistics
        """
        try:
            logger.info(f"Starting transaction matching: {len(bank_transactions)} bank, {len(ledger_transactions)} ledger")
            
            matches = []
            used_ledger_ids = set()
            used_bank_ids = set()
            
            # Phase 1: Exact matches (reference number + amount)
            exact_matches = await self._find_exact_matches(bank_transactions, ledger_transactions)
            for match in exact_matches:
                matches.append(match)
                used_bank_ids.add(match.bankTransaction.id)
                used_ledger_ids.add(match.ledgerTransaction.id)
            
            logger.info(f"Found {len(exact_matches)} exact matches")
            
            # Phase 2: Fuzzy matches (similar reference/description + amount + date range)
            remaining_bank = [t for t in bank_transactions if t.id not in used_bank_ids]
            remaining_ledger = [t for t in ledger_transactions if t.id not in used_ledger_ids]
            
            fuzzy_matches = await self._find_fuzzy_matches(remaining_bank, remaining_ledger)
            for match in fuzzy_matches:
                if match.confidence >= self.confidence_threshold:
                    matches.append(match)
                    used_bank_ids.add(match.bankTransaction.id)
                    used_ledger_ids.add(match.ledgerTransaction.id)
            
            logger.info(f"Found {len(fuzzy_matches)} fuzzy matches above threshold")
            
            # Phase 3: Amount-only matches (same amount within date range)
            remaining_bank = [t for t in bank_transactions if t.id not in used_bank_ids]
            remaining_ledger = [t for t in ledger_transactions if t.id not in used_ledger_ids]
            
            amount_matches = await self._find_amount_matches(remaining_bank, remaining_ledger)
            for match in amount_matches:
                if match.confidence >= self.confidence_threshold:
                    matches.append(match)
                    used_bank_ids.add(match.bankTransaction.id)
                    used_ledger_ids.add(match.ledgerTransaction.id)
            
            logger.info(f"Found {len(amount_matches)} amount-based matches")
            
            # Generate discrepancies for unmatched transactions
            discrepancies = await self._generate_discrepancies(
                bank_transactions, ledger_transactions, used_bank_ids, used_ledger_ids
            )
            
            # Calculate statistics
            stats = self._calculate_matching_stats(
                bank_transactions, ledger_transactions, matches, discrepancies
            )
            
            logger.info(f"Matching completed: {len(matches)} matches, {len(discrepancies)} discrepancies")
            
            return MatchingResult(
                matches=matches,
                discrepancies=discrepancies,
                matchingStats=stats
            )
            
        except Exception as e:
            logger.error(f"Transaction matching failed: {str(e)}", exc_info=True)
            raise

    async def _find_exact_matches(
        self, 
        bank_transactions: List[ParsedTransaction], 
        ledger_transactions: List[ParsedTransaction]
    ) -> List[TransactionMatch]:
        """Find exact matches based on reference number and amount (enhanced for RFSA data)"""
        matches = []
        used_ledger_ids = set()
        
        for bank_tx in bank_transactions:
            best_match = None
            best_confidence = 0.0
            
            for ledger_tx in ledger_transactions:
                if ledger_tx.id in used_ledger_ids:
                    continue
                
                # Check amount compatibility
                if not self._amounts_compatible(bank_tx.amount, ledger_tx.amount):
                    continue
                
                # Check for reference matches (handles CHQ NO ******** = CD********)
                has_ref_match = self._has_reference_match(bank_tx, ledger_tx)
                
                if has_ref_match:
                    confidence = 1.0
                    criteria = ["reference_match", "amount_match"]
                    
                    # Check date proximity for additional confidence
                    date_score = self._calculate_date_similarity(bank_tx.date, ledger_tx.date)
                    if date_score > 0.8:
                        criteria.append("date_proximity")
                    
                    if confidence > best_confidence:
                        best_confidence = confidence
                        best_match = TransactionMatch(
                            bankTransaction=bank_tx,
                            ledgerTransaction=ledger_tx,
                            confidence=confidence,
                            matchType=MatchType.EXACT,
                            matchCriteria=criteria
                        )
            
            if best_match:
                matches.append(best_match)
                used_ledger_ids.add(best_match.ledgerTransaction.id)
        
        return matches

    async def _find_fuzzy_matches(
        self, 
        bank_transactions: List[ParsedTransaction], 
        ledger_transactions: List[ParsedTransaction]
    ) -> List[TransactionMatch]:
        """Find fuzzy matches using string similarity and date proximity"""
        matches = []
        
        for bank_tx in bank_transactions:
            best_match = None
            best_confidence = 0.0
            
            for ledger_tx in ledger_transactions:
                confidence = await self._calculate_match_confidence(bank_tx, ledger_tx)
                
                if confidence > best_confidence and confidence >= self.fuzzy_threshold:
                    best_confidence = confidence
                    best_match = ledger_tx
            
            if best_match and best_confidence >= self.fuzzy_threshold:
                match = TransactionMatch(
                    bankTransaction=bank_tx,
                    ledgerTransaction=best_match,
                    confidence=best_confidence,
                    matchType=MatchType.FUZZY,
                    matchCriteria=self._get_match_criteria(bank_tx, best_match)
                )
                matches.append(match)
        
        return matches

    async def _find_amount_matches(
        self, 
        bank_transactions: List[ParsedTransaction], 
        ledger_transactions: List[ParsedTransaction]
    ) -> List[TransactionMatch]:
        """Find matches based primarily on amount and date proximity"""
        matches = []
        
        for bank_tx in bank_transactions:
            candidates = []
            
            for ledger_tx in ledger_transactions:
                # Check amount compatibility
                if not self._amounts_compatible(bank_tx.amount, ledger_tx.amount):
                    continue
                
                # Check date proximity
                date_score = self._calculate_date_similarity(bank_tx.date, ledger_tx.date)
                if date_score < 0.3:  # Too far apart
                    continue
                
                # Calculate confidence based on amount exactness and date proximity
                amount_score = 1.0 if abs(bank_tx.amount) == abs(ledger_tx.amount) else 0.8
                confidence = (amount_score * 0.7) + (date_score * 0.3)
                
                candidates.append((ledger_tx, confidence))
            
            # Take the best candidate if above threshold
            if candidates:
                best_ledger, best_confidence = max(candidates, key=lambda x: x[1])
                
                if best_confidence >= 0.6:  # Lower threshold for amount-only matches
                    match = TransactionMatch(
                        bankTransaction=bank_tx,
                        ledgerTransaction=best_ledger,
                        confidence=best_confidence,
                        matchType=MatchType.PARTIAL,
                        matchCriteria=["amount", "date_proximity"]
                    )
                    matches.append(match)
        
        return matches

    async def _calculate_match_confidence(
        self, 
        bank_tx: ParsedTransaction, 
        ledger_tx: ParsedTransaction
    ) -> float:
        """Calculate confidence score for a potential match (enhanced for RFSA patterns)"""
        factors = []
        
        # Amount similarity (highest weight - 40%)
        if self._amounts_compatible(bank_tx.amount, ledger_tx.amount):
            if abs(bank_tx.amount) == abs(ledger_tx.amount):
                factors.append(("amount_exact", 1.0, 0.4))
            else:
                amount_ratio = min(abs(bank_tx.amount), abs(ledger_tx.amount)) / max(abs(bank_tx.amount), abs(ledger_tx.amount))
                factors.append(("amount_similar", amount_ratio, 0.35))
        else:
            return 0.0  # Incompatible amounts = no match
        
        # Reference matching (30% weight) - Enhanced for RFSA patterns
        ref_score = 0.0
        if self._has_reference_match(bank_tx, ledger_tx):
            ref_score = 1.0
        elif bank_tx.reference and ledger_tx.reference:
            ref_score = self._calculate_string_similarity(bank_tx.reference, ledger_tx.reference)
        factors.append(("reference", ref_score, 0.3))
        
        # Date proximity (20% weight)
        date_score = self._calculate_date_similarity(bank_tx.date, ledger_tx.date)
        factors.append(("date", date_score, 0.2))
        
        # Description similarity (10% weight)
        desc_similarity = self._calculate_string_similarity(
            bank_tx.description, ledger_tx.description
        )
        factors.append(("description", desc_similarity, 0.1))
        
        # Calculate weighted confidence
        total_weight = sum(weight for _, _, weight in factors)
        confidence = sum(score * weight for _, score, weight in factors) / total_weight
        
        return confidence

    def _amounts_compatible(self, amount1: float, amount2: float) -> bool:
        """Check if two amounts are compatible for matching"""
        # Same absolute value
        if abs(amount1) == abs(amount2):
            return True
        
        # Close amounts (within 1% for rounding differences)
        diff_ratio = abs(abs(amount1) - abs(amount2)) / max(abs(amount1), abs(amount2))
        return diff_ratio <= 0.01
    
    def _extract_check_number(self, text: str) -> Optional[str]:
        """Extract check number from transaction text"""
        # Pattern for "CHQ NO ********" -> "********"
        chq_match = re.search(r'CHQ\s+NO\s+(\d+)', text, re.IGNORECASE)
        if chq_match:
            return chq_match.group(1)
        
        # Pattern for "CD********" -> "********"
        cd_match = re.search(r'CD(\d+)', text, re.IGNORECASE)
        if cd_match:
            return cd_match.group(1)
        
        return None
    
    def _extract_voucher_number(self, text: str) -> Optional[str]:
        """Extract voucher number from transaction text"""
        # Pattern for "PV-353074" or "Pv-353074"
        pv_match = re.search(r'PV-?(\d+)', text, re.IGNORECASE)
        if pv_match:
            return pv_match.group(1)
        
        return None
    
    def _has_reference_match(self, bank_tx: ParsedTransaction, ledger_tx: ParsedTransaction) -> bool:
        """Check if transactions have matching reference numbers (handles RFSA-style matching)"""
        # Extract check numbers
        bank_check = self._extract_check_number(f"{bank_tx.description} {bank_tx.reference or ''}")
        ledger_check = self._extract_check_number(f"{ledger_tx.description} {ledger_tx.reference or ''}")
        
        if bank_check and ledger_check and bank_check == ledger_check:
            return True
        
        # Extract voucher numbers
        bank_voucher = self._extract_voucher_number(f"{bank_tx.description} {bank_tx.reference or ''}")
        ledger_voucher = self._extract_voucher_number(f"{ledger_tx.description} {ledger_tx.reference or ''}")
        
        if bank_voucher and ledger_voucher and bank_voucher == ledger_voucher:
            return True
        
        # Direct reference match
        if bank_tx.reference and ledger_tx.reference:
            return bank_tx.reference.upper().strip() == ledger_tx.reference.upper().strip()
        
        return False

    def _calculate_date_similarity(self, date1: str, date2: str) -> float:
        """Calculate similarity score based on date proximity"""
        try:
            dt1 = datetime.fromisoformat(date1.replace('Z', '+00:00'))
            dt2 = datetime.fromisoformat(date2.replace('Z', '+00:00'))
            
            diff_days = abs((dt1 - dt2).days)
            
            if diff_days == 0:
                return 1.0
            elif diff_days <= self.date_tolerance_days:
                return 1.0 - (diff_days / self.date_tolerance_days) * 0.3
            else:
                return max(0.0, 1.0 - (diff_days / 30.0))  # Decay over 30 days
                
        except Exception:
            return 0.5  # Default score if date parsing fails

    def _calculate_string_similarity(self, str1: str, str2: str) -> float:
        """Calculate string similarity using sequence matcher"""
        if not str1 or not str2:
            return 0.0
        
        # Normalize strings
        s1 = re.sub(r'[^\w\s]', '', str1.upper().strip())
        s2 = re.sub(r'[^\w\s]', '', str2.upper().strip())
        
        return SequenceMatcher(None, s1, s2).ratio()

    def _get_match_criteria(
        self, 
        bank_tx: ParsedTransaction, 
        ledger_tx: ParsedTransaction
    ) -> List[str]:
        """Get list of criteria that contributed to the match"""
        criteria = []
        
        if abs(bank_tx.amount) == abs(ledger_tx.amount):
            criteria.append("exact_amount")
        elif self._amounts_compatible(bank_tx.amount, ledger_tx.amount):
            criteria.append("similar_amount")
        
        if bank_tx.reference and ledger_tx.reference:
            similarity = self._calculate_string_similarity(bank_tx.reference, ledger_tx.reference)
            if similarity > 0.8:
                criteria.append("reference_match")
        
        date_score = self._calculate_date_similarity(bank_tx.date, ledger_tx.date)
        if date_score > 0.8:
            criteria.append("date_proximity")
        
        desc_similarity = self._calculate_string_similarity(bank_tx.description, ledger_tx.description)
        if desc_similarity > 0.6:
            criteria.append("description_similarity")
        
        return criteria

    async def _generate_discrepancies(
        self,
        bank_transactions: List[ParsedTransaction],
        ledger_transactions: List[ParsedTransaction],
        used_bank_ids: set,
        used_ledger_ids: set
    ) -> List[Discrepancy]:
        """Generate discrepancies for unmatched transactions"""
        discrepancies = []
        
        # Unmatched bank transactions (missing from ledger)
        for bank_tx in bank_transactions:
            if bank_tx.id not in used_bank_ids:
                discrepancy = Discrepancy(
                    id=f"disc_bank_{bank_tx.id}",
                    transaction=bank_tx,
                    type=DiscrepancyType.MISSING_FROM_LEDGER,
                    description=f"Bank transaction not found in ledger: {bank_tx.description}",
                    suggestedAction="Create journal entry to record this transaction in the ledger"
                )
                discrepancies.append(discrepancy)
        
        # Unmatched ledger transactions (missing from bank)
        for ledger_tx in ledger_transactions:
            if ledger_tx.id not in used_ledger_ids:
                discrepancy = Discrepancy(
                    id=f"disc_ledger_{ledger_tx.id}",
                    transaction=ledger_tx,
                    type=DiscrepancyType.MISSING_FROM_BANK,
                    description=f"Ledger transaction not found in bank statement: {ledger_tx.description}",
                    suggestedAction="Verify if this transaction has cleared the bank or investigate timing differences"
                )
                discrepancies.append(discrepancy)
        
        return discrepancies

    def _calculate_matching_stats(
        self,
        bank_transactions: List[ParsedTransaction],
        ledger_transactions: List[ParsedTransaction],
        matches: List[TransactionMatch],
        discrepancies: List[Discrepancy]
    ) -> MatchingStats:
        """Calculate matching statistics"""
        exact_matches = sum(1 for m in matches if m.matchType == MatchType.EXACT)
        fuzzy_matches = sum(1 for m in matches if m.matchType == MatchType.FUZZY)
        partial_matches = sum(1 for m in matches if m.matchType == MatchType.PARTIAL)
        
        avg_confidence = sum(m.confidence for m in matches) / len(matches) if matches else 0.0
        
        return MatchingStats(
            totalBankTransactions=len(bank_transactions),
            totalLedgerTransactions=len(ledger_transactions),
            exactMatches=exact_matches,
            fuzzyMatches=fuzzy_matches,
            partialMatches=partial_matches,
            unmatched=len(discrepancies),
            averageConfidence=avg_confidence
        )
