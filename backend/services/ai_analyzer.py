"""
AI Analyzer Service
Google Gemini integration for intelligent discrepancy analysis and explanations
"""

import logging
from typing import List, Dict, Any, Optional
import asyncio
import google.generativeai as genai
from datetime import datetime

from models.schemas import Discrepancy, ParsedTransaction
from core.exceptions import AIAnalysisError
from core.config import settings

logger = logging.getLogger(__name__)

class AIAnalyzerService:
    """Service for AI-powered transaction analysis using Google Gemini"""

    def __init__(self):
        if settings.GOOGLE_API_KEY:
            genai.configure(api_key=settings.GOOGLE_API_KEY)
            self.model = genai.GenerativeModel(settings.AI_MODEL)
        else:
            logger.warning("Google API key not configured, AI analysis will be disabled")
            self.model = None

    async def analyze_discrepancies(self, discrepancies: List[Discrepancy]) -> List[Discrepancy]:
        """
        Analyze discrepancies and provide AI-powered explanations

        Args:
            discrepancies: List of identified discrepancies

        Returns:
            List of discrepancies with AI explanations
        """
        # Temporarily disable AI analysis to avoid rate limits
        logger.info(f"AI analysis temporarily disabled. Returning {len(discrepancies)} discrepancies without AI explanations")
        return discrepancies

    async def _analyze_single_discrepancy(self, discrepancy: Discrepancy) -> Discrepancy:
        """Analyze a single discrepancy and generate AI explanation"""
        try:
            prompt = self._create_analysis_prompt(discrepancy)

            # Generate AI explanation
            response = await asyncio.to_thread(
                self.model.generate_content,
                prompt,
                generation_config=genai.types.GenerationConfig(
                    temperature=settings.AI_TEMPERATURE,
                    max_output_tokens=settings.AI_MAX_TOKENS
                )
            )

            ai_explanation = response.text.strip() if response.text else None

            # Create updated discrepancy with AI explanation
            updated_discrepancy = discrepancy.model_copy()
            updated_discrepancy.aiExplanation = ai_explanation

            return updated_discrepancy

        except Exception as e:
            logger.error(f"AI analysis failed for discrepancy {discrepancy.id}: {str(e)}")
            raise AIAnalysisError(f"Failed to analyze discrepancy: {str(e)}")

    def _create_analysis_prompt(self, discrepancy: Discrepancy) -> str:
        """Create a prompt for AI analysis of the discrepancy"""
        transaction = discrepancy.transaction

        base_prompt = f"""
You are a financial expert analyzing bank reconciliation discrepancies. Please provide a clear, professional explanation for the following discrepancy:

**Discrepancy Type:** {discrepancy.type.value.replace('_', ' ').title()}
**Transaction Details:**
- Date: {transaction.date}
- Description: {transaction.description}
- Amount: ${abs(transaction.amount):,.2f} ({'Credit' if transaction.amount > 0 else 'Debit'})
- Reference: {transaction.reference or 'None'}
- Source: {transaction.source.value.title()}

**Current Description:** {discrepancy.description}
**Suggested Action:** {discrepancy.suggestedAction}

Please provide:
1. A clear explanation of why this discrepancy might have occurred
2. Common causes for this type of discrepancy
3. Specific recommendations for resolution
4. Any additional considerations or red flags

Keep your response concise (under 200 words) and professional. Focus on practical insights that would help an accountant resolve this issue.
"""

        # Add specific context based on discrepancy type
        if discrepancy.type.value == "missing_from_ledger":
            base_prompt += """

**Additional Context:** This transaction appears on the bank statement but not in the accounting ledger. Consider timing differences, unrecorded transactions, or data entry errors."""

        elif discrepancy.type.value == "missing_from_bank":
            base_prompt += """

**Additional Context:** This transaction appears in the ledger but not on the bank statement. Consider outstanding checks, deposits in transit, or bank processing delays."""

        elif discrepancy.type.value == "amount_mismatch":
            base_prompt += """

**Additional Context:** The transaction appears in both records but with different amounts. Consider data entry errors, currency conversion, or fee adjustments."""

        return base_prompt.strip()

    async def generate_reconciliation_summary(
        self,
        total_matches: int,
        total_discrepancies: int,
        balance_difference: float,
        key_discrepancies: List[Discrepancy]
    ) -> str:
        """Generate an AI-powered summary of the reconciliation results"""
        if not self.model:
            return self._create_fallback_summary(total_matches, total_discrepancies, balance_difference)

        try:
            prompt = f"""
You are a financial expert reviewing bank reconciliation results. Please provide a professional executive summary:

**Reconciliation Results:**
- Total Matches: {total_matches}
- Total Discrepancies: {total_discrepancies}
- Balance Difference: ${balance_difference:,.2f}

**Key Discrepancies:**
"""

            for i, disc in enumerate(key_discrepancies[:5], 1):
                prompt += f"""
{i}. {disc.type.value.replace('_', ' ').title()}: ${abs(disc.transaction.amount):,.2f}
   Description: {disc.transaction.description}
"""

            prompt += """

Please provide:
1. Overall assessment of the reconciliation quality
2. Priority areas that need immediate attention
3. Recommended next steps
4. Any patterns or concerns identified

Keep the summary professional and actionable (under 300 words).
"""

            response = await asyncio.to_thread(
                self.model.generate_content,
                prompt,
                generation_config=genai.types.GenerationConfig(
                    temperature=0.1,
                    max_output_tokens=500
                )
            )

            return response.text.strip() if response.text else self._create_fallback_summary(
                total_matches, total_discrepancies, balance_difference
            )

        except Exception as e:
            logger.error(f"AI summary generation failed: {str(e)}")
            return self._create_fallback_summary(total_matches, total_discrepancies, balance_difference)

    def _create_fallback_summary(self, total_matches: int, total_discrepancies: int, balance_difference: float) -> str:
        """Create a fallback summary when AI is not available"""
        status = "excellent" if total_discrepancies == 0 else "good" if total_discrepancies < 5 else "needs attention"

        return f"""
**Reconciliation Summary**

The bank reconciliation has been completed with {total_matches} successful matches and {total_discrepancies} discrepancies identified.

**Overall Status:** {status.title()}
- Balance Difference: ${abs(balance_difference):,.2f}
- Match Rate: {(total_matches / (total_matches + total_discrepancies) * 100):.1f}%

**Recommended Actions:**
{"- Review and resolve the identified discrepancies" if total_discrepancies > 0 else "- No immediate action required"}
{"- Investigate the balance difference of $" + f"{abs(balance_difference):,.2f}" if abs(balance_difference) > 10 else ""}
- Update accounting records as needed
- Document any adjusting entries

This reconciliation provides a solid foundation for accurate financial reporting.
"""

    async def suggest_journal_entries(self, discrepancies: List[Discrepancy]) -> List[Dict[str, Any]]:
        """Generate suggested journal entries for discrepancies"""
        if not self.model:
            return []

        suggestions = []

        for discrepancy in discrepancies[:3]:  # Limit to top 3 for API efficiency
            try:
                prompt = f"""
You are a CPA creating journal entries. Based on this discrepancy, suggest the appropriate journal entry:

**Discrepancy:** {discrepancy.type.value.replace('_', ' ').title()}
**Transaction:** {discrepancy.transaction.description}
**Amount:** ${abs(discrepancy.transaction.amount):,.2f}
**Date:** {discrepancy.transaction.date}

Provide the journal entry in this format:
Account Name | Debit | Credit
[Account] | [Amount] |
[Account] | | [Amount]

Include a brief explanation of the entry.
"""

                response = await asyncio.to_thread(
                    self.model.generate_content,
                    prompt,
                    generation_config=genai.types.GenerationConfig(
                        temperature=0.1,
                        max_output_tokens=300
                    )
                )

                if response.text:
                    suggestions.append({
                        "discrepancy_id": discrepancy.id,
                        "journal_entry": response.text.strip(),
                        "amount": abs(discrepancy.transaction.amount)
                    })

            except Exception as e:
                logger.error(f"Journal entry suggestion failed for {discrepancy.id}: {str(e)}")
                continue

        return suggestions
