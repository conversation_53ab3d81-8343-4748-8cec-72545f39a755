"""
Excel/CSV Parser Service for Accounting Ledgers
Robust parsing using pandas with intelligent column detection
"""

import pandas as pd
import numpy as np
import logging
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
import asyncio
from fastapi import UploadFile
import io

from models.schemas import ParsedTransaction, ParsedLedger, TransactionType, TransactionSource
from core.exceptions import ExcelParsingError
from core.config import settings

logger = logging.getLogger(__name__)

class ExcelParserService:
    """Service for parsing Excel and CSV ledger files"""
    
    def __init__(self):
        # Column name mappings for intelligent detection
        self.column_mappings = {
            'date': ['date', 'transaction date', 'posting date', 'entry date', 'dt'],
            'description': ['description', 'memo', 'details', 'transaction', 'narration', 'particulars'],
            'debit': ['debit', 'dr', 'debit amount', 'withdrawal', 'expense'],
            'credit': ['credit', 'cr', 'credit amount', 'deposit', 'income'],
            'amount': ['amount', 'transaction amount', 'value'],
            'reference': ['reference', 'ref', 'ref no', 'transaction id', 'voucher no', 'check no'],
            'account': ['account', 'account code', 'gl account', 'ledger account']
        }

    async def parse_ledger(self, file: UploadFile) -> ParsedLedger:
        """
        Parse an Excel or CSV ledger file
        
        Args:
            file: UploadFile containing the Excel/CSV file
            
        Returns:
            ParsedLedger with extracted transactions
        """
        try:
            logger.info(f"Starting ledger parsing for file: {file.filename}")
            
            # Read file content
            content = await file.read()
            
            # Determine file type and read accordingly
            if file.filename.lower().endswith('.csv'):
                df = await self._read_csv_file(content)
            else:
                df = await self._read_excel_file(content)
            
            logger.info(f"Loaded DataFrame with {len(df)} rows and {len(df.columns)} columns")
            
            # Clean and prepare the DataFrame
            df = self._clean_dataframe(df)
            
            # Detect column mappings
            column_map = self._detect_columns(df)
            logger.info(f"Detected columns: {column_map}")
            
            # Parse transactions
            transactions = self._parse_transactions(df, column_map)
            
            # Calculate balances
            opening_balance, closing_balance = self._calculate_balances(transactions)
            
            logger.info(f"Successfully parsed {len(transactions)} transactions")
            
            return ParsedLedger(
                transactions=transactions,
                opening_balance=opening_balance,
                closing_balance=closing_balance
            )
            
        except Exception as e:
            logger.error(f"Excel/CSV parsing failed: {str(e)}", exc_info=True)
            raise ExcelParsingError(f"Failed to parse ledger file: {str(e)}")

    async def _read_csv_file(self, content: bytes) -> pd.DataFrame:
        """Read CSV file with multiple encoding attempts"""
        encodings = ['utf-8', 'latin-1', 'cp1252', 'iso-8859-1']
        
        for encoding in encodings:
            try:
                df = pd.read_csv(
                    io.BytesIO(content),
                    encoding=encoding,
                    skipinitialspace=True,
                    na_values=['', 'N/A', 'NULL', 'null', '-']
                )
                logger.info(f"Successfully read CSV with {encoding} encoding")
                return df
            except UnicodeDecodeError:
                continue
            except Exception as e:
                logger.warning(f"Failed to read CSV with {encoding}: {str(e)}")
                continue
        
        raise ExcelParsingError("Could not read CSV file with any supported encoding")

    async def _read_excel_file(self, content: bytes) -> pd.DataFrame:
        """Read Excel file with sheet detection"""
        try:
            # Try to read the first sheet
            excel_file = pd.ExcelFile(io.BytesIO(content))
            sheet_names = excel_file.sheet_names
            
            logger.info(f"Found Excel sheets: {sheet_names}")
            
            # Try each sheet to find the one with transaction data
            for sheet_name in sheet_names:
                try:
                    df = pd.read_excel(
                        io.BytesIO(content),
                        sheet_name=sheet_name,
                        na_values=['', 'N/A', 'NULL', 'null', '-']
                    )
                    
                    # Check if this sheet has transaction-like data
                    if self._is_transaction_sheet(df):
                        logger.info(f"Using sheet '{sheet_name}' for transaction data")
                        return df
                        
                except Exception as e:
                    logger.warning(f"Could not read sheet '{sheet_name}': {str(e)}")
                    continue
            
            # If no suitable sheet found, use the first one
            df = pd.read_excel(io.BytesIO(content), sheet_name=0)
            return df
            
        except Exception as e:
            raise ExcelParsingError(f"Could not read Excel file: {str(e)}")

    def _is_transaction_sheet(self, df: pd.DataFrame) -> bool:
        """Check if DataFrame contains transaction-like data"""
        if len(df) < 2:  # Need at least header and one row
            return False
            
        # Check for transaction-related column names
        columns_lower = [str(col).lower() for col in df.columns]
        transaction_indicators = ['date', 'amount', 'debit', 'credit', 'description', 'transaction']
        
        matches = sum(1 for indicator in transaction_indicators 
                     if any(indicator in col for col in columns_lower))
        
        return matches >= 2  # Need at least 2 transaction-related columns

    def _clean_dataframe(self, df: pd.DataFrame) -> pd.DataFrame:
        """Clean and prepare DataFrame for processing"""
        # Remove completely empty rows and columns
        df = df.dropna(how='all').dropna(axis=1, how='all')
        
        # Clean column names
        df.columns = [str(col).strip() for col in df.columns]
        
        # Remove rows that are likely headers (repeated in data)
        if len(df) > 1:
            # Check if first row might be a duplicate header
            first_row_str = [str(val).lower().strip() for val in df.iloc[0]]
            column_names_str = [str(col).lower().strip() for col in df.columns]
            
            if any(cell in column_names_str for cell in first_row_str if cell):
                df = df.iloc[1:].reset_index(drop=True)
        
        return df

    def _detect_columns(self, df: pd.DataFrame) -> Dict[str, str]:
        """Intelligently detect column mappings (enhanced for RFSA format)"""
        column_map = {}
        columns_lower = {col: str(col).lower().strip() for col in df.columns}
        
        # Special handling for RFSA format with unnamed columns
        if any('unnamed' in col.lower() for col in df.columns):
            # RFSA format detected: Account ID | Account Description | Date | Reference | Jrnl | Trans Description | Debit Amt | Credit Amt | Balance
            col_list = list(df.columns)
            if len(col_list) >= 9:
                column_map = {
                    'account': col_list[0],  # Account ID
                    'description': col_list[1],  # Account Description  
                    'date': col_list[2],  # Date
                    'reference': col_list[3],  # Reference
                    'journal': col_list[4],  # Jrnl
                    'trans_desc': col_list[5],  # Trans Description
                    'debit': col_list[6],  # Debit Amt
                    'credit': col_list[7],  # Credit Amt
                    'balance': col_list[8]  # Balance
                }
                logger.info(f"Detected RFSA format with columns: {column_map}")
                return column_map
        
        # Standard column detection
        for field, keywords in self.column_mappings.items():
            best_match = None
            best_score = 0
            
            for col, col_lower in columns_lower.items():
                score = 0
                for keyword in keywords:
                    if keyword in col_lower:
                        score += len(keyword)  # Longer matches get higher scores
                
                if score > best_score:
                    best_score = score
                    best_match = col
            
            if best_match:
                column_map[field] = best_match
        
        return column_map

    def _parse_transactions(self, df: pd.DataFrame, column_map: Dict[str, str]) -> List[ParsedTransaction]:
        """Parse transactions from DataFrame using column mappings (enhanced for RFSA format)"""
        transactions = []
        
        for index, row in df.iterrows():
            try:
                # Skip header rows and empty rows
                if self._is_header_row(row, index):
                    continue
                
                # Extract date
                transaction_date = self._extract_date(row, column_map.get('date'))
                if not transaction_date:
                    continue
                
                # Extract description (combine account description and transaction description for RFSA)
                description = self._extract_rfsa_description(row, column_map)
                
                # Extract amount (handle debit/credit or single amount column)
                amount = self._extract_amount(row, column_map)
                if amount == 0:
                    continue
                
                # Extract reference (enhanced for RFSA patterns)
                reference = self._extract_rfsa_reference(row, column_map)
                
                # Create transaction
                transaction = ParsedTransaction(
                    id=f"ledger_{index + 1}",
                    date=transaction_date,
                    description=description,
                    amount=amount,
                    type=TransactionType.CREDIT if amount > 0 else TransactionType.DEBIT,
                    source=TransactionSource.LEDGER,
                    reference=reference
                )
                
                transactions.append(transaction)
                
            except Exception as e:
                logger.warning(f"Failed to parse row {index}: {str(e)}")
                continue
        
        return transactions
    
    def _is_header_row(self, row: pd.Series, index: int) -> bool:
        """Check if row is a header row that should be skipped"""
        # Skip first few rows that might be headers
        if index < 5:
            row_str = ' '.join(str(val) for val in row if pd.notna(val)).lower()
            header_indicators = ['account id', 'general ledger', 'period from', 'filter criteria']
            if any(indicator in row_str for indicator in header_indicators):
                return True
        return False
    
    def _extract_rfsa_description(self, row: pd.Series, column_map: Dict[str, str]) -> str:
        """Extract description for RFSA format (combines multiple description fields)"""
        descriptions = []
        
        # Add account description
        if 'description' in column_map and column_map['description'] in row.index:
            desc_value = row[column_map['description']]
            if not pd.isna(desc_value) and str(desc_value).strip():
                descriptions.append(str(desc_value).strip())
        
        # Add transaction description
        if 'trans_desc' in column_map and column_map['trans_desc'] in row.index:
            trans_desc = row[column_map['trans_desc']]
            if not pd.isna(trans_desc) and str(trans_desc).strip():
                descriptions.append(str(trans_desc).strip())
        
        # Combine descriptions
        if descriptions:
            return ' - '.join(descriptions)
        
        return "Ledger Transaction"
    
    def _extract_rfsa_reference(self, row: pd.Series, column_map: Dict[str, str]) -> Optional[str]:
        """Extract reference for RFSA format (handles multiple reference fields)"""
        # Try main reference column first
        if 'reference' in column_map and column_map['reference'] in row.index:
            ref_value = row[column_map['reference']]
            if not pd.isna(ref_value) and str(ref_value).strip():
                return str(ref_value).strip()
        
        # Try transaction description for reference patterns
        if 'trans_desc' in column_map and column_map['trans_desc'] in row.index:
            trans_desc = row[column_map['trans_desc']]
            if not pd.isna(trans_desc):
                trans_str = str(trans_desc).strip()
                # Look for voucher patterns like "PV-353074"
                import re
                pv_match = re.search(r'(PV-?\d+)', trans_str, re.IGNORECASE)
                if pv_match:
                    return pv_match.group(1)
        
        return None

    def _extract_date(self, row: pd.Series, date_column: Optional[str]) -> Optional[str]:
        """Extract and normalize date from row"""
        if not date_column or date_column not in row.index:
            return None
        
        date_value = row[date_column]
        
        if pd.isna(date_value):
            return None
        
        # Handle different date types
        if isinstance(date_value, (pd.Timestamp, datetime)):
            return date_value.isoformat()
        
        # Try to parse string dates
        try:
            if isinstance(date_value, str):
                # Clean the date string
                date_str = str(date_value).strip()
                parsed_date = pd.to_datetime(date_str, infer_datetime_format=True)
                return parsed_date.isoformat()
        except:
            pass
        
        return None

    def _extract_description(self, row: pd.Series, desc_column: Optional[str]) -> str:
        """Extract description from row"""
        if desc_column and desc_column in row.index:
            desc_value = row[desc_column]
            if not pd.isna(desc_value):
                return str(desc_value).strip()
        
        # Fallback: try to find any text column
        for col in row.index:
            value = row[col]
            if not pd.isna(value) and isinstance(value, str) and len(str(value).strip()) > 5:
                return str(value).strip()
        
        return "Ledger Transaction"

    def _extract_amount(self, row: pd.Series, column_map: Dict[str, str]) -> float:
        """Extract amount from row, handling debit/credit or single amount columns"""
        amount = 0.0
        
        # Try debit/credit columns first
        if 'debit' in column_map and column_map['debit'] in row.index:
            debit_value = row[column_map['debit']]
            if not pd.isna(debit_value):
                debit_amount = self._parse_numeric_value(debit_value)
                if debit_amount > 0:
                    amount -= debit_amount  # Debits are negative
        
        if 'credit' in column_map and column_map['credit'] in row.index:
            credit_value = row[column_map['credit']]
            if not pd.isna(credit_value):
                credit_amount = self._parse_numeric_value(credit_value)
                if credit_amount > 0:
                    amount += credit_amount  # Credits are positive
        
        # If no amount from debit/credit, try amount column
        if amount == 0 and 'amount' in column_map and column_map['amount'] in row.index:
            amount_value = row[column_map['amount']]
            if not pd.isna(amount_value):
                amount = self._parse_numeric_value(amount_value)
        
        return amount

    def _extract_reference(self, row: pd.Series, ref_column: Optional[str]) -> Optional[str]:
        """Extract reference from row"""
        if ref_column and ref_column in row.index:
            ref_value = row[ref_column]
            if not pd.isna(ref_value):
                ref_str = str(ref_value).strip()
                return ref_str if ref_str else None
        
        return None

    def _parse_numeric_value(self, value: Any) -> float:
        """Parse numeric value from various formats"""
        if pd.isna(value):
            return 0.0
        
        if isinstance(value, (int, float)):
            return float(value)
        
        if isinstance(value, str):
            # Clean the string
            cleaned = value.strip().replace(',', '').replace('$', '').replace('(', '-').replace(')', '')
            
            # Handle negative indicators
            is_negative = cleaned.startswith('-') or '(' in value
            cleaned = cleaned.replace('-', '').replace('(', '').replace(')', '')
            
            try:
                amount = float(cleaned)
                return -amount if is_negative else amount
            except ValueError:
                return 0.0
        
        return 0.0

    def _calculate_balances(self, transactions: List[ParsedTransaction]) -> Tuple[Optional[float], Optional[float]]:
        """Calculate opening and closing balances from transactions"""
        if not transactions:
            return None, None
        
        # Sort transactions by date
        sorted_transactions = sorted(transactions, key=lambda t: t.date)
        
        # For ledger, we typically don't have explicit opening balance
        # We can calculate running balance if needed
        total_amount = sum(t.amount for t in transactions)
        
        # Return None for opening (unknown) and calculated total for closing
        return None, total_amount
