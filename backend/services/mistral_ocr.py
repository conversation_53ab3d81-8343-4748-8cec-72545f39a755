"""
Mistral OCR Service for Python Backend
Enhanced with table-based parsing and optimized for RFSA bank statements
"""

import base64
import json
import logging
import re
import time
from typing import List, Dict, Any, Optional
from datetime import datetime
import requests
from fastapi import UploadFile

from models.schemas import ParsedTransaction, TransactionType, TransactionSource
from core.config import settings

logger = logging.getLogger(__name__)

class MistralOCRService:
    """Mistral OCR service for extracting text from PDFs"""

    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://api.mistral.ai/v1/ocr"

    async def parse_document(self, file: UploadFile) -> Dict[str, Any]:
        """Parse a document using Mistral OCR API"""
        try:
            # Read file content and encode to base64
            content = await file.read()
            base64_content = base64.b64encode(content).decode('utf-8')

            request_body = {
                "model": "mistral-ocr-latest",
                "document": {
                    "type": "document_url",
                    "document_url": f"data:{file.content_type};base64,{base64_content}"
                },
                "include_image_base64": True
            }

            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.api_key}"
            }

            logger.info(f"Sending PDF to Mistral OCR API (size: {len(content)} bytes)")
            response = requests.post(
                self.base_url,
                headers=headers,
                json=request_body,
                timeout=120  # 2 minutes timeout for large PDFs
            )

            if not response.ok:
                error_text = response.text
                logger.error(f"Mistral OCR API error: {response.status_code} - {error_text}")
                raise Exception(f"Mistral OCR API error: {response.status_code} - {error_text}")

            result = response.json()
            logger.info(f"Mistral OCR processed {result.get('usage_info', {}).get('pages_processed', 0)} pages")
            return result

        except Exception as e:
            logger.error(f"Error calling Mistral OCR API: {str(e)}")
            raise

    async def extract_bank_transactions(self, file: UploadFile) -> List[ParsedTransaction]:
        """Extract bank transactions with enhanced error handling"""
        try:
            start_time = time.time()
            
            # Parse document with Mistral OCR
            ocr_result = await self.parse_document(file)
            
            # Log OCR result details
            pages_count = len(ocr_result.get("pages", []))
            total_chars = sum(len(page.get("markdown", "")) for page in ocr_result.get("pages", []))
            
            logger.info(f"OCR completed: {pages_count} pages, {total_chars} characters")
            
            # Combine markdown from all pages
            all_markdown = ""
            for page in ocr_result.get("pages", []):
                if "markdown" in page:
                    all_markdown += page["markdown"] + "\n\n"
                    
            # Parse transactions with detailed logging
            transactions = self._parse_transactions_from_markdown(all_markdown)
            
            # Filter out zero-amount transactions
            valid_transactions = [tx for tx in transactions if tx.amount > 0]
            
            processing_time = time.time() - start_time
            
            logger.info(f"Processing completed in {processing_time:.2f}s: "
                       f"{len(transactions)} raw transactions, "
                       f"{len(valid_transactions)} valid transactions")
                       
            # Log sample transactions for debugging
            for i, tx in enumerate(valid_transactions[:3]):
                logger.info(f"Sample transaction {i+1}: {tx.date} - {tx.description[:50]} - {tx.amount}")
                
            return valid_transactions
            
        except Exception as e:
            logger.error(f"Failed to extract bank transactions: {str(e)}", exc_info=True)
            return []

    def _parse_transactions_from_markdown(self, markdown: str) -> List[ParsedTransaction]:
        """Enhanced table-based parsing with performance optimizations"""
        transactions = []

        # Pre-compile regex patterns for better performance
        if not hasattr(self, '_compiled_patterns'):
            self._compiled_patterns = {
                'table_row': re.compile(r'\|[^|]+\|[^|]+\|[^|]+\|[^|]+\|[^|]+\|[^|]+\|[^|]+\|[^|]+\|'),
                'date_ddmmyyyy': re.compile(r'\b\d{8}\b'),
                'date_standard': re.compile(r'\b\d{1,2}[/\s-]\d{1,2}[/\s-]\d{4}\b'),
                'amount': re.compile(r'-?\d{1,3}(?:,\d{3})*\.?\d{0,2}')
            }

        # Use compiled patterns for faster matching
        table_rows = self._compiled_patterns['table_row'].findall(markdown)

        # Filter out header and separator rows
        data_rows = []
        for row in table_rows:
            lower_row = row.lower()
            if not any(header in lower_row for header in [
                'date', 'particulars', 'reference', 'narrative',
                'debit', 'credit', 'balance', '---', 'balance b/f', 'balance c/f'
            ]):
                data_rows.append(row)

        logger.info(f"Found {len(data_rows)} transaction rows in markdown tables")

        # Process in batches for better memory usage with large files
        batch_size = 100
        for i in range(0, len(data_rows), batch_size):
            batch = data_rows[i:i + batch_size]
            for index, row in enumerate(batch):
                transaction = self._parse_transaction_from_table_cells(row, i + index)
                if transaction:
                    transactions.append(transaction)

        # If table-based parsing yields too few results, fall back to line-by-line parsing
        if len(transactions) < 10:
            logger.info("Table-based parsing yielded few results, falling back to line-by-line parsing")
            
            # Split into lines and look for transaction patterns
            lines = markdown.split('\n')
            
            transaction_id = len(transactions) + 1
            for i, line in enumerate(lines):
                line = line.strip()
                
                # Skip empty lines, headers, and lines that are too short
                if not line or len(line) < 20 or self._is_header_row(line):
                    continue
                
                # Only process lines that look like transactions
                if not self._is_transaction_row(line):
                    continue
                
                # Extract transaction data
                transaction = self._parse_transaction_from_line(line, transaction_id)
                if transaction:
                    transactions.append(transaction)
                    transaction_id += 1

        logger.info(f"Total transactions extracted: {len(transactions)}")
        return transactions
        
    def _is_header_row(self, line: str) -> bool:
        """Determine if a row is a header row"""
        lower_line = line.lower()
        header_patterns = [
            'date', 'particulars', 'reference', 'narrative',
            'debit', 'credit', 'balance', '---', '===',
            'balance b/f', 'balance c/f', 'opening balance', 'closing balance',
            'total', 'subtotal', 'page', 'statement'
        ]
        
        return any(pattern in lower_line for pattern in header_patterns)
        
    def _is_transaction_row(self, line: str) -> bool:
        """Determine if a row contains transaction data"""
        lower_row = line.lower()

        # Filter out common non-transaction patterns
        exclusion_patterns = [
            'date', 'particulars', 'reference', 'narrative',
            'debit', 'credit', 'balance', '---', '===',
            'balance b/f', 'balance c/f', 'brought forward',
            'carried forward', 'opening balance', 'closing balance',
            'total', 'subtotal', 'page', 'statement'
        ]

        # Check if row contains any exclusion patterns
        for pattern in exclusion_patterns:
            if pattern in lower_row:
                return False

        # Must contain a date pattern to be a transaction
        date_patterns = [
            r'\b\d{1,2}[/\s-]\d{1,2}[/\s-]\d{4}\b',
            r'\b\d{8}\b'  # DDMMYYYY
        ]

        for pattern in date_patterns:
            if re.search(pattern, line):
                return True

        return False
    
    def _parse_transaction_from_table_cells(self, row: str, index: int) -> Optional[ParsedTransaction]:
        """Parse transaction from table row cells"""
        # Split by pipes and clean cells
        cells = [cell.strip() for cell in row.split('|') if cell.strip()]
        
        if len(cells) < 6:
            return None
            
        # Expected format: Date | Particulars | Reference | Narrative | Value Date | Debit | Credit | Balance
        date_str = cells[0] if len(cells) > 0 else ""
        particulars = cells[1] if len(cells) > 1 else ""
        reference = cells[2] if len(cells) > 2 else ""
        narrative = cells[3] if len(cells) > 3 else ""
        debit_str = cells[5] if len(cells) > 5 else ""
        credit_str = cells[6] if len(cells) > 6 else ""
        
        # Parse date using RFSA format first
        parsed_date = self._parse_date_ddmmyyyy(date_str)
        if not parsed_date:
            return None
            
        # Parse amounts
        debit_amount = self._parse_amount(debit_str)
        credit_amount = self._parse_amount(credit_str)
        
        # Determine transaction details
        if credit_amount > 0:
            amount = credit_amount
            transaction_type = TransactionType.CREDIT
        elif debit_amount > 0:
            amount = debit_amount
            transaction_type = TransactionType.DEBIT
        else:
            return None  # No valid amount
            
        description = narrative or particulars or 'Bank Transaction'
        
        return ParsedTransaction(
            id=f"mistral_{index + 1}",
            date=parsed_date,
            description=description,
            reference=reference or None,
            amount=amount,
            type=transaction_type,
            source=TransactionSource.BANK
        )
        
    def _parse_transaction_from_line(self, line: str, transaction_id: int) -> Optional[ParsedTransaction]:
        """Parse transaction from a single line of text"""
        # Look for date patterns
        date_patterns = [
            r'\b(\d{1,2}[/\s]\d{1,2}[/\s]\d{4})\b',  # DD/MM/YYYY or DD MM YYYY
            r'\b(\d{4}[/\s]\d{1,2}[/\s]\d{1,2})\b',  # YYYY/MM/DD
            r'\b(\d{8})\b',  # DDMMYYYY
        ]

        date_match = None
        for pattern in date_patterns:
            date_match = re.search(pattern, line)
            if date_match:
                break

        if not date_match:
            return None

        # Look for amounts
        amount_patterns = [
            r'(-?\d{1,3}(?:,\d{3})*\.\d{2})',  # Format: -1,234.56
            r'(-?\d+\.\d{2})',  # Format: -1234.56
            r'(-?\d{1,3}(?:,\d{3})*)',  # Format: -1,234 (no decimals)
        ]

        amounts = []
        for pattern in amount_patterns:
            amounts.extend(re.findall(pattern, line))

        if not amounts:
            return None

        # Parse the amounts found
        amount_values = []
        for amount_str in amounts:
            try:
                amount_values.append(float(amount_str.replace(',', '')))
            except ValueError:
                continue

        if not amount_values:
            return None

        # Use the last amount found (usually the transaction amount)
        amount = amount_values[-1]

        # Skip transactions with zero amounts
        if amount == 0:
            return None

        # Extract description (text between date and amount)
        date_end = date_match.end()
        amount_start = line.rfind(str(amounts[-1]))
        if amount_start > date_end:
            description = line[date_end:amount_start].strip()
        else:
            description = line[date_end:].strip()

        # Clean up description
        description = re.sub(r'\s+', ' ', description)
        description = description.strip('|').strip()

        if not description:
            description = f"Transaction on {date_match.group(1)}"

        # Parse date
        date_str = date_match.group(1)
        parsed_date = self._parse_date_ddmmyyyy(date_str)
        if not parsed_date:
            parsed_date = datetime.now().strftime('%Y-%m-%d')

        # Determine transaction type
        transaction_type = TransactionType.CREDIT if amount > 0 else TransactionType.DEBIT

        # Look for reference numbers in the description
        ref_patterns = [
            r'(CHQ\s+NO\s+\d+)',
            r'(FIN/\d+/\d+)',
            r'(CD\d+)',  # RFSA check deposit format
            r'(PV-\d+)',  # RFSA payment voucher format
            r'([A-Z0-9]{8,})',  # Long alphanumeric codes
        ]

        reference = None
        for pattern in ref_patterns:
            ref_match = re.search(pattern, description)
            if ref_match:
                reference = ref_match.group(1)
                break

        return ParsedTransaction(
            id=f"bank_{transaction_id}",
            date=parsed_date,
            description=description,
            amount=abs(amount),
            type=transaction_type,
            source=TransactionSource.BANK,
            reference=reference
        )
        
    def _parse_date_ddmmyyyy(self, date_str: str) -> Optional[str]:
        """Parse RFSA format dates (DDMMYYYY) with fallbacks"""
        try:
            # Remove any non-digits first for format detection
            digits_only = re.sub(r'[^\d]', '', date_str)
            
            # Handle DDMMYYYY format (******** -> 2025-07-29)
            if len(digits_only) == 8:
                day = digits_only[0:2]
                month = digits_only[2:4]
                year = digits_only[4:8]
                return f"{year}-{month}-{day}"
                
            # Handle DD/MM/YYYY or DD MM YYYY format
            date_parts = re.split(r'[/\s-]', date_str.strip())
            if len(date_parts) == 3:
                day, month, year = date_parts
                if len(year) == 2:
                    year = f"20{year}"
                return f"{year}-{month.zfill(2)}-{day.zfill(2)}"
                
            # Fallback: try standard datetime parsing
            try:
                parsed = datetime.strptime(date_str, '%d/%m/%Y')
                return parsed.strftime('%Y-%m-%d')
            except ValueError:
                pass
                
        except Exception as e:
            logger.warning(f"Date parsing failed for '{date_str}': {e}")
            
        return None
        
    def _parse_amount(self, amount_str: str) -> float:
        """Enhanced amount parsing with better edge case handling"""
        try:
            if not amount_str or amount_str in ['.00', '0.00', '', '-']:
                return 0.0
                
            # Remove currency symbols, commas, and extra spaces
            cleaned = re.sub(r'[$,\s]', '', amount_str)
            
            # Remove non-numeric characters except decimal point and minus
            cleaned = re.sub(r'[^\d.-]', '', cleaned)
            
            # Handle empty or invalid strings
            if not cleaned or cleaned in ['.', '-', '-.']:
                return 0.0
                
            amount = float(cleaned)
            return abs(amount)  # Always return positive, type determines debit/credit
            
        except (ValueError, TypeError):
            logger.warning(f"Amount parsing failed for '{amount_str}'")
            return 0.0


async def create_mistral_ocr_service() -> Optional[MistralOCRService]:
    """Factory function to create Mistral OCR service if API key is available"""
    api_key = settings.MISTRAL_API_KEY
    if not api_key:
        logger.warning("MISTRAL_API_KEY not found in environment variables")
        return None

    return MistralOCRService(api_key)