"""
Security middleware for FastAPI backend
Handles rate limiting, file validation, and security headers
"""

import time
import hashlib
from collections import defaultdict
from typing import Dict, List, Optional
from fastapi import HTTPException, Request, UploadFile
from fastapi.responses import Response
import logging

logger = logging.getLogger(__name__)

class SecurityConfig:
    MAX_FILE_SIZE = 50 * 1024 * 1024  # 50MB
    ALLOWED_PDF_TYPES = ["application/pdf"]
    ALLOWED_SPREADSHEET_TYPES = [
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        "application/vnd.ms-excel",
        "text/csv"
    ]
    RATE_LIMIT_REQUESTS = 30  # requests per minute
    RATE_LIMIT_WINDOW = 60  # seconds
    SUSPICIOUS_PATTERNS = [
        b"<script",
        b"javascript:",
        b"eval(",
        b"document.",
        b"window.",
    ]

class RateLimiter:
    def __init__(self):
        self.requests: Dict[str, List[float]] = defaultdict(list)

    def is_allowed(self, client_ip: str) -> bool:
        now = time.time()
        window_start = now - SecurityConfig.RATE_LIMIT_WINDOW

        # Clean old requests
        self.requests[client_ip] = [
            req_time for req_time in self.requests[client_ip]
            if req_time > window_start
        ]

        # Check if limit exceeded
        if len(self.requests[client_ip]) >= SecurityConfig.RATE_LIMIT_REQUESTS:
            return False

        # Add current request
        self.requests[client_ip].append(now)
        return True

    def get_remaining_requests(self, client_ip: str) -> int:
        now = time.time()
        window_start = now - SecurityConfig.RATE_LIMIT_WINDOW

        valid_requests = [
            req_time for req_time in self.requests[client_ip]
            if req_time > window_start
        ]

        return max(0, SecurityConfig.RATE_LIMIT_REQUESTS - len(valid_requests))

class FileSecurityValidator:
    @staticmethod
    def validate_file_type(file: UploadFile, expected_types: List[str]) -> bool:
        # First check content type if available
        if file.content_type and file.content_type in expected_types:
            return True

        # Fallback to file extension validation if content type is missing or incorrect
        if not file.filename:
            return False

        filename_lower = file.filename.lower()

        # Check for PDF files
        if "application/pdf" in expected_types and filename_lower.endswith('.pdf'):
            return True

        # Check for Excel/CSV files
        if any(excel_type in expected_types for excel_type in [
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            "application/vnd.ms-excel",
            "text/csv"
        ]):
            if filename_lower.endswith(('.xlsx', '.xls', '.csv')):
                return True

        return False

    @staticmethod
    def validate_file_size(file: UploadFile) -> bool:
        if not hasattr(file, 'size') or file.size is None:
            return True  # Size check will be done during read
        return file.size <= SecurityConfig.MAX_FILE_SIZE

    @staticmethod
    async def scan_file_content(file: UploadFile, max_scan_bytes: int = 1024 * 1024) -> bool:
        """Scan file content for suspicious patterns"""
        try:
            # Read first MB for scanning
            content = await file.read(max_scan_bytes)
            await file.seek(0)  # Reset file pointer

            # Check for suspicious patterns
            content_lower = content.lower()
            for pattern in SecurityConfig.SUSPICIOUS_PATTERNS:
                if pattern in content_lower:
                    logger.warning(f"Suspicious pattern found in file: {file.filename}")
                    return False

            return True
        except Exception as e:
            logger.error(f"Error scanning file content: {e}")
            return False

    @staticmethod
    def validate_filename(filename: str) -> bool:
        """Validate filename for security"""
        if not filename:
            return False

        # Check for path traversal
        if ".." in filename or "/" in filename or "\\" in filename:
            return False

        # Check for suspicious extensions
        suspicious_extensions = [".exe", ".bat", ".cmd", ".scr", ".vbs", ".jar"]
        if any(filename.lower().endswith(ext) for ext in suspicious_extensions):
            return False

        return True

class SecurityMiddleware:
    def __init__(self):
        self.rate_limiter = RateLimiter()
        self.file_validator = FileSecurityValidator()

    def get_client_ip(self, request: Request) -> str:
        """Extract client IP from request"""
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()

        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip

        return request.client.host if request.client else "unknown"

    async def validate_request(self, request: Request) -> None:
        """Validate incoming request for security"""
        client_ip = self.get_client_ip(request)

        # Rate limiting
        if not self.rate_limiter.is_allowed(client_ip):
            remaining = self.rate_limiter.get_remaining_requests(client_ip)
            logger.warning(f"Rate limit exceeded for IP: {client_ip}")
            raise HTTPException(
                status_code=429,
                detail=f"Rate limit exceeded. Try again later. Remaining requests: {remaining}",
                headers={"Retry-After": str(SecurityConfig.RATE_LIMIT_WINDOW)}
            )

        # Log request
        logger.info(f"Request from {client_ip}: {request.method} {request.url.path}")

    async def validate_file_upload(self, bank_statement: UploadFile, ledger: UploadFile) -> None:
        """Validate uploaded files for security"""

        # Log file details for debugging
        logger.info(f"Validating bank statement: {bank_statement.filename}, content_type: {bank_statement.content_type}")
        logger.info(f"Validating ledger: {ledger.filename}, content_type: {ledger.content_type}")

        # Validate bank statement
        if not self.file_validator.validate_filename(bank_statement.filename):
            raise HTTPException(status_code=400, detail="Invalid bank statement filename")

        if not self.file_validator.validate_file_type(bank_statement, SecurityConfig.ALLOWED_PDF_TYPES):
            logger.error(f"Bank statement type validation failed: {bank_statement.content_type} not in {SecurityConfig.ALLOWED_PDF_TYPES}")
            raise HTTPException(status_code=400, detail="Bank statement must be a PDF file")

        if not self.file_validator.validate_file_size(bank_statement):
            raise HTTPException(status_code=400, detail="Bank statement file too large")

        # Validate ledger
        if not self.file_validator.validate_filename(ledger.filename):
            raise HTTPException(status_code=400, detail="Invalid ledger filename")

        if not self.file_validator.validate_file_type(ledger, SecurityConfig.ALLOWED_SPREADSHEET_TYPES):
            logger.error(f"Ledger type validation failed: {ledger.content_type} not in {SecurityConfig.ALLOWED_SPREADSHEET_TYPES}")
            raise HTTPException(status_code=400, detail="Ledger must be Excel or CSV file")

        if not self.file_validator.validate_file_size(ledger):
            raise HTTPException(status_code=400, detail="Ledger file too large")

        # Scan file contents
        if not await self.file_validator.scan_file_content(bank_statement):
            raise HTTPException(status_code=400, detail="Bank statement contains suspicious content")

        if not await self.file_validator.scan_file_content(ledger):
            raise HTTPException(status_code=400, detail="Ledger contains suspicious content")

        logger.info(f"File validation passed: {bank_statement.filename}, {ledger.filename}")

    def add_security_headers(self, response: Response) -> Response:
        """Add security headers to response"""
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
        response.headers["Content-Security-Policy"] = (
            "default-src 'self'; "
            "script-src 'self'; "
            "style-src 'self' 'unsafe-inline'; "
            "img-src 'self' data:; "
            "connect-src 'self'; "
            "font-src 'self'; "
            "object-src 'none'; "
            "media-src 'self'; "
            "frame-src 'none'"
        )
        return response

# Global security middleware instance
security_middleware = SecurityMiddleware()

# Audit logging
class SecurityAuditLogger:
    @staticmethod
    def log_file_upload(filename: str, file_size: int, client_ip: str, success: bool = True):
        """Log file upload attempt"""
        status = "SUCCESS" if success else "FAILED"
        logger.info(f"FILE_UPLOAD {status}: {filename} ({file_size} bytes) from {client_ip}")

    @staticmethod
    def log_security_violation(violation_type: str, details: str, client_ip: str):
        """Log security violation"""
        logger.warning(f"SECURITY_VIOLATION: {violation_type} - {details} from {client_ip}")

    @staticmethod
    def log_rate_limit_exceeded(client_ip: str):
        """Log rate limit violation"""
        logger.warning(f"RATE_LIMIT_EXCEEDED: {client_ip}")

audit_logger = SecurityAuditLogger()
