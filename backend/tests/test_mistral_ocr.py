"""
Unit tests for the enhanced Mistral OCR service
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from fastapi import UploadFile

from services.mistral_ocr import MistralOCRService
from models.schemas import TransactionType, TransactionSource

@pytest.fixture
def mistral_service():
    """Create a test instance of MistralOCRService"""
    return MistralOCRService("test_api_key")

def test_parse_date_ddmmyyyy(mistral_service):
    """Test enhanced date parsing for RFSA formats"""
    # Test DDMMYYYY format
    assert mistral_service._parse_date_ddmmyyyy("********") == "2025-07-29"
    
    # Test DD/MM/YYYY format
    assert mistral_service._parse_date_ddmmyyyy("29/07/2025") == "2025-07-29"
    
    # Test DD MM YYYY format
    assert mistral_service._parse_date_ddmmyyyy("29 07 2025") == "2025-07-29"
    
    # Test handling of 2-digit years
    assert mistral_service._parse_date_ddmmyyyy("29/07/25") == "2025-07-29"
    
    # Test invalid date
    assert mistral_service._parse_date_ddmmyyyy("invalid") is None

def test_parse_amount(mistral_service):
    """Test enhanced amount parsing with edge cases"""
    # Test standard formats
    assert mistral_service._parse_amount("1234.56") == 1234.56
    assert mistral_service._parse_amount("-1234.56") == 1234.56  # Should return absolute value
    assert mistral_service._parse_amount("1,234.56") == 1234.56
    
    # Test edge cases
    assert mistral_service._parse_amount("") == 0.0
    assert mistral_service._parse_amount(".00") == 0.0
    assert mistral_service._parse_amount("-") == 0.0
    assert mistral_service._parse_amount("$1,234.56") == 1234.56
    assert mistral_service._parse_amount("1234") == 1234.0
    
    # Test invalid formats
    assert mistral_service._parse_amount("invalid") == 0.0

def test_is_transaction_row(mistral_service):
    """Test transaction row identification"""
    # Valid transaction rows
    assert mistral_service._is_transaction_row("29/07/2025 Payment CHQ NO 12345 1234.56")
    assert mistral_service._is_transaction_row("******** DEPOSIT FIN/123/456 5000.00")
    
    # Invalid transaction rows (headers, etc.)
    assert not mistral_service._is_transaction_row("Date Particulars Reference Debit Credit Balance")
    assert not mistral_service._is_transaction_row("Balance b/f 10000.00")
    assert not mistral_service._is_transaction_row("Total 12345.67")

def test_parse_transaction_from_table_cells(mistral_service):
    """Test parsing transactions from table cells"""
    # Valid table row
    row = "| 29/07/2025 | SALARY | REF123 | Monthly Salary | 29/07/2025 | | 5000.00 | 15000.00 |"
    transaction = mistral_service._parse_transaction_from_table_cells(row, 0)
    
    assert transaction is not None
    assert transaction.date == "2025-07-29"
    assert transaction.description == "Monthly Salary"
    assert transaction.reference == "REF123"
    assert transaction.amount == 5000.00
    assert transaction.type == TransactionType.CREDIT
    
    # Debit transaction
    row = "| 30/07/2025 | RENT | CHQ001 | Office Rent | 30/07/2025 | 1200.00 | | 13800.00 |"
    transaction = mistral_service._parse_transaction_from_table_cells(row, 1)
    
    assert transaction is not None
    assert transaction.date == "2025-07-30"
    assert transaction.description == "Office Rent"
    assert transaction.reference == "CHQ001"
    assert transaction.amount == 1200.00
    assert transaction.type == TransactionType.DEBIT
    
    # Invalid row (too few cells)
    row = "| 29/07/2025 | SALARY |"
    transaction = mistral_service._parse_transaction_from_table_cells(row, 2)
    assert transaction is None

@pytest.mark.asyncio
async def test_parse_transactions_from_markdown(mistral_service):
    """Test the enhanced table-based parsing"""
    sample_markdown = """
    | Date | Particulars | Reference | Narrative | Value Date | Debit | Credit | Balance |
    |------|-------------|-----------|-----------|------------|-------|--------|---------|
    | ******** | DEPOSIT | DEP001 | Salary Payment | ******** | | 5000.00 | 15000.00 |
    | ******** | WITHDRAWAL | CHQ001 | Office Rent | ******** | 1200.00 | | 13800.00 |
    """
    
    transactions = mistral_service._parse_transactions_from_markdown(sample_markdown)
    
    assert len(transactions) == 2
    assert transactions[0].amount == 5000.00
    assert transactions[0].type == TransactionType.CREDIT
    assert transactions[1].amount == 1200.00
    assert transactions[1].type == TransactionType.DEBIT

@pytest.mark.asyncio
async def test_extract_bank_transactions():
    """Test the full extraction pipeline with mocked OCR response"""
    # Create a mock OCR response
    mock_ocr_result = {
        "pages": [
            {
                "markdown": """
                | Date | Particulars | Reference | Narrative | Value Date | Debit | Credit | Balance |
                |------|-------------|-----------|-----------|------------|-------|--------|---------|
                | ******** | DEPOSIT | DEP001 | Salary Payment | ******** | | 5000.00 | 15000.00 |
                | ******** | WITHDRAWAL | CHQ001 | Office Rent | ******** | 1200.00 | | 13800.00 |
                """
            }
        ],
        "usage_info": {
            "pages_processed": 1
        }
    }
    
    # Create a mock service with patched parse_document method
    service = MistralOCRService("test_api_key")
    service.parse_document = AsyncMock(return_value=mock_ocr_result)
    
    # Create a mock file
    mock_file = MagicMock(spec=UploadFile)
    mock_file.filename = "test.pdf"
    
    # Call the extract_bank_transactions method
    transactions = await service.extract_bank_transactions(mock_file)
    
    # Verify results
    assert len(transactions) == 2
    assert transactions[0].amount == 5000.00
    assert transactions[0].type == TransactionType.CREDIT
    assert transactions[1].amount == 1200.00
    assert transactions[1].type == TransactionType.DEBIT
