"""
Custom exceptions for the Bank Reconciliation AI backend
"""

class BankReconciliationError(Exception):
    """Base exception for all bank reconciliation errors"""
    pass

class ValidationError(BankReconciliationError):
    """Raised when input validation fails"""
    pass

class ProcessingError(BankReconciliationError):
    """Raised when file processing fails"""
    pass

class PDFParsingError(ProcessingError):
    """Raised when PDF parsing fails"""
    pass

class ExcelParsingError(ProcessingError):
    """Raised when Excel/CSV parsing fails"""
    pass

class MatchingError(BankReconciliationError):
    """Raised when transaction matching fails"""
    pass

class AIAnalysisError(BankReconciliationError):
    """Raised when AI analysis fails"""
    pass

class DatabaseError(BankReconciliationError):
    """Raised when database operations fail"""
    pass
