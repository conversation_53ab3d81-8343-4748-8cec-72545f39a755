"""
Configuration settings for the FastAPI backend
"""

from pydantic_settings import BaseSettings
from typing import List, Optional
import os
from pathlib import Path

class Settings(BaseSettings):
    """Application settings with environment variable support"""

    # API Configuration
    API_V1_STR: str = "/api/v1"
    PROJECT_NAME: str = "Bank Reconciliation AI"
    VERSION: str = "1.0.0"
    DESCRIPTION: str = "AI-powered bank reconciliation service"

    # Server Configuration
    HOST: str = "0.0.0.0"
    PORT: int = 8000
    DEBUG: bool = False

    # CORS Configuration
    ALLOWED_ORIGINS: List[str] = [
        "http://localhost:3000",
        "http://127.0.0.1:3000",
        "https://localhost:3000"
    ]
    ALLOWED_VERCEL_DOMAIN: Optional[str] = None

    # File Processing Configuration
    MAX_FILE_SIZE: int = 50 * 1024 * 1024  # 50MB
    MAX_FILE_SIZE_MB: int = 50  # For environment variable compatibility
    ALLOWED_PDF_EXTENSIONS: List[str] = [".pdf"]
    ALLOWED_EXCEL_EXTENSIONS: List[str] = [".xlsx", ".xls", ".csv"]
    UPLOAD_DIR: str = "uploads"
    TEMP_DIR: str = "temp"

    # AI Configuration
    GOOGLE_API_KEY: Optional[str] = None
    MISTRAL_API_KEY: Optional[str] = None
    AI_MODEL: str = "gemini-2.5-flash"
    AI_TEMPERATURE: float = 0.1
    AI_MAX_TOKENS: int = 1000

    # Database Configuration (for future Supabase integration)
    DATABASE_URL: Optional[str] = None
    SUPABASE_URL: Optional[str] = None
    SUPABASE_KEY: Optional[str] = None

    # Logging Configuration
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    LOG_FILE: str = "logs/app.log"  # For environment variable compatibility

    # Processing Configuration
    MATCHING_CONFIDENCE_THRESHOLD: float = 0.85
    DATE_TOLERANCE_DAYS: int = 3
    FUZZY_MATCH_THRESHOLD: float = 0.8

    # Security Configuration
    SECRET_KEY: str = "your-secret-key-change-in-production"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30

    class Config:
        env_file = ".env"
        case_sensitive = True
        extra = "ignore"  # Ignore extra fields from environment variables

# Create settings instance
settings = Settings()

# Ensure directories exist
def create_directories():
    """Create necessary directories if they don't exist"""
    Path(settings.UPLOAD_DIR).mkdir(exist_ok=True)
    Path(settings.TEMP_DIR).mkdir(exist_ok=True)

create_directories()
