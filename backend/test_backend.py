#!/usr/bin/env python3
"""
Simple test script to verify FastAPI backend functionality
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models.schemas import ParsedTransaction, TransactionType, TransactionSource
from services.transaction_matcher import TransactionMatcherService
import asyncio

async def test_transaction_matcher():
    """Test the transaction matching service"""
    print("Testing Transaction Matcher Service...")
    
    # Create sample transactions
    bank_transactions = [
        ParsedTransaction(
            id="bank_1",
            date="2025-01-15T00:00:00Z",
            description="ACH CREDIT PAYROLL",
            reference="REF123456",
            amount=5000.00,
            type=TransactionType.CREDIT,
            source=TransactionSource.BANK
        ),
        ParsedTransaction(
            id="bank_2",
            date="2025-01-16T00:00:00Z",
            description="CHECK #1001",
            reference="1001",
            amount=-1200.00,
            type=TransactionType.DEBIT,
            source=TransactionSource.BANK
        )
    ]
    
    ledger_transactions = [
        ParsedTransaction(
            id="ledger_1",
            date="2025-01-15T00:00:00Z",
            description="Payroll Deposit",
            reference="REF123456",
            amount=5000.00,
            type=TransactionType.CREDIT,
            source=TransactionSource.LEDGER
        ),
        ParsedTransaction(
            id="ledger_2",
            date="2025-01-16T00:00:00Z",
            description="Office Supplies Check",
            reference="1001",
            amount=-1200.00,
            type=TransactionType.DEBIT,
            source=TransactionSource.LEDGER
        )
    ]
    
    # Test matching
    matcher = TransactionMatcherService()
    result = await matcher.match_transactions(bank_transactions, ledger_transactions)
    
    print(f"✓ Found {len(result.matches)} matches")
    print(f"✓ Found {len(result.discrepancies)} discrepancies")
    print(f"✓ Average confidence: {result.matchingStats.averageConfidence:.2f}")
    
    for match in result.matches:
        print(f"  - Match: {match.bankTransaction.description} <-> {match.ledgerTransaction.description} (confidence: {match.confidence:.2f})")
    
    return True

def test_schemas():
    """Test Pydantic schemas"""
    print("Testing Pydantic Schemas...")
    
    # Test transaction creation
    transaction = ParsedTransaction(
        id="test_1",
        date="2025-01-15T00:00:00Z",
        description="Test Transaction",
        reference="TEST123",
        amount=100.00,
        type=TransactionType.CREDIT,
        source=TransactionSource.BANK
    )
    
    print(f"✓ Created transaction: {transaction.description}")
    print(f"✓ Amount: ${transaction.amount}")
    print(f"✓ Type: {transaction.type.value}")
    
    return True

async def main():
    """Run all tests"""
    print("=" * 50)
    print("Bank Reconciliation AI - Backend Tests")
    print("=" * 50)
    
    try:
        # Test schemas
        test_schemas()
        print()
        
        # Test transaction matcher
        await test_transaction_matcher()
        print()
        
        print("✅ All tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
