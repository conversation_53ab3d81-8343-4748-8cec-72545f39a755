#!/usr/bin/env python3
"""
Startup script for Bank Reconciliation AI FastAPI Backend
"""

import uvicorn
import os
from pathlib import Path

def main():
    """Start the FastAPI server"""
    # Ensure required directories exist
    Path("uploads").mkdir(exist_ok=True)
    Path("temp").mkdir(exist_ok=True)
    Path("logs").mkdir(exist_ok=True)
    
    # Start the server
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )

if __name__ == "__main__":
    main()
