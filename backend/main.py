"""
Bank Reconciliation AI - FastAPI Backend
Main application entry point with API routes for document processing
"""

from fastapi import FastAPI, File, UploadFile, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.middleware.trustedhost import TrustedHostMiddleware
import asyncio
import logging
from pathlib import Path
import os
from dotenv import load_dotenv
import time
from collections import defaultdict
from datetime import datetime
import uvicorn

from services.pdf_parser import PDFParserService
from services.excel_parser import ExcelParserService
from services.transaction_matcher import TransactionMatcherService
from services.ai_analyzer import AIAnalyzerService
from middleware.security import security_middleware, audit_logger
from models.schemas import (
    ReconciliationRequest,
    ReconciliationResponse,
    ParsedTransaction,
    MatchingResult,
    ProcessingStatus
)
from core.config import settings
from core.exceptions import ProcessingError, ValidationError
from core.logging_config import setup_logging

# Load environment variables explicitly
# First load from backend/.env
backend_env_path = Path(__file__).parent / ".env"
if backend_env_path.exists():
    load_dotenv(backend_env_path)
    print(f"Loaded environment from {backend_env_path}")

# Also try to load from the project root's .env.local
project_root = Path(__file__).parent.parent
env_local_path = project_root / ".env.local"
if env_local_path.exists():
    load_dotenv(env_local_path)
    print(f"Loaded environment from {env_local_path}")

# Setup logging
setup_logging()
logger = logging.getLogger(__name__)

# Log successful environment loading
if backend_env_path.exists():
    logger.info(f"Loaded environment from {backend_env_path}")
if env_local_path.exists():
    logger.info(f"Loaded environment from {env_local_path}")

# Initialize FastAPI app
app = FastAPI(
    title="Bank Reconciliation AI API",
    description="AI-powered bank reconciliation service for automated transaction matching and discrepancy detection",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# CORS middleware for Next.js frontend
allowed_origins = settings.ALLOWED_ORIGINS.copy()
if settings.ALLOWED_VERCEL_DOMAIN:
    allowed_origins.append(settings.ALLOWED_VERCEL_DOMAIN)

app.add_middleware(
    CORSMiddleware,
    allow_origins=allowed_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize services
pdf_parser = PDFParserService()
excel_parser = ExcelParserService()
transaction_matcher = TransactionMatcherService()
ai_analyzer = AIAnalyzerService()

@app.get("/")
async def root():
    """Health check endpoint"""
    return {
        "message": "Bank Reconciliation AI API",
        "version": "1.0.0",
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat()
    }

@app.get("/health")
async def health_check():
    """Detailed health check with service status"""
    return {
        "status": "healthy",
        "services": {
            "pdf_parser": "operational",
            "excel_parser": "operational",
            "transaction_matcher": "operational",
            "ai_analyzer": "operational"
        },
        "timestamp": datetime.utcnow().isoformat()
    }

@app.post("/api/process-files", response_model=ReconciliationResponse)
async def process_reconciliation_files(
    request: Request,
    bank_statement: UploadFile = File(..., description="Bank statement PDF file"),
    ledger: UploadFile = File(..., description="Accounting ledger Excel/CSV file")
):
    """
    Process bank statement and ledger files for reconciliation

    Args:
        bank_statement: PDF file containing bank statement
        ledger: Excel or CSV file containing accounting ledger

    Returns:
        ReconciliationResponse with matches, discrepancies, and analysis
    """
    client_ip = security_middleware.get_client_ip(request)

    try:
        # Security validation
        await security_middleware.validate_request(request)
        await security_middleware.validate_file_upload(bank_statement, ledger)

        # Log file upload attempt
        audit_logger.log_file_upload(
            f"{bank_statement.filename}, {ledger.filename}",
            (bank_statement.size or 0) + (ledger.size or 0),
            client_ip
        )

        logger.info(f"Processing reconciliation request - Bank: {bank_statement.filename}, Ledger: {ledger.filename}")

        # Validate file types
        if not bank_statement.filename.lower().endswith('.pdf'):
            raise ValidationError("Bank statement must be a PDF file")

        if not ledger.filename.lower().endswith(('.xlsx', '.xls', '.csv')):
            raise ValidationError("Ledger must be an Excel or CSV file")

        # Process files in parallel
        bank_data_task = asyncio.create_task(
            pdf_parser.parse_bank_statement(bank_statement)
        )
        ledger_data_task = asyncio.create_task(
            excel_parser.parse_ledger(ledger)
        )

        # Wait for both parsing operations to complete
        bank_data, ledger_data = await asyncio.gather(
            bank_data_task, ledger_data_task
        )

        logger.info(f"Parsed {len(bank_data.transactions)} bank transactions and {len(ledger_data.transactions)} ledger transactions")

        # Perform transaction matching
        matching_result = await transaction_matcher.match_transactions(
            bank_data.transactions,
            ledger_data.transactions
        )

        logger.info(f"Matching completed - {len(matching_result.matches)} matches, {len(matching_result.discrepancies)} discrepancies")

        # Generate AI analysis for discrepancies
        analyzed_discrepancies = await ai_analyzer.analyze_discrepancies(
            matching_result.discrepancies
        )

        # Calculate summary statistics
        summary = {
            "totalBankTransactions": len(bank_data.transactions),
            "totalLedgerTransactions": len(ledger_data.transactions),
            "matchedTransactions": len(matching_result.matches),
            "discrepancies": len(analyzed_discrepancies),
            "bankBalance": {
                "opening": bank_data.opening_balance,
                "closing": bank_data.closing_balance,
                "totalCredits": sum(t.amount for t in bank_data.transactions if t.amount > 0),
                "totalDebits": sum(t.amount for t in bank_data.transactions if t.amount < 0),
            },
            "ledgerBalance": {
                "opening": ledger_data.opening_balance or bank_data.opening_balance,
                "closing": ledger_data.closing_balance or (
                    ledger_data.opening_balance + sum(t.amount for t in ledger_data.transactions)
                ),
                "totalCredits": sum(t.amount for t in ledger_data.transactions if t.amount > 0),
                "totalDebits": sum(t.amount for t in ledger_data.transactions if t.amount < 0),
            },
            "matchingStats": matching_result.matchingStats
        }

        # Calculate balance difference
        summary["balanceDifference"] = (
            summary["bankBalance"]["closing"] - summary["ledgerBalance"]["closing"]
        )

        response = ReconciliationResponse(
            id=f"reconciliation_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}",
            uploadedAt=datetime.utcnow().isoformat(),
            bankStatementInfo={
                "filename": bank_statement.filename,
                "totalTransactions": len(bank_data.transactions),
                "accountNumber": bank_data.account_number,
                "statementPeriod": f"{bank_data.statement_period['from']} to {bank_data.statement_period['to']}"
            },
            ledgerInfo={
                "filename": ledger.filename,
                "totalTransactions": len(ledger_data.transactions)
            },
            summary=summary,
            matches=matching_result.matches,
            discrepancies=analyzed_discrepancies,
            status="completed"
        )

        logger.info(f"Reconciliation completed successfully - ID: {response.id}")
        return response

    except ValidationError as e:
        logger.error(f"Validation error: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))

    except ProcessingError as e:
        logger.error(f"Processing error: {str(e)}")
        raise HTTPException(status_code=422, detail=str(e))

    except Exception as e:
        logger.error(f"Unexpected error during reconciliation: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail="An unexpected error occurred during processing. Please try again."
        )

@app.post("/api/parse-pdf")
async def parse_pdf_only(
    file: UploadFile = File(..., description="PDF file to parse")
):
    """Parse PDF file and return extracted data"""
    try:
        if not file.filename.lower().endswith('.pdf'):
            raise ValidationError("File must be a PDF")

        parsed_data = await pdf_parser.parse_bank_statement(file)
        return {
            "filename": file.filename,
            "transactions": parsed_data.transactions,
            "metadata": {
                "account_number": parsed_data.account_number,
                "statement_period": parsed_data.statement_period,
                "opening_balance": parsed_data.opening_balance,
                "closing_balance": parsed_data.closing_balance
            }
        }
    except Exception as e:
        logger.error(f"PDF parsing error: {str(e)}")
        raise HTTPException(status_code=422, detail=f"PDF parsing failed: {str(e)}")

@app.post("/api/parse-excel")
async def parse_excel_only(
    file: UploadFile = File(..., description="Excel/CSV file to parse")
):
    """Parse Excel/CSV file and return extracted data"""
    try:
        if not file.filename.lower().endswith(('.xlsx', '.xls', '.csv')):
            raise ValidationError("File must be Excel or CSV format")

        parsed_data = await excel_parser.parse_ledger(file)
        return {
            "filename": file.filename,
            "transactions": parsed_data.transactions,
            "metadata": {
                "opening_balance": parsed_data.opening_balance,
                "closing_balance": parsed_data.closing_balance
            }
        }
    except Exception as e:
        logger.error(f"Excel parsing error: {str(e)}")
        raise HTTPException(status_code=422, detail=f"Excel parsing failed: {str(e)}")

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level="info"
    )
