// Script to clear localStorage - paste this in browser console
console.log('🧹 Clearing localStorage...');

// Clear all reconciliation data
localStorage.removeItem('bank-reconciliation-results');

// Clear any other related data
Object.keys(localStorage).forEach(key => {
  if (key.includes('reconciliation') || key.includes('bank')) {
    console.log('Removing:', key);
    localStorage.removeItem(key);
  }
});

console.log('✅ localStorage cleared. Refresh the page to start fresh.');
