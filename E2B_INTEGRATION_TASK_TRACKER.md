# E2B Integration Task Tracker for Bank Reconciliation AI

## Overview

This document outlines the tasks required to implement E2B (Execution Environment for AI) integration with our Bank Reconciliation AI application. The integration will provide a third processing option alongside our existing Next.js API and FastAPI backends, enabling secure sandboxed execution for advanced data analysis and visualization.

## Architecture

The E2B integration will be implemented as a separate Next.js API route that will:
1. Create secure sandboxes for processing financial data
2. Execute custom code for transaction matching and analysis
3. Generate visualizations and reports
4. Return results to the frontend

## Integration Strategy

We'll implement the E2B integration in parallel with our existing backends to allow for comparative performance testing without disrupting current functionality. The implementation will follow a phased approach with clear milestones.

## Task List

### Phase 1: Setup & Infrastructure (Weeks 1-2)

| ID | Task | Description | Priority | Dependencies | Status |
|----|------|-------------|----------|--------------|--------|
| 1.1 | Create E2B API credentials | Register for E2B API and obtain credentials | High | None | To Do |
| 1.2 | Set up environment variables | Configure E2B API keys and endpoints in .env files | High | 1.1 | To Do |
| 1.3 | Create E2B service module | Implement core E2B service for sandbox management | High | 1.2 | To Do |
| 1.4 | Implement sandbox lifecycle | Create functions for sandbox creation, execution, and cleanup | High | 1.3 | To Do |
| 1.5 | Add secure data transfer | Implement encryption and secure data transfer to/from sandboxes | High | 1.3 | To Do |
| 1.6 | Create audit logging | Set up comprehensive logging for all sandbox operations | Medium | 1.3 | To Do |
| 1.7 | Write unit tests | Create tests for E2B service functionality | Medium | 1.3-1.6 | To Do |

### Phase 2: Core Processing Implementation (Weeks 3-4)

| ID | Task | Description | Priority | Dependencies | Status |
|----|------|-------------|----------|--------------|--------|
| 2.1 | Create E2B API route | Implement `/api/process-files-e2b` endpoint | High | 1.4 | To Do |
| 2.2 | Implement file parsing | Add code to parse bank statements and ledgers in sandbox | High | 2.1 | To Do |
| 2.3 | Add transaction matching | Implement enhanced matching algorithm in sandbox | High | 2.2 | To Do |
| 2.4 | Create discrepancy analysis | Add AI-powered discrepancy analysis in sandbox | Medium | 2.3 | To Do |
| 2.5 | Implement progress streaming | Add SSE streaming for real-time progress updates | Medium | 2.1 | To Do |
| 2.6 | Add error handling | Implement comprehensive error handling and recovery | High | 2.1-2.5 | To Do |
| 2.7 | Create integration tests | Test full processing pipeline with real files | Medium | 2.1-2.6 | To Do |

### Phase 3: Advanced Features (Weeks 5-6)

| ID | Task | Description | Priority | Dependencies | Status |
|----|------|-------------|----------|--------------|--------|
| 3.1 | Add custom visualization | Implement matplotlib/plotly chart generation | Medium | 2.3 | To Do |
| 3.2 | Create custom report generation | Add PDF/Excel report generation in sandbox | Medium | 2.3 | To Do |
| 3.3 | Implement pattern recognition | Add automated pattern detection for exceptions | Low | 2.4 | To Do |
| 3.4 | Add format detection | Implement automatic bank statement format detection | Medium | 2.2 | To Do |
| 3.5 | Create data validation | Add comprehensive data quality validation | Medium | 2.3 | To Do |
| 3.6 | Implement performance optimizations | Add caching and resource optimization | Low | 3.1-3.5 | To Do |

### Phase 4: Frontend Integration (Week 7)

| ID | Task | Description | Priority | Dependencies | Status |
|----|------|-------------|----------|--------------|--------|
| 4.1 | Update file processing hook | Add E2B as processing option in `use-file-processing.ts` | High | 2.7 | To Do |
| 4.2 | Add backend selector UI | Create UI for selecting processing backend | Medium | 4.1 | To Do |
| 4.3 | Implement visualization display | Add components to display E2B-generated visualizations | Medium | 3.1, 4.1 | To Do |
| 4.4 | Update results dashboard | Enhance dashboard to show E2B-specific insights | Medium | 4.3 | To Do |
| 4.5 | Add performance metrics | Implement UI for comparing backend performance | Low | 4.1 | To Do |

### Phase 5: Testing & Deployment (Week 8)

| ID | Task | Description | Priority | Dependencies | Status |
|----|------|-------------|----------|--------------|--------|
| 5.1 | Perform integration testing | Test all components working together | High | 4.4 | To Do |
| 5.2 | Conduct performance comparison | Compare E2B vs FastAPI vs Next.js API performance | Medium | 5.1 | To Do |
| 5.3 | Create documentation | Document E2B integration and usage | Medium | 5.1 | To Do |
| 5.4 | Deploy to staging | Deploy to staging environment for testing | High | 5.1 | To Do |
| 5.5 | Perform security audit | Conduct security review of E2B implementation | High | 5.4 | To Do |
| 5.6 | Deploy to production | Deploy to production environment | High | 5.5 | To Do |

## Implementation Details

### E2B API Route Structure

```typescript
// /api/process-files-e2b/route.ts
import { NextRequest } from 'next/server';
import { E2BService } from '@/lib/e2b-service';
import { ReconciliationResult } from '@/lib/schemas';

export async function POST(request: NextRequest) {
  const encoder = new TextEncoder();
  const stream = new ReadableStream({
    async start(controller) {
      try {
        // Initialize E2B service
        const e2bService = new E2BService();
        
        // Send progress updates
        const sendProgress = (step: string, data?: Record<string, unknown>) => {
          const message = JSON.stringify({ 
            type: 'progress', 
            step, 
            data, 
            timestamp: new Date().toISOString() 
          });
          controller.enqueue(encoder.encode(`data: ${message}\n\n`));
        };
        
        // Parse form data and extract files
        const formData = await request.formData();
        const bankStatement = formData.get('bank_statement') as File;
        const ledger = formData.get('ledger') as File;
        
        // Validate files
        if (!bankStatement || !ledger) {
          throw new Error('Both bank statement and ledger files are required');
        }
        
        // Create sandbox
        sendProgress('Creating secure sandbox environment');
        const sandbox = await e2bService.createSandbox();
        
        try {
          // Upload files to sandbox
          sendProgress('Uploading files to secure environment');
          await e2bService.uploadFiles(sandbox, bankStatement, ledger);
          
          // Process files in sandbox
          sendProgress('Processing files in secure environment');
          const result = await e2bService.processFiles(sandbox, {
            onProgress: (step, data) => sendProgress(step, data)
          });
          
          // Send final result
          const message = JSON.stringify({ 
            type: 'result', 
            data: result, 
            timestamp: new Date().toISOString() 
          });
          controller.enqueue(encoder.encode(`data: ${message}\n\n`));
        } finally {
          // Always clean up sandbox
          await e2bService.destroySandbox(sandbox);
        }
        
        controller.close();
      } catch (error) {
        // Handle errors
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        const message = JSON.stringify({
          type: 'error',
          error: errorMessage,
          timestamp: new Date().toISOString()
        });
        controller.enqueue(encoder.encode(`data: ${message}\n\n`));
        controller.close();
      }
    }
  });
  
  return new Response(stream, {
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive'
    }
  });
}
```

### E2B Service Implementation

```typescript
// /lib/e2b-service.ts
import { Sandbox } from '@e2b/sdk';
import { ReconciliationResult } from './schemas';

export class E2BService {
  private apiKey: string;
  
  constructor() {
    const apiKey = process.env.E2B_API_KEY;
    if (!apiKey) {
      throw new Error('E2B_API_KEY is not defined in environment variables');
    }
    this.apiKey = apiKey;
  }
  
  async createSandbox(): Promise<Sandbox> {
    const sandbox = await Sandbox.create({
      apiKey: this.apiKey,
      template: 'base',
      metadata: {
        purpose: 'bank-reconciliation'
      }
    });
    return sandbox;
  }
  
  async destroySandbox(sandbox: Sandbox): Promise<void> {
    await sandbox.close();
  }
  
  async uploadFiles(sandbox: Sandbox, bankStatement: File, ledger: File): Promise<void> {
    // Convert File objects to ArrayBuffer
    const bankBuffer = await bankStatement.arrayBuffer();
    const ledgerBuffer = await ledger.arrayBuffer();
    
    // Upload files to sandbox
    await sandbox.filesystem.write('/tmp/bank_statement.pdf', new Uint8Array(bankBuffer));
    await sandbox.filesystem.write('/tmp/ledger.xlsx', new Uint8Array(ledgerBuffer));
  }
  
  async processFiles(
    sandbox: Sandbox, 
    options: { onProgress?: (step: string, data?: Record<string, unknown>) => void }
  ): Promise<ReconciliationResult> {
    const { onProgress } = options;
    
    // Install required packages
    onProgress?.('Installing dependencies');
    await sandbox.process.start({
      cmd: 'pip install pandas numpy matplotlib plotly openpyxl pdfplumber',
    });
    
    // Execute processing script
    onProgress?.('Processing files');
    const result = await sandbox.process.startAndWaitForOutput({
      cmd: 'python',
      args: ['-c', this.getProcessingScript()],
      timeout: 120000, // 2 minutes
      onStdout: (data) => {
        try {
          const parsedData = JSON.parse(data);
          if (parsedData.type === 'progress') {
            onProgress?.(parsedData.step, parsedData.data);
          }
        } catch (e) {
          // Not JSON or not progress data
        }
      }
    });
    
    // Parse and return result
    return JSON.parse(result.stdout);
  }
  
  private getProcessingScript(): string {
    // This is the Python script that will run in the sandbox
    return `
import pandas as pd
import numpy as np
import json
import pdfplumber
import sys
import os
import re
from datetime import datetime

def log_progress(step, data=None):
    """Log progress to stdout in JSON format"""
    progress = {
        "type": "progress",
        "step": step,
        "data": data or {},
        "timestamp": datetime.now().isoformat()
    }
    print(json.dumps(progress))
    sys.stdout.flush()

def parse_bank_statement(file_path):
    """Parse bank statement PDF"""
    log_progress("Parsing bank statement")
    transactions = []
    
    with pdfplumber.open(file_path) as pdf:
        for page in pdf.pages:
            text = page.extract_text()
            # Parse transactions from text
            # [Implementation details]
    
    log_progress("Bank statement parsing completed", {"transactions": len(transactions)})
    return transactions

def parse_ledger(file_path):
    """Parse ledger Excel/CSV file"""
    log_progress("Parsing ledger")
    if file_path.endswith('.xlsx') or file_path.endswith('.xls'):
        df = pd.read_excel(file_path)
    else:
        df = pd.read_csv(file_path)
    
    # Process dataframe
    # [Implementation details]
    
    transactions = []
    # Convert dataframe to transactions
    # [Implementation details]
    
    log_progress("Ledger parsing completed", {"transactions": len(transactions)})
    return transactions

def match_transactions(bank_transactions, ledger_transactions):
    """Match bank and ledger transactions"""
    log_progress("Matching transactions")
    
    matches = []
    # Implement matching algorithm
    # [Implementation details]
    
    log_progress("Transaction matching completed", {
        "matches": len(matches),
        "match_rate": f"{(len(matches) / len(bank_transactions) * 100):.1f}%"
    })
    return matches

def analyze_discrepancies(bank_transactions, ledger_transactions, matches):
    """Analyze discrepancies between bank and ledger"""
    log_progress("Analyzing discrepancies")
    
    # Find unmatched transactions
    matched_bank_ids = set(m["bank_id"] for m in matches)
    matched_ledger_ids = set(m["ledger_id"] for m in matches)
    
    unmatched_bank = [t for t in bank_transactions if t["id"] not in matched_bank_ids]
    unmatched_ledger = [t for t in ledger_transactions if t["id"] not in matched_ledger_ids]
    
    discrepancies = []
    # Process discrepancies
    # [Implementation details]
    
    log_progress("Discrepancy analysis completed", {"discrepancies": len(discrepancies)})
    return discrepancies

def generate_visualizations():
    """Generate visualizations for the report"""
    log_progress("Generating visualizations")
    
    # Create visualizations
    # [Implementation details]
    
    log_progress("Visualizations completed")
    return {}

def main():
    """Main processing function"""
    try:
        # Parse files
        bank_transactions = parse_bank_statement('/tmp/bank_statement.pdf')
        ledger_transactions = parse_ledger('/tmp/ledger.xlsx')
        
        # Match transactions
        matches = match_transactions(bank_transactions, ledger_transactions)
        
        # Analyze discrepancies
        discrepancies = analyze_discrepancies(bank_transactions, ledger_transactions, matches)
        
        # Generate visualizations
        visualizations = generate_visualizations()
        
        # Create result
        result = {
            "id": f"reconciliation_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            "uploadedAt": datetime.now().isoformat(),
            "bankStatementInfo": {
                "filename": "bank_statement.pdf",
                "totalTransactions": len(bank_transactions)
            },
            "ledgerInfo": {
                "filename": "ledger.xlsx",
                "totalTransactions": len(ledger_transactions)
            },
            "summary": {
                "totalBankTransactions": len(bank_transactions),
                "totalLedgerTransactions": len(ledger_transactions),
                "matchedTransactions": len(matches),
                "discrepancies": len(discrepancies)
                # Additional summary data
            },
            "matches": matches,
            "discrepancies": discrepancies,
            "visualizations": visualizations,
            "status": "completed"
        }
        
        # Output final result
        print(json.dumps(result))
        
    except Exception as e:
        error_result = {
            "error": str(e),
            "status": "failed"
        }
        print(json.dumps(error_result))
        sys.exit(1)

if __name__ == "__main__":
    main()
    `;
  }
}
```

### Frontend Integration

```typescript
// Update to use-file-processing.ts
export function useFileProcessing() {
  // ...existing code...

  return useMutation({
    mutationFn: async ({ bankStatement, ledger, onProgress, onAIExplanationUpdate, onInitialResult }: ProcessFilesParams): Promise<ReconciliationResult> => {
      return handleAsyncError(async () => {
        // Validate files before processing
        FileValidation.validateBoth(bankStatement, ledger)

        const formData = new FormData()
        
        // Add files to form data
        formData.append('bank_statement', bankStatement)
        formData.append('ledger', ledger)

        // Check total file size - Vercel has 4.5MB limit
        const totalSize = bankStatement.size + ledger.size
        const VERCEL_LIMIT = 7.5 * 1024 * 1024 // 7.5MB in bytes
        
        // Determine which backend to use
        // 1. E2B - if enabled in settings
        // 2. FastAPI - if files are larger than Vercel limit
        // 3. Next.js API - default for smaller files
        const useE2B = process.env.NEXT_PUBLIC_USE_E2B === 'true'
        const useBackend = !useE2B && totalSize > VERCEL_LIMIT

        // Ensure backend URL is available if needed
        if (useBackend && !process.env.NEXT_PUBLIC_BACKEND_URL) {
          throw new Error('Backend URL not configured but required for large files. Please contact support.')
        }

        const endpoint = useE2B
          ? '/api/process-files-e2b'
          : useBackend
            ? `${process.env.NEXT_PUBLIC_BACKEND_URL}/api/process-files`
            : '/api/process-files-stream'

        console.log(`[FILE_PROCESSING] Files total: ${(totalSize / 1024 / 1024).toFixed(2)}MB, size limit: ${(VERCEL_LIMIT / 1024 / 1024).toFixed(2)}MB`)
        console.log(`[FILE_PROCESSING] Using ${useE2B ? 'E2B' : useBackend ? 'FastAPI backend' : 'Next.js API'}`)
        console.log(`[FILE_PROCESSING] Endpoint: ${endpoint}`)

        // Use streaming endpoint for better stability
        const response = await fetch(endpoint, {
          method: 'POST',
          body: formData,
        })

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }

        // Handle different response types
        if (useBackend && !useE2B) {
          // FastAPI backend returns JSON directly
          const result = await response.json() as ReconciliationResult
          onProgress?.('Processing completed')
          onInitialResult?.(result)
          return result
        } else {
          // Next.js API and E2B return streaming response
          // ... existing streaming response handling ...
        }
      }, 'FILE_PROCESSING', 1)
    },
    // ... rest of the existing code ...
  })
}
```

## Risk Assessment

| Risk | Impact | Probability | Mitigation |
|------|--------|------------|------------|
| E2B API rate limits | High | Medium | Implement caching and request throttling |
| Sandbox startup delays | Medium | High | Pre-warm sandboxes and implement timeout handling |
| Integration complexity | Medium | Medium | Phased approach with clear milestones and testing |
| Performance issues | High | Low | Optimize code execution and implement monitoring |
| Security concerns | High | Low | Implement comprehensive security review and testing |

## Success Metrics

| Metric | Target | Measurement Method |
|--------|--------|-------------------|
| Transaction extraction accuracy | >95% | Compare with manual extraction |
| Transaction matching rate | >90% | Compare with existing backends |
| Processing time | <60s for typical files | Automated performance testing |
| Memory usage | <500MB per sandbox | Resource monitoring |
| Error rate | <1% | Production monitoring |

## Conclusion

The E2B integration will provide a powerful new option for processing bank reconciliation data with enhanced security, flexibility, and visualization capabilities. By implementing this integration alongside our existing backends, we can compare performance and functionality to determine the optimal approach for different use cases.

## Next Steps

1. Begin Phase 1 implementation with E2B API setup
2. Create core sandbox management functionality
3. Implement file processing in sandbox
4. Integrate with frontend
5. Test and deploy
