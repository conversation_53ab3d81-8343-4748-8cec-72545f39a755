const fs = require('fs');
const path = require('path');

// Test the fixed Mistral OCR integration
async function testMistralOCR() {
  console.log('Testing Mistral OCR with fixed Node.js Buffer implementation...');
  
  try {
    // Read the PDF file
    const pdfPath = path.join(__dirname, 'example-docs', 'RFSA bank statement July 2025_compressed.pdf');
    const pdfBuffer = fs.readFileSync(pdfPath);
    
    // Create a File-like object for Node.js
    const file = {
      arrayBuffer: () => Promise.resolve(pdfBuffer.buffer.slice(pdfBuffer.byteOffset, pdfBuffer.byteOffset + pdfBuffer.byteLength)),
      type: 'application/pdf',
      name: 'RFSA bank statement July 2025_compressed.pdf',
      size: pdfBuffer.length
    };
    
    console.log(`PDF file loaded: ${file.size} bytes`);
    
    // Test the Mistral OCR service
    const { createMistralOCRService } = await import('./src/lib/mistral-ocr.js');
    
    // Check if MISTRAL_API_KEY is available
    const apiKey = process.env.MISTRAL_API_KEY;
    if (!apiKey) {
      console.log('MISTRAL_API_KEY not found in environment variables');
      console.log('Available env vars:', Object.keys(process.env).filter(key => key.includes('MISTRAL')));
      return;
    }
    
    const mistralService = createMistralOCRService(apiKey);
    
    console.log('Testing base64 encoding...');
    // Test the encoding function directly
    const base64Content = await mistralService.encodeFileToBase64?.(file);
    if (!base64Content) {
      // Access the private method through reflection for testing
      const arrayBuffer = await file.arrayBuffer();
      const buffer = Buffer.from(arrayBuffer);
      const base64 = buffer.toString('base64');
      console.log(`Base64 encoding successful: ${base64.length} characters`);
    } else {
      console.log(`Base64 encoding successful: ${base64Content.length} characters`);
    }
    
    console.log('Mistral OCR integration test completed successfully!');
    
  } catch (error) {
    console.error('Test failed:', error.message);
    console.error('Stack trace:', error.stack);
  }
}

testMistralOCR();
