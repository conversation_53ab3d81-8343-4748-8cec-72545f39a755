const fs = require('fs');

async function validatePDFStructure(filePath) {
  try {
    console.log('=== PDF Structure Validation ===');
    console.log(`Analyzing: ${filePath}`);
    
    const buffer = fs.readFileSync(filePath);
    console.log(`File size: ${(buffer.length / 1024 / 1024).toFixed(2)}MB`);
    
    // Convert to string for text analysis
    const pdfText = buffer.toString('binary');
    
    // Check for PDF header
    const hasValidHeader = pdfText.startsWith('%PDF-');
    console.log(`✓ Valid PDF header: ${hasValidHeader}`);
    
    if (hasValidHeader) {
      const version = pdfText.substring(0, 10);
      console.log(`  PDF Version: ${version}`);
    }
    
    // Check for essential PDF structures
    const hasXrefTable = pdfText.includes('xref');
    const hasTrailer = pdfText.includes('trailer');
    const hasStartXref = pdfText.includes('startxref');
    const hasEOF = pdfText.includes('%%EOF');
    
    console.log(`✓ Cross-reference table: ${hasXrefTable}`);
    console.log(`✓ Trailer: ${hasTrailer}`);
    console.log(`✓ StartXref: ${hasStartXref}`);
    console.log(`✓ EOF marker: ${hasEOF}`);
    
    // Check for page objects
    const pageMatches = pdfText.match(/\/Type\s*\/Page[^s]/g) || [];
    const pageCount = pageMatches.length;
    console.log(`✓ Page objects found: ${pageCount}`);
    
    // Check for Pages tree
    const hasPagesTree = pdfText.includes('/Type /Pages');
    console.log(`✓ Pages tree: ${hasPagesTree}`);
    
    // Check for Catalog
    const hasCatalog = pdfText.includes('/Type /Catalog');
    console.log(`✓ Document catalog: ${hasCatalog}`);
    
    // Look for potential corruption indicators
    const corruptionIndicators = [];
    
    if (!hasValidHeader) corruptionIndicators.push('Missing or invalid PDF header');
    if (!hasXrefTable) corruptionIndicators.push('Missing cross-reference table');
    if (!hasTrailer) corruptionIndicators.push('Missing trailer');
    if (!hasEOF) corruptionIndicators.push('Missing EOF marker');
    if (pageCount === 0) corruptionIndicators.push('No page objects found');
    if (!hasPagesTree) corruptionIndicators.push('Missing Pages tree');
    if (!hasCatalog) corruptionIndicators.push('Missing document catalog');
    
    // Check for linearization (web-optimized)
    const isLinearized = pdfText.includes('/Linearized');
    console.log(`✓ Linearized (web-optimized): ${isLinearized}`);
    
    // Check for encryption
    const isEncrypted = pdfText.includes('/Encrypt');
    console.log(`✓ Encrypted: ${isEncrypted}`);
    
    // Summary
    console.log('\n=== DIAGNOSIS ===');
    if (corruptionIndicators.length === 0) {
      console.log('✅ PDF structure appears valid');
      console.log(`   Found ${pageCount} page objects`);
      
      if (pageCount === 0) {
        console.log('⚠️  WARNING: No page objects detected');
        console.log('   This could cause "document has no pages" error');
        console.log('   Possible causes:');
        console.log('   - PDF contains only images without proper page structure');
        console.log('   - Scanned PDF without OCR layer');
        console.log('   - Corrupted page tree');
      }
    } else {
      console.log('❌ PDF structure issues detected:');
      corruptionIndicators.forEach(issue => {
        console.log(`   - ${issue}`);
      });
    }
    
    // Recommendations
    console.log('\n=== RECOMMENDATIONS ===');
    if (pageCount === 0) {
      console.log('• Try re-saving the PDF with Adobe Acrobat or similar tool');
      console.log('• Check if PDF is actually a scanned image without page structure');
      console.log('• Use PDF repair tools like PDFtk or qpdf');
      console.log('• Consider converting to images first, then processing with vision API');
    }
    
    if (isEncrypted) {
      console.log('• Remove password protection before API processing');
    }
    
    if (buffer.length > 20 * 1024 * 1024) {
      console.log('• File is larger than 20MB - consider using File API instead of direct upload');
    }
    
    return {
      isValid: corruptionIndicators.length === 0,
      pageCount,
      issues: corruptionIndicators,
      fileSize: buffer.length,
      isEncrypted,
      isLinearized
    };
    
  } catch (error) {
    console.error('❌ PDF validation failed:', error.message);
    return {
      isValid: false,
      error: error.message
    };
  }
}

// Test with the RFSA PDF
const pdfPath = './example-docs/RFSA bank statement July 2025_compressed.pdf';

if (fs.existsSync(pdfPath)) {
  validatePDFStructure(pdfPath);
} else {
  console.log('PDF file not found. Please check the path.');
  console.log('Expected: ./example-docs/RFSA bank statement July 2025_compressed.pdf');
}
