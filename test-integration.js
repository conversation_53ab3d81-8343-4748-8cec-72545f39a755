// Integration test for FastAPI backend and frontend
const fs = require('fs');
const path = require('path');

async function testBackendIntegration() {
  console.log('🧪 Testing FastAPI Backend Integration...\n');

  try {
    // Test 1: Health check
    console.log('1. Testing health check endpoint...');
    const healthResponse = await fetch('http://localhost:8000/health');
    const healthData = await healthResponse.json();
    console.log('✅ Health check:', healthData.status);

    // Test 2: Root endpoint
    console.log('\n2. Testing root endpoint...');
    const rootResponse = await fetch('http://localhost:8000/');
    const rootData = await rootResponse.json();
    console.log('✅ Root endpoint:', rootData.message);

    // Test 3: Create test files for processing
    console.log('\n3. Creating test files...');
    
    // Create a simple test CSV file
    const csvContent = `Date,Description,Reference,Amount,Type
2025-01-15,Salary Payment,SAL001,5000.00,Credit
2025-01-16,Office Rent,RENT001,-1200.00,Debit
2025-01-17,Client Payment,CLI001,2500.00,Credit`;
    
    const csvPath = path.join(__dirname, 'test-ledger.csv');
    fs.writeFileSync(csvPath, csvContent);

    // Create a simple test PDF content (we'll use a text file for testing)
    const pdfContent = `BANK STATEMENT
Account Number: ****1234
Statement Period: 01/01/2025 to 01/31/2025
Opening Balance: $10,000.00

Date        Description              Amount    Reference
01/15/2025  ACH CREDIT PAYROLL      $5,000.00  REF123456
01/16/2025  CHECK #1001            -$1,200.50  CHK1001
01/17/2025  WIRE TRANSFER IN        $2,500.00  WIRE789

Closing Balance: $16,299.50`;
    
    const txtPath = path.join(__dirname, 'test-statement.txt');
    fs.writeFileSync(txtPath, pdfContent);

    console.log('✅ Test files created');

    // Test 4: Test file processing endpoint
    console.log('\n4. Testing file processing...');
    
    const formData = new FormData();
    
    // For testing, we'll use the text file as a "PDF"
    const txtFile = new File([pdfContent], 'test-statement.pdf', { type: 'application/pdf' });
    const csvFile = new File([csvContent], 'test-ledger.csv', { type: 'text/csv' });
    
    formData.append('bank_statement', txtFile);
    formData.append('ledger', csvFile);

    const processResponse = await fetch('http://localhost:8000/api/process-files', {
      method: 'POST',
      body: formData,
    });

    if (processResponse.ok) {
      const processData = await processResponse.json();
      console.log('✅ File processing successful');
      console.log(`   - Bank transactions: ${processData.summary.totalBankTransactions}`);
      console.log(`   - Ledger transactions: ${processData.summary.totalLedgerTransactions}`);
      console.log(`   - Matches found: ${processData.summary.matchedTransactions}`);
      console.log(`   - Discrepancies: ${processData.summary.discrepancies}`);
      console.log(`   - Processing time: ${processData.summary.processingTime || 'N/A'}ms`);
    } else {
      const errorData = await processResponse.json();
      console.log('❌ File processing failed:', errorData);
    }

    // Cleanup test files
    fs.unlinkSync(csvPath);
    fs.unlinkSync(txtPath);
    console.log('✅ Test files cleaned up');

    console.log('\n🎉 Backend integration test completed successfully!');
    
  } catch (error) {
    console.error('❌ Integration test failed:', error.message);
    process.exit(1);
  }
}

// Run the test
testBackendIntegration();
