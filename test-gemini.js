const { GoogleGenerativeAI } = require('@google/generative-ai');
const fs = require('fs');

async function testGeminiAPI() {
  try {
    console.log('Testing Gemini API connection...');
    
    // Read API key from .env.local file
    let apiKey;
    try {
      const envContent = fs.readFileSync('.env.local', 'utf8');
      const match = envContent.match(/GOOGLE_GENERATIVE_AI_API_KEY=(.+)/);
      apiKey = match ? match[1].trim() : null;
    } catch (err) {
      console.error('Could not read .env.local file:', err.message);
    }
    console.log('API Key exists:', !!apiKey);
    console.log('API Key length:', apiKey ? apiKey.length : 0);
    
    if (!apiKey) {
      throw new Error('GOOGLE_GENERATIVE_AI_API_KEY not found in environment');
    }
    
    const genAI = new GoogleGenerativeAI(apiKey);
    
    // Test 1: Simple text generation with gemini-2.5-flash
    console.log('\n=== Test 1: Simple text generation ===');
    const model = genAI.getGenerativeModel({ model: 'gemini-2.5-flash' });
    
    const prompt = "Say hello in one word";
    console.log('Sending prompt:', prompt);
    
    const result = await model.generateContent(prompt);
    const response = await result.response;
    const text = response.text();
    
    console.log('✅ Success! Response:', text);
    
    // Test 2: Check if PDF processing works with actual RFSA PDF
    console.log('\n=== Test 2: Real PDF processing ===');
    
    // Load the actual RFSA PDF
    const pdfPath = './example-docs/RFSA bank statement July 2025_compressed.pdf';
    let testBase64;
    
    try {
      const pdfBuffer = fs.readFileSync(pdfPath);
      testBase64 = pdfBuffer.toString('base64');
      console.log(`Loaded PDF: ${(pdfBuffer.length / 1024 / 1024).toFixed(2)}MB`);
    } catch (err) {
      console.log('Could not load RFSA PDF, using test data');
      testBase64 = Buffer.from('test pdf content').toString('base64');
    }
    
    const pdfPrompt = "Analyze this document and return 'PDF_TEST_SUCCESS'";
    
    try {
      const pdfResult = await model.generateContent([
        {
          inlineData: {
            mimeType: 'application/pdf',
            data: testBase64
          }
        },
        pdfPrompt
      ]);
      
      const pdfResponse = await pdfResult.response;
      const pdfText = pdfResponse.text();
      console.log('✅ PDF API Success! Response:', pdfText);
      
    } catch (pdfError) {
      console.log('❌ PDF API Error:', pdfError.message);
      
      // Test if it's a model capability issue
      if (pdfError.message.includes('does not support')) {
        console.log('🔍 Trying with different model...');
        
        // Try with a different model that definitely supports multimodal
        const multimodalModel = genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });
        
        try {
          const fallbackResult = await multimodalModel.generateContent([
            {
              inlineData: {
                mimeType: 'application/pdf',
                data: testBase64
              }
            },
            pdfPrompt
          ]);
          
          const fallbackResponse = await fallbackResult.response;
          const fallbackText = fallbackResponse.text();
          console.log('✅ Fallback model success:', fallbackText);
          
        } catch (fallbackError) {
          console.log('❌ Fallback model also failed:', fallbackError.message);
        }
      }
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Full error:', error);
    
    // Additional debugging
    if (error.message.includes('fetch failed')) {
      console.log('\n🔍 Network debugging:');
      console.log('- Check internet connection');
      console.log('- Check if corporate firewall blocks generativelanguage.googleapis.com');
      console.log('- Try: curl -I https://generativelanguage.googleapis.com');
    }
    
    if (error.message.includes('API key')) {
      console.log('\n🔍 API Key debugging:');
      console.log('- Verify API key is correct');
      console.log('- Check if Gemini API is enabled in Google Cloud Console');
      console.log('- Verify billing is set up');
    }
  }
}

// Run the test
testGeminiAPI();
