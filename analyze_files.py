#!/usr/bin/env python3
"""
Script to analyze the actual bank statement PDF and Excel ledger files
to understand their structure and develop better matching algorithms.
"""

import pandas as pd
import PyPDF2
import re
from datetime import datetime
import json
from pathlib import Path

def analyze_excel_ledger(file_path):
    """Analyze the Excel ledger file structure and extract transactions."""
    print(f"Analyzing Excel file: {file_path}")
    
    try:
        # Try reading with different engines and sheet options
        xl_file = pd.ExcelFile(file_path)
        print(f"Available sheets: {xl_file.sheet_names}")
        
        # Read the first sheet
        df = pd.read_excel(file_path, sheet_name=0)
        print(f"DataFrame shape: {df.shape}")
        print(f"Column names: {list(df.columns)}")
        
        # Display first few rows
        print("\nFirst 10 rows:")
        print(df.head(10).to_string())
        
        # Look for transactions that might match the bank statement
        # Search for amounts around 24,000
        amount_matches = df[df.apply(lambda row: any(
            str(val).replace(',', '').replace('-', '').replace(' ', '') == '24000' or
            str(val).replace(',', '').replace('-', '').replace(' ', '') == '24000.00'
            for val in row if pd.notna(val)
        ), axis=1)]
        
        print(f"\nRows containing amount 24,000:")
        print(amount_matches.to_string())
        
        # Look for July 2025 dates
        july_matches = df[df.apply(lambda row: any(
            '7/1/25' in str(val) or '01/07/2025' in str(val) or 'Jul' in str(val)
            for val in row if pd.notna(val)
        ), axis=1)]
        
        print(f"\nRows containing July dates:")
        print(july_matches.head(10).to_string())
        
        return df
        
    except Exception as e:
        print(f"Error reading Excel file: {e}")
        return None

def analyze_pdf_bank_statement(file_path):
    """Analyze the PDF bank statement structure."""
    print(f"\nAnalyzing PDF file: {file_path}")
    
    try:
        with open(file_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            print(f"Number of pages: {len(pdf_reader.pages)}")
            
            all_text = ""
            for page_num, page in enumerate(pdf_reader.pages):
                text = page.extract_text()
                all_text += text
                print(f"\nPage {page_num + 1} text:")
                print(text[:1000] + "..." if len(text) > 1000 else text)
            
            # Look for transaction patterns
            # Pattern for the transaction we saw: CHQ NO ********, amount -24,000.00
            transaction_patterns = [
                r'CHQ NO \d+.*?-?24,?000\.00',
                r'01 07 2025.*?CHQ NO.*?********',
                r'TT25182CV3YG',
                r'-24,?000\.00'
            ]
            
            print("\nSearching for transaction patterns:")
            for pattern in transaction_patterns:
                matches = re.findall(pattern, all_text, re.IGNORECASE)
                if matches:
                    print(f"Pattern '{pattern}' found: {matches}")
            
            # Extract all lines that look like transactions
            lines = all_text.split('\n')
            transaction_lines = []
            for line in lines:
                # Look for lines with dates, amounts, and references
                if re.search(r'\d{2}\s\d{2}\s\d{4}', line) and re.search(r'-?\d+,?\d*\.?\d*', line):
                    transaction_lines.append(line.strip())
            
            print(f"\nPotential transaction lines ({len(transaction_lines)}):")
            for line in transaction_lines[:10]:  # Show first 10
                print(f"  {line}")
            
            return all_text
            
    except Exception as e:
        print(f"Error reading PDF file: {e}")
        return None

def develop_matching_strategy(ledger_df, bank_text):
    """Develop a comprehensive matching strategy based on actual data."""
    print("\n" + "="*50)
    print("DEVELOPING MATCHING STRATEGY")
    print("="*50)
    
    strategy = {
        "primary_matching_fields": [],
        "secondary_matching_fields": [],
        "fuzzy_matching_rules": [],
        "date_normalization": [],
        "amount_normalization": [],
        "reference_mapping": []
    }
    
    # Analyze amount formats
    print("\n1. AMOUNT ANALYSIS:")
    if ledger_df is not None:
        # Find numeric columns that might contain amounts
        numeric_cols = ledger_df.select_dtypes(include=['float64', 'int64']).columns
        print(f"Numeric columns in ledger: {list(numeric_cols)}")
        
        # Look for the 24,000 amount specifically
        for col in ledger_df.columns:
            col_data = ledger_df[col].astype(str)
            if col_data.str.contains('24000|24,000', na=False).any():
                print(f"Column '{col}' contains 24,000 amount")
                matching_rows = ledger_df[col_data.str.contains('24000|24,000', na=False)]
                print(f"Matching rows:\n{matching_rows.to_string()}")
    
    # Analyze date formats
    print("\n2. DATE ANALYSIS:")
    print("Bank statement uses: 01 07 2025 (DD MM YYYY)")
    print("Ledger likely uses: 7/1/25 (M/D/YY)")
    
    strategy["date_normalization"] = [
        "Convert 'DD MM YYYY' to datetime",
        "Convert 'M/D/YY' to datetime", 
        "Convert 'MM/DD/YYYY' to datetime",
        "Handle various separators: /, -, space"
    ]
    
    # Analyze reference patterns
    print("\n3. REFERENCE ANALYSIS:")
    print("Bank statement references: CHQ NO ********, TT25182CV3YG")
    print("Ledger references: FIN/2011/25, Pv-353074")
    
    strategy["reference_mapping"] = [
        "Extract check numbers from bank descriptions",
        "Map internal voucher numbers to bank references",
        "Use transaction sequences and amounts for matching",
        "Consider date proximity for ambiguous matches"
    ]
    
    # Primary matching strategy
    strategy["primary_matching_fields"] = [
        "amount_exact_match",
        "date_within_3_days", 
        "transaction_type_match"
    ]
    
    strategy["secondary_matching_fields"] = [
        "amount_fuzzy_match_99_percent",
        "date_within_7_days",
        "description_similarity"
    ]
    
    strategy["fuzzy_matching_rules"] = [
        "Amount tolerance: ±0.01 for rounding differences",
        "Date tolerance: ±3 days for processing delays",
        "Description matching: Extract key identifiers (check numbers, etc.)",
        "Reference cross-mapping: Build lookup table for internal vs bank refs"
    ]
    
    return strategy

def main():
    """Main analysis function."""
    base_path = Path("/Users/<USER>/Desktop/projects/accounting-ai/bank-reconciliation-ai/example-docs")
    
    excel_file = base_path / "RFSA_July_2025_CBE_bank_statement.xlsx"
    pdf_file = base_path / "RFSA bank statement July 2025_compressed.pdf"
    
    print("BANK RECONCILIATION FILE ANALYSIS")
    print("="*50)
    
    # Analyze Excel ledger
    ledger_df = analyze_excel_ledger(excel_file)
    
    # Analyze PDF bank statement  
    bank_text = analyze_pdf_bank_statement(pdf_file)
    
    # Develop matching strategy
    strategy = develop_matching_strategy(ledger_df, bank_text)
    
    # Save strategy to file
    strategy_file = base_path.parent / "improved_matching_strategy.json"
    with open(strategy_file, 'w') as f:
        json.dump(strategy, f, indent=2)
    
    print(f"\nMatching strategy saved to: {strategy_file}")
    print("\nSTRATEGY SUMMARY:")
    print(json.dumps(strategy, indent=2))

if __name__ == "__main__":
    main()
