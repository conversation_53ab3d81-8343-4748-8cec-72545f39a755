{"totalMatches": 22, "matchRate": 44, "averageConfidence": 0.****************, "matchTypes": {"exact": 17, "fuzzy": 2, "fin_reference": 3}, "matches": [{"bankTransaction": {"id": "bank_71", "date": "2025-09-08T00:00:00Z", "description": "FIN 2061 2025", "reference": "FT251842J8HH", "amount": 15897.6, "type": "debit", "source": "bank"}, "ledgerTransaction": {"id": "ledger_64", "date": "2025-07-01T00:00:00Z", "description": "Bank CBE-RFSA *************", "reference": "Fin/2061/25", "amount": 15897.6, "type": "credit", "source": "ledger"}, "confidence": 0.85, "matchType": "fin_reference", "reasons": ["Exact amount match", "FIN reference match: FIN/2061", "Transaction type consistent"]}, {"bankTransaction": {"id": "bank_110", "date": "2025-09-08T00:00:00Z", "description": "FIN 2047 2025", "reference": "FT25185N839K", "amount": 225450, "type": "debit", "source": "bank"}, "ledgerTransaction": {"id": "ledger_63", "date": "2025-07-01T00:00:00Z", "description": "Bank CBE-RFSA *************", "reference": "Fin/2047/25", "amount": 225450, "type": "credit", "source": "ledger"}, "confidence": 0.85, "matchType": "fin_reference", "reasons": ["Exact amount match", "FIN reference match: FIN/2047", "Transaction type consistent"]}, {"bankTransaction": {"id": "bank_112", "date": "2025-09-08T00:00:00Z", "description": "FIN 2050 2025", "reference": "FT2518579XW6", "amount": 80000, "type": "debit", "source": "bank"}, "ledgerTransaction": {"id": "ledger_65", "date": "2025-07-01T00:00:00Z", "description": "Bank CBE-RFSA *************", "reference": "Fin/2050/25", "amount": 80000, "type": "credit", "source": "ledger"}, "confidence": 0.85, "matchType": "fin_reference", "reasons": ["Exact amount match", "FIN reference match: FIN/2050", "Transaction type consistent"]}, {"bankTransaction": {"id": "bank_2", "date": "2025-07-01T00:00:00Z", "description": "********", "reference": "TT25182CV3YG", "amount": 24000, "type": "debit", "source": "bank"}, "ledgerTransaction": {"id": "ledger_247", "date": "2025-07-17T00:00:00Z", "description": "Bank CBE-RFSA *************", "reference": "CD********", "amount": 24000, "type": "credit", "source": "ledger"}, "confidence": 0.****************, "matchType": "exact", "reasons": ["Exact amount match", "Check number match: ********", "Transaction type consistent"]}, {"bankTransaction": {"id": "bank_155", "date": "2025-09-08T00:00:00Z", "description": "********", "reference": "TT25189RRZ9Q", "amount": 6558, "type": "debit", "source": "bank"}, "ledgerTransaction": {"id": "ledger_207", "date": "2025-07-16T00:00:00Z", "description": "Bank CBE-RFSA *************", "reference": "CD-********", "amount": 6558, "type": "credit", "source": "ledger"}, "confidence": 0.****************, "matchType": "exact", "reasons": ["Exact amount match", "Check number match: ********", "Transaction type consistent"]}, {"bankTransaction": {"id": "bank_57", "date": "2025-09-08T00:00:00Z", "description": "********", "reference": "TT251847DLSD", "amount": 40000, "type": "debit", "source": "bank"}, "ledgerTransaction": {"id": "ledger_10", "date": "2025-07-01T00:00:00Z", "description": "Bank CBE-RFSA *************", "reference": "CD********", "amount": 40000, "type": "credit", "source": "ledger"}, "confidence": 0.****************, "matchType": "exact", "reasons": ["Exact amount match", "Check number match: ********", "Transaction type consistent"]}, {"bankTransaction": {"id": "bank_137", "date": "2025-09-08T00:00:00Z", "description": "********", "reference": "TT25185B9TRS", "amount": 16000, "type": "debit", "source": "bank"}, "ledgerTransaction": {"id": "ledger_9", "date": "2025-07-01T00:00:00Z", "description": "Bank CBE-RFSA *************", "reference": "CD********", "amount": 16000, "type": "credit", "source": "ledger"}, "confidence": 0.****************, "matchType": "exact", "reasons": ["Exact amount match", "Check number match: ********", "Transaction type consistent"]}, {"bankTransaction": {"id": "bank_75", "date": "2025-09-08T00:00:00Z", "description": "********", "reference": "TT251842CBWK", "amount": 27000, "type": "debit", "source": "bank"}, "ledgerTransaction": {"id": "ledger_192", "date": "2025-07-16T00:00:00Z", "description": "Bank CBE-RFSA *************", "reference": "CD********", "amount": 27000, "type": "credit", "source": "ledger"}, "confidence": 0.****************, "matchType": "exact", "reasons": ["Exact amount match", "Check number match: ********", "Transaction type consistent"]}, {"bankTransaction": {"id": "bank_76", "date": "2025-09-08T00:00:00Z", "description": "********", "reference": "TT251845GR7C", "amount": 9363.23, "type": "debit", "source": "bank"}, "ledgerTransaction": {"id": "ledger_24", "date": "2025-07-01T00:00:00Z", "description": "Bank CBE-RFSA *************", "reference": "CD-********", "amount": 9363.23, "type": "credit", "source": "ledger"}, "confidence": 0.****************, "matchType": "exact", "reasons": ["Exact amount match", "Check number match: ********", "Transaction type consistent"]}, {"bankTransaction": {"id": "bank_87", "date": "2025-09-08T00:00:00Z", "description": "********", "reference": "TT25184LNM9K", "amount": 4009.07, "type": "debit", "source": "bank"}, "ledgerTransaction": {"id": "ledger_198", "date": "2025-07-16T00:00:00Z", "description": "Bank CBE-RFSA *************", "reference": "CD********", "amount": 4009.07, "type": "credit", "source": "ledger"}, "confidence": 0.****************, "matchType": "exact", "reasons": ["Exact amount match", "Check number match: ********", "Transaction type consistent"]}]}