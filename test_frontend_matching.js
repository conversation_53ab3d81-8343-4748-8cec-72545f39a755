// Test script to verify RFSA-specific matching improvements
const { matchTransactions } = require('./src/lib/transaction-matcher.ts');

// Mock RFSA transactions based on our analysis
const bankTransactions = [
  {
    id: 'bank_1',
    date: '2025-07-01T00:00:00Z',
    description: 'CHQ NO ******** PAYMENT TO VENDOR',
    reference: 'CHQ NO ********',
    amount: -24000.0,
    type: 'debit',
    source: 'bank'
  }
];

const ledgerTransactions = [
  {
    id: 'ledger_1',
    date: '2025-07-17T00:00:00Z',
    description: 'Bank CBE-RFSA ************* CD******** CDJ PV-353206',
    reference: 'CD********',
    amount: 24000.0,
    type: 'credit',
    source: 'ledger'
  }
];

console.log('Testing RFSA transaction matching...');
console.log('Bank Transaction:', bankTransactions[0]);
console.log('Ledger Transaction:', ledgerTransactions[0]);

const result = matchTransactions(bankTransactions, ledgerTransactions);

console.log('\nMatching Results:');
console.log('Matches found:', result.matches.length);
console.log('Discrepancies:', result.discrepancies.length);

if (result.matches.length > 0) {
  const match = result.matches[0];
  console.log('\nMatch Details:');
  console.log('Confidence:', match.confidence);
  console.log('Match Type:', match.matchType);
  console.log('Criteria:', match.matchCriteria);
} else {
  console.log('\nNo matches found - this indicates an issue with the matching logic');
}

console.log('\nStats:', result.matchingStats);
