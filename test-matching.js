import fs from 'fs';
import FormData from 'form-data';
import fetch from 'node-fetch';

async function testMatchingAlgorithm() {
  try {
    console.log('🧪 Testing Enhanced Matching Algorithm with Real RFSA Data');
    console.log('=' .repeat(60));

    // Read the real RFSA files from example-docs folder
    const pdfPath = './example-docs/RFSA bank statement July 2025_compressed.pdf';
    const excelPath = './example-docs/RFSA_July_2025_CBE_bank_statement.xlsx';

    if (!fs.existsSync(pdfPath)) {
      console.error('❌ PDF file not found:', pdfPath);
      return;
    }

    if (!fs.existsSync(excelPath)) {
      console.error('❌ Excel file not found:', excelPath);
      return;
    }

    console.log('📄 Found PDF file:', pdfPath);
    console.log('📊 Found Excel file:', excelPath);

    // Create form data
    const formData = new FormData();
    formData.append('bankStatement', fs.createReadStream(pdfPath));
    formData.append('ledger', fs.createReadStream(excelPath));

    console.log('\n🚀 Sending files to API for processing...');

    // Send request to API
    const response = await fetch('http://localhost:3000/api/process-files', {
      method: 'POST',
      body: formData,
      headers: formData.getHeaders()
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ API Error:', response.status, errorText);
      return;
    }

    const result = await response.json();
    
    if (!result.success) {
      console.error('❌ Processing failed:', result.error);
      return;
    }

    console.log('\n✅ Processing completed successfully!');
    console.log('📊 Results Summary:');
    console.log('=' .repeat(40));

    const data = result.data;
    
    // Summary statistics
    console.log(`📈 Total Bank Transactions: ${data.summary.totalBankTransactions}`);
    console.log(`📈 Total Ledger Transactions: ${data.summary.totalLedgerTransactions}`);
    console.log(`✅ Matches Found: ${data.summary.totalMatches}`);
    console.log(`❌ Discrepancies: ${data.summary.totalDiscrepancies}`);
    console.log(`🎯 Match Rate: ${((data.summary.totalMatches / data.summary.totalBankTransactions) * 100).toFixed(1)}%`);
    
    if (data.summary.averageConfidence) {
      console.log(`📊 Average Confidence: ${(data.summary.averageConfidence * 100).toFixed(1)}%`);
    }

    // Balance analysis
    console.log('\n💰 Balance Analysis:');
    console.log(`Bank Balance: ${data.summary.bankBalance?.toLocaleString() || 'N/A'}`);
    console.log(`Ledger Balance: ${data.summary.ledgerBalance?.toLocaleString() || 'N/A'}`);
    console.log(`Balance Difference: ${data.summary.balanceDifference?.toLocaleString() || 'N/A'}`);

    // Match confidence distribution
    if (data.matches && data.matches.length > 0) {
      console.log('\n🎯 Match Confidence Distribution:');
      const confidenceRanges = {
        'High (90-100%)': 0,
        'Good (80-89%)': 0,
        'Medium (70-79%)': 0,
        'Low (60-69%)': 0,
        'Very Low (<60%)': 0
      };

      data.matches.forEach(match => {
        const conf = match.confidence * 100;
        if (conf >= 90) confidenceRanges['High (90-100%)']++;
        else if (conf >= 80) confidenceRanges['Good (80-89%)']++;
        else if (conf >= 70) confidenceRanges['Medium (70-79%)']++;
        else if (conf >= 60) confidenceRanges['Low (60-69%)']++;
        else confidenceRanges['Very Low (<60%)']++;
      });

      Object.entries(confidenceRanges).forEach(([range, count]) => {
        if (count > 0) {
          console.log(`  ${range}: ${count} matches`);
        }
      });
    }

    // Sample matches
    if (data.matches && data.matches.length > 0) {
      console.log('\n🔍 Sample High-Confidence Matches:');
      const highConfMatches = data.matches
        .filter(m => m.confidence >= 0.8)
        .slice(0, 3);

      highConfMatches.forEach((match, i) => {
        console.log(`\n  Match ${i + 1} (${(match.confidence * 100).toFixed(1)}% confidence):`);
        console.log(`    Bank: ${match.bankTransaction.amount} | ${match.bankTransaction.description}`);
        console.log(`    Ledger: ${match.ledgerTransaction.amount} | ${match.ledgerTransaction.description}`);
        console.log(`    Match Type: ${match.matchType}`);
      });
    }

    // Validation results
    if (data.validation) {
      console.log('\n🔍 Validation Results:');
      console.log(`Potential Missed Matches: ${data.validation.potentialMissedMatches || 0}`);
      console.log(`High-Value Unmatched: ${data.validation.highValueUnmatched || 0}`);
      
      if (data.validation.recommendations && data.validation.recommendations.length > 0) {
        console.log('\n💡 Recommendations:');
        data.validation.recommendations.forEach((rec, i) => {
          console.log(`  ${i + 1}. ${rec}`);
        });
      }
    }

    // AI explanation status
    if (data.discrepancies && data.discrepancies.length > 0) {
      const withAI = data.discrepancies.filter(d => d.aiExplanation && !d.aiExplanation.includes('placeholder')).length;
      console.log(`\n🤖 AI Explanations: ${withAI}/${data.discrepancies.length} generated`);
    }

    console.log('\n✅ Test completed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack:', error.stack);
  }
}

// Run the test
testMatchingAlgorithm();
