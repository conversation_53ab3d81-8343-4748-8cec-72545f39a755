# Bank Reconciliation AI MVP

An AI-powered bank reconciliation tool that automates the comparison of bank statements (PDF) with accounting ledgers (Excel/CSV), identifies discrepancies, and provides actionable insights using Google Gemini AI.

## Features

- 🏦 **Smart File Processing**: Upload PDF bank statements and Excel/CSV ledgers
- 🤖 **AI-Powered Analysis**: Google Gemini integration for intelligent discrepancy explanations
- 🔍 **Automated Matching**: Advanced transaction matching based on reference numbers, amounts, and dates
- 📊 **Interactive Dashboard**: Real-time results with detailed reconciliation insights
- 🎯 **Discrepancy Detection**: Identifies missing transactions, amount mismatches, and timing differences
- 📈 **Balance Comparison**: Side-by-side bank vs ledger balance analysis
- 🎨 **Modern UI**: Built with shadcn/ui components and Tailwind CSS
- 🔒 **Multiple Processing Options**: Choose between Next.js API, FastAPI backend, or E2B secure sandbox
- 📄 **Enhanced PDF Parsing**: Mistral OCR integration with table-based extraction
- 🛡️ **Secure Processing**: E2B sandboxed execution for maximum security

## Processing Options

### 1. Next.js API (Default)
- **Best for**: Files under 7.5MB
- **Features**: Fast processing, real-time progress updates
- **Processing Time**: 30-60 seconds

### 2. FastAPI Backend
- **Best for**: Large files or enhanced PDF parsing
- **Features**: Mistral OCR integration, advanced table extraction
- **Processing Time**: 60-120 seconds

### 3. E2B Secure Sandbox (Beta)
- **Best for**: Maximum security requirements
- **Features**: Isolated execution, advanced analytics, secure processing
- **Processing Time**: 90-180 seconds

## Technology Stack

- **Frontend**: Next.js 15 with App Router, React, TypeScript
- **UI Library**: shadcn/ui with Tailwind CSS
- **State Management**: TanStack Query (React Query)
- **Validation**: Zod schemas with runtime type safety
- **AI Integration**: Vercel AI SDK + Google Gemini API
- **File Handling**: React Dropzone with comprehensive validation
- **Backend Options**: 
  - Next.js API Routes
  - FastAPI with Mistral OCR
  - E2B Secure Sandbox

## Getting Started

### Prerequisites

- Node.js 18+ 
- npm, yarn, or pnpm
- Google Gemini API key

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd bank-reconciliation-ai
```

2. Install dependencies:
```bash
npm install
```

3. Set up environment variables:
```bash
cp .env.local.example .env.local
```

4. Configure environment variables in `.env.local`:
```env
# Required
GOOGLE_GENERATIVE_AI_API_KEY=your_gemini_api_key_here

# Optional - FastAPI Backend
NEXT_PUBLIC_BACKEND_URL=http://localhost:8000
MISTRAL_API_KEY=your_mistral_api_key_here

# Optional - E2B Secure Sandbox
NEXT_PUBLIC_E2B_API_KEY=your_e2b_api_key_here
NEXT_PUBLIC_USE_E2B=false

# Optional - Supabase (for data persistence)
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
```

5. Run the development server:
```bash
npm run dev
```

6. Open [http://localhost:3002](http://localhost:3002) in your browser.

## Usage

1. **Upload Files**: Drag and drop or select your PDF bank statement and Excel/CSV ledger file
2. **Processing**: The AI analyzes and matches transactions automatically
3. **Review Results**: View the interactive dashboard with:
   - Summary statistics and match rates
   - Balance comparison between bank and ledger
   - AI-explained discrepancies with suggested actions
   - Detailed matched transactions table
4. **Export**: Generate reports for further analysis (coming soon)

## Project Structure

```
src/
├── app/
│   ├── api/process-files/     # API route for file processing
│   ├── layout.tsx             # Root layout with providers
│   └── page.tsx               # Main application page
├── components/
│   ├── ui/                    # shadcn/ui components
│   ├── file-upload.tsx        # File upload component
│   └── results-dashboard.tsx  # Results visualization
├── hooks/
│   └── use-file-processing.ts # React Query hook for API calls
└── lib/
    ├── providers.tsx          # React Query and toast providers
    └── schemas.ts             # Zod validation schemas
```

## API Endpoints

- `POST /api/process-files-stream` - Next.js API for files under 7.5MB (streaming response)
- `POST /api/process-files-e2b` - E2B secure sandbox processing (streaming response)
- `POST /api/process-files` - FastAPI backend for large files and enhanced processing

## Backend Selection Logic

The application automatically selects the appropriate backend based on:

1. **File Size**: Files larger than 7.5MB are sent to FastAPI backend
2. **E2B Configuration**: If `NEXT_PUBLIC_USE_E2B=true`, E2B sandbox is available
3. **User Choice**: Users can manually select their preferred processing method

## E2B Integration

The E2B (Execution Environment for AI) integration provides secure sandboxed processing with:

- **Isolated Execution**: All processing happens in a secure sandbox
- **Advanced Analytics**: Enhanced transaction matching and analysis
- **Security**: Maximum security for sensitive financial data
- **Flexibility**: Custom processing scripts and visualizations

### Setting up E2B

1. Sign up for an E2B account at [e2b.dev](https://e2b.dev)
2. Get your API key from the E2B dashboard
3. Add to your `.env.local`:
   ```env
   NEXT_PUBLIC_E2B_API_KEY=your_e2b_api_key_here
   NEXT_PUBLIC_USE_E2B=true
   ```
4. Test the integration using the built-in test component

## Development

The application uses:
- **React Query** for server state management and caching
- **Zod** for runtime type validation and schema enforcement
- **Sonner** for toast notifications
- **Lucide React** for icons
- **Google Gemini** for AI-powered explanations

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

MIT License - see LICENSE file for details.
# recon-app
