import fs from 'fs';
import FormData from 'form-data';
import fetch from 'node-fetch';

async function testRealAPI() {
  try {
    console.log('🧪 Testing Bank Reconciliation API with real files...');
    
    // Create form data with the actual files
    const form = new FormData();
    
    // Add the PDF bank statement
    const pdfPath = './example-docs/RFSA bank statement July 2025_compressed.pdf';
    const excelPath = './example-docs/RFSA_July_2025_CBE_bank_statement.xlsx';
    
    if (!fs.existsSync(pdfPath)) {
      console.error('❌ PDF file not found:', pdfPath);
      return;
    }
    
    if (!fs.existsSync(excelPath)) {
      console.error('❌ Excel file not found:', excelPath);
      return;
    }
    
    form.append('bankStatement', fs.createReadStream(pdfPath));
    form.append('ledger', fs.createReadStream(excelPath));
    
    console.log('📤 Sending files to API...');
    const startTime = Date.now();
    
    const response = await fetch('http://localhost:3000/api/process-files', {
      method: 'POST',
      body: form,
      headers: form.getHeaders()
    });
    
    const processingTime = Date.now() - startTime;
    console.log(`⏱️  Processing time: ${processingTime}ms`);
    
    if (!response.ok) {
      console.error('❌ API Error:', response.status, response.statusText);
      const errorText = await response.text();
      console.error('Error details:', errorText);
      return;
    }
    
    const result = await response.json();
    
    if (result.success) {
      console.log('✅ API call successful!');
      console.log('📊 Results:');
      console.log(`   - Bank transactions: ${result.data.summary.totalBankTransactions}`);
      console.log(`   - Ledger transactions: ${result.data.summary.totalLedgerTransactions}`);
      console.log(`   - Matches found: ${result.data.summary.matchedTransactions}`);
      console.log(`   - Discrepancies: ${result.data.summary.discrepancies}`);
      console.log(`   - Average confidence: ${(result.data.summary.matchingStats.averageConfidence * 100).toFixed(1)}%`);
      
      // Check if AI explanations are working
      const discrepanciesWithAI = result.data.discrepancies.filter(d => 
        d.aiExplanation && !d.aiExplanation.includes('manual review')
      );
      
      console.log(`🤖 AI explanations generated: ${discrepanciesWithAI.length}/${result.data.discrepancies.length}`);
      
      if (discrepanciesWithAI.length > 0) {
        console.log('✅ Gemini 2.0 Flash is working! Sample explanation:');
        console.log(`   "${discrepanciesWithAI[0].aiExplanation}"`);
      } else {
        console.log('⚠️  No AI explanations generated - check rate limits or API key');
      }
      
      // Save results for inspection
      fs.writeFileSync('./test-results.json', JSON.stringify(result, null, 2));
      console.log('💾 Results saved to test-results.json');
      
    } else {
      console.error('❌ API returned error:', result.error);
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testRealAPI();
