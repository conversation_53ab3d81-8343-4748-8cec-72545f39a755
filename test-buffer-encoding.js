const fs = require('fs');
const path = require('path');

// Test Buffer-based file encoding (Node.js compatible)
function testBufferEncoding() {
  console.log('Testing Buffer-based file encoding for Node.js...');
  
  try {
    // Read the PDF file
    const pdfPath = path.join(__dirname, 'example-docs', 'RFSA bank statement July 2025_compressed.pdf');
    const pdfBuffer = fs.readFileSync(pdfPath);
    
    console.log(`PDF file loaded: ${pdfBuffer.length} bytes`);
    
    // Test the Buffer encoding approach (same as our fix)
    const base64Content = pdfBuffer.toString('base64');
    console.log(`Base64 encoding successful: ${base64Content.length} characters`);
    
    // Verify the base64 is valid
    const decoded = Buffer.from(base64Content, 'base64');
    console.log(`Decoded buffer length: ${decoded.length} bytes`);
    console.log(`Original vs decoded match: ${pdfBuffer.length === decoded.length}`);
    
    // Test creating a File-like object with arrayBuffer method
    const fileObject = {
      arrayBuffer: () => {
        const arrayBuffer = new ArrayBuffer(pdfBuffer.length);
        const view = new Uint8Array(arrayBuffer);
        for (let i = 0; i < pdfBuffer.length; i++) {
          view[i] = pdfBuffer[i];
        }
        return Promise.resolve(arrayBuffer);
      },
      type: 'application/pdf',
      name: 'RFSA bank statement July 2025_compressed.pdf',
      size: pdfBuffer.length
    };
    
    // Test the arrayBuffer conversion
    fileObject.arrayBuffer().then(arrayBuffer => {
      const buffer = Buffer.from(arrayBuffer);
      const base64FromArrayBuffer = buffer.toString('base64');
      console.log(`ArrayBuffer to base64 successful: ${base64FromArrayBuffer.length} characters`);
      console.log(`Base64 content matches: ${base64Content === base64FromArrayBuffer}`);
      console.log('✅ Buffer encoding test completed successfully!');
    }).catch(error => {
      console.error('❌ ArrayBuffer test failed:', error);
    });
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testBufferEncoding();
