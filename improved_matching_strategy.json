{"primary_matching_fields": ["amount_exact_match", "date_within_3_days", "transaction_type_match"], "secondary_matching_fields": ["amount_fuzzy_match_99_percent", "date_within_7_days", "description_similarity"], "fuzzy_matching_rules": ["Amount tolerance: ±0.01 for rounding differences", "Date tolerance: ±3 days for processing delays", "Description matching: Extract key identifiers (check numbers, etc.)", "Reference cross-mapping: Build lookup table for internal vs bank refs"], "date_normalization": ["Convert 'DD MM YYYY' to datetime", "Convert 'M/D/YY' to datetime", "Convert 'MM/DD/YYYY' to datetime", "Handle various separators: /, -, space"], "amount_normalization": [], "reference_mapping": ["Extract check numbers from bank descriptions", "Map internal voucher numbers to bank references", "Use transaction sequences and amounts for matching", "Consider date proximity for ambiguous matches"]}